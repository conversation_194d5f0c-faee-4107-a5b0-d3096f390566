import type { 
  OrderWithLinesDto, 
  OrderLineResponseDto, 
  OrderPartResponseDto, 
  OrderBundleResponseDto, 
  OrderCraftResponseDto 
} from '@/services/types.gen'

// 使用API返回的完整订单类型作为基础
export interface Order extends OrderWithLinesDto {
  // 扩展订单类型，包含可能的关联数据
  order_parts?: OrderPartResponseDto[]
  order_bundles?: OrderBundleResponseDto[]
  // order_lines 和 order_crafts 已经在 OrderWithLinesDto 中定义
  // 修复 order_crafts 的 null 类型问题
  order_crafts?: OrderCraftResponseDto[]
}

// 订单行类型
export type OrderLine = OrderLineResponseDto

// 订单部件类型
export type OrderPart = OrderPartResponseDto

// 订单捆绑类型
export type OrderBundle = OrderBundleResponseDto

// 订单工艺类型
export type OrderCraft = OrderCraftResponseDto
