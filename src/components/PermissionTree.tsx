import { useState } from 'react'
import { ChevronDown, ChevronRight, Shield, ShieldCheck } from 'lucide-react'
import { Label } from "@/components/ui/label"
import type { PermissionResponseDto } from '@/services/types.gen'

interface PermissionNode extends PermissionResponseDto {
  children: PermissionNode[]
  checked: boolean
  indeterminate: boolean
}

interface PermissionTreeProps {
  permissions: PermissionResponseDto[]
  selectedIds: number[]
  onPermissionChange: (permissionIds: number[]) => void
  readOnly?: boolean
}

export function PermissionTree({ permissions, selectedIds, onPermissionChange, readOnly = false }: PermissionTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set())

  // Build tree structure from flat permissions array
  const buildTree = (permissions: PermissionResponseDto[]): PermissionNode[] => {
    const permissionMap = new Map<number, PermissionNode>()
    const rootNodes: PermissionNode[] = []

    // First pass: create all nodes
    permissions.forEach(permission => {
      permissionMap.set(permission.id, {
        ...permission,
        children: [],
        checked: selectedIds.includes(permission.id),
        indeterminate: false
      })
    })

    // Second pass: build tree structure
    permissions.forEach(permission => {
      const node = permissionMap.get(permission.id)!
      if (permission.parent_id && permissionMap.has(permission.parent_id)) {
        const parent = permissionMap.get(permission.parent_id)!
        parent.children.push(node)
      } else {
        rootNodes.push(node)
      }
    })

    // Third pass: update indeterminate states
    const updateIndeterminateState = (node: PermissionNode): { checked: boolean; indeterminate: boolean } => {
      if (node.children.length === 0) {
        return { checked: node.checked, indeterminate: false }
      }

      const childStates = node.children.map(child => updateIndeterminateState(child))
      const checkedChildren = childStates.filter(state => state.checked || state.indeterminate)
      const allChecked = childStates.length > 0 && childStates.every(state => state.checked)
      const someChecked = checkedChildren.length > 0

      node.checked = allChecked
      node.indeterminate = someChecked && !allChecked

      return { checked: node.checked, indeterminate: node.indeterminate }
    }

    rootNodes.forEach(updateIndeterminateState)
    return rootNodes
  }

  const treeData = buildTree(permissions)

  const toggleExpanded = (nodeId: number) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId)
      } else {
        newSet.add(nodeId)
      }
      return newSet
    })
  }

  const getAllDescendantIds = (node: PermissionNode): number[] => {
    const ids = [node.id]
    node.children.forEach(child => {
      ids.push(...getAllDescendantIds(child))
    })
    return ids
  }

  const handlePermissionToggle = (node: PermissionNode, checked: boolean) => {
    let newSelectedIds = [...selectedIds]
    const descendantIds = getAllDescendantIds(node)

    if (checked) {
      // Add all descendants
      descendantIds.forEach(id => {
        if (!newSelectedIds.includes(id)) {
          newSelectedIds.push(id)
        }
      })
    } else {
      // Remove all descendants
      newSelectedIds = newSelectedIds.filter(id => !descendantIds.includes(id))
    }

    onPermissionChange(newSelectedIds)
  }

  const renderNode = (node: PermissionNode, level: number = 0) => {
    const hasChildren = node.children.length > 0
    const isExpanded = expandedNodes.has(node.id)
    const paddingLeft = level * 20

    return (
      <div key={node.id} className="select-none">
        <div 
          className="flex items-center py-1 hover:bg-gray-50 rounded-sm"
          style={{ paddingLeft: `${paddingLeft}px` }}
        >
          {hasChildren ? (
            <button
              type="button"
              onClick={() => toggleExpanded(node.id)}
              className="flex items-center justify-center w-4 h-4 mr-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="w-3 h-3" />
              ) : (
                <ChevronRight className="w-3 h-3" />
              )}
            </button>
          ) : (
            <div className="w-5 mr-1" />
          )}
          
          <div className="flex items-center space-x-2 flex-1">
            <div className="relative">
              <input
                type="checkbox"
                id={`permission-${node.id}`}
                checked={node.checked}
                disabled={readOnly}
                ref={(input) => {
                  if (input) {
                    input.indeterminate = node.indeterminate
                  }
                }}
                onChange={(e) => !readOnly && handlePermissionToggle(node, e.target.checked)}
                className="rounded"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              {node.checked || node.indeterminate ? (
                <ShieldCheck className="w-4 h-4 text-green-600" />
              ) : (
                <Shield className="w-4 h-4 text-gray-400" />
              )}
              <Label 
                htmlFor={`permission-${node.id}`} 
                className="text-sm cursor-pointer font-medium"
              >
                {node.name}
              </Label>
            </div>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="ml-2">
            {node.children.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`border rounded-md p-3 max-h-64 overflow-y-auto bg-white ${readOnly ? 'bg-gray-50' : ''}`}>
      {treeData.length > 0 ? (
        <div className="space-y-1">
          {treeData.map(node => renderNode(node))}
        </div>
      ) : (
        <div className="text-center text-gray-500 py-4">
          <Shield className="w-8 h-8 mx-auto mb-2 text-gray-300" />
          <p className="text-sm">暂无权限数据</p>
        </div>
      )}
    </div>
  )
}
