import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'

interface CraftRoute {
  id: string
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  price: number
}

interface TemplateRouteEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  route: CraftRoute | null
  onSave: (route: CraftRoute) => void
}

const TemplateRouteEditDialog: React.FC<TemplateRouteEditDialogProps> = ({
  open,
  onOpenChange,
  route,
  onSave
}) => {
  const [editedRoute, setEditedRoute] = useState<CraftRoute | null>(null)

  useEffect(() => {
    if (route && open) {
      setEditedRoute({ ...route })
    }
  }, [route, open])

  const toggleMeasurementType = (type: string) => {
    if (!editedRoute) return
    
    const updated = editedRoute.measurement_types.includes(type)
      ? editedRoute.measurement_types.filter(t => t !== type)
      : [...editedRoute.measurement_types, type]
    
    setEditedRoute({ ...editedRoute, measurement_types: updated })
  }

  const toggleRegistrationType = (type: string) => {
    if (!editedRoute) return
    
    const updated = editedRoute.registration_types.includes(type)
      ? editedRoute.registration_types.filter(t => t !== type)
      : [...editedRoute.registration_types, type]
    
    setEditedRoute({ ...editedRoute, registration_types: updated })
  }

  const handleSave = () => {
    if (editedRoute) {
      onSave(editedRoute)
      onOpenChange(false)
    }
  }

  const handleFieldChange = (field: keyof CraftRoute, value: string | number | string[]) => {
    if (!editedRoute) return
    setEditedRoute({ ...editedRoute, [field]: value })
  }

  if (!editedRoute) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>编辑工艺路线</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="code" className="text-sm font-medium">代码</Label>
              <Input
                id="code"
                value={editedRoute.code}
                onChange={(e) => handleFieldChange('code', e.target.value)}
                placeholder="工艺路线代码"
              />
            </div>
            <div>
              <Label htmlFor="order" className="text-sm font-medium">顺序</Label>
              <Input
                id="order"
                type="number"
                min="1"
                value={editedRoute.order}
                onChange={(e) => handleFieldChange('order', parseInt(e.target.value) || 1)}
                placeholder="执行顺序"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="name" className="text-sm font-medium">名称</Label>
            <Input
              id="name"
              value={editedRoute.name}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              placeholder="工艺路线名称"
            />
          </div>
          
          {/* Skill Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="skill_code" className="text-sm font-medium">技能代码</Label>
              <Input
                id="skill_code"
                value={editedRoute.skill_code}
                onChange={(e) => handleFieldChange('skill_code', e.target.value)}
                placeholder="技能代码"
              />
            </div>
            <div>
              <Label htmlFor="skill_name" className="text-sm font-medium">技能名称</Label>
              <Input
                id="skill_name"
                value={editedRoute.skill_name || ''}
                onChange={(e) => handleFieldChange('skill_name', e.target.value)}
                placeholder="技能名称"
              />
            </div>
          </div>
          
          {/* Price */}
          <div>
            <Label htmlFor="price" className="text-sm font-medium">价格</Label>
            <Input
              id="price"
              type="number"
              min="0"
              step="0.01"
              value={editedRoute.price}
              onChange={(e) => handleFieldChange('price', parseFloat(e.target.value) || 0)}
              placeholder="0.00"
            />
          </div>
          
          {/* Measurement Types */}
          <div>
            <Label className="text-sm font-medium mb-3 block">计价方式</Label>
            <div className="flex flex-wrap gap-2">
              {MEASUREMENT_TYPES.map((type) => {
                const isSelected = editedRoute.measurement_types.includes(type.value)
                return (
                  <Badge
                    key={type.value}
                    variant={isSelected ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => toggleMeasurementType(type.value)}
                  >
                    {type.label}
                  </Badge>
                )
              })}
            </div>
          </div>
          
          {/* Registration Types */}
          <div>
            <Label className="text-sm font-medium mb-3 block">登记方式</Label>
            <div className="flex flex-wrap gap-2">
              {REGISTRATION_TYPES.map((type) => {
                const isSelected = editedRoute.registration_types.includes(type.value)
                return (
                  <Badge
                    key={type.value}
                    variant={isSelected ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => toggleRegistrationType(type.value)}
                  >
                    {type.label}
                  </Badge>
                )
              })}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSave}>
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default TemplateRouteEditDialog
