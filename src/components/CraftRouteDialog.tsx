import React from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { 
  CraftRouteDetailDto, 
  CraftRouteCreateDto, 
  CraftRouteUpdateDto,
  SkillResponseDto 
} from '@/services/types.gen'

// 测量类型选项
const MEASUREMENT_TYPES = [
  { value: 'length', label: '长度' },
  { value: 'weight', label: '重量' },
  { value: 'quantity', label: '数量' },
  { value: 'time', label: '时间' },
  { value: 'temperature', label: '温度' },
  { value: 'pressure', label: '压力' }
]

// 注册类型选项
const REGISTRATION_TYPES = [
  { value: 'manual', label: '手动录入' },
  { value: 'scan', label: '扫码录入' },
  { value: 'auto', label: '自动录入' },
  { value: 'photo', label: '照片记录' }
]

// Zod 验证模式
const craftRouteSchema = z.object({
  craft_code: z.string().min(1, '工艺编码不能为空'),
  skill_code: z.string().min(1, '请选择技能'),
  order: z.number().min(1, '顺序必须大于0'),
  measurement_types: z.array(z.string()).optional(),
  registration_types: z.array(z.string()).optional(),
  notes: z.string().optional(),
  is_required: z.boolean()
})

type CraftRouteFormData = z.infer<typeof craftRouteSchema>

interface MultiSelectCheckboxProps {
  options: Array<{ value: string; label: string }>
  value: string[]
  onChange: (value: string[]) => void
  label: string
  error?: string
}

function MultiSelectCheckbox({ options, value, onChange, label, error }: MultiSelectCheckboxProps) {
  const handleChange = (optionValue: string, checked: boolean) => {
    if (checked) {
      onChange([...value, optionValue])
    } else {
      onChange(value.filter(v => v !== optionValue))
    }
  }

  return (
    <div>
      <Label>{label}</Label>
      <div className="grid grid-cols-2 gap-2 mt-2">
        {options.map((option) => (
          <label key={option.value} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value.includes(option.value)}
              onChange={(e) => handleChange(option.value, e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">{option.label}</span>
          </label>
        ))}
      </div>
      {error && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}
    </div>
  )
}

interface CraftRouteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingRoute: CraftRouteDetailDto | null
  craftCode: string
  skills: SkillResponseDto[]
  maxOrder: number
  onSubmit: (data: CraftRouteCreateDto | { id: number; data: CraftRouteUpdateDto }) => void
  isLoading?: boolean
}

export default function CraftRouteDialog({ 
  open, 
  onOpenChange, 
  editingRoute, 
  craftCode,
  skills,
  maxOrder,
  onSubmit, 
  isLoading = false 
}: CraftRouteDialogProps) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isValid },
    reset,
    setValue,
    watch
  } = useForm<CraftRouteFormData>({
    resolver: zodResolver(craftRouteSchema),
    defaultValues: {
      craft_code: craftCode,
      skill_code: '',
      order: maxOrder + 1,
      measurement_types: [],
      registration_types: [],
      notes: '',
      is_required: true
    }
  })

  const watchIsRequired = watch('is_required')
  const watchMeasurementTypes = watch('measurement_types') || []
  const watchRegistrationTypes = watch('registration_types') || []

  // 当对话框打开时重置表单
  React.useEffect(() => {
    if (open) {
      if (editingRoute) {
        reset({
          craft_code: editingRoute.craft_code,
          skill_code: editingRoute.skill_code,
          order: editingRoute.order,
          measurement_types: editingRoute.measurement_types || [],
          registration_types: editingRoute.registration_types || [],
          notes: editingRoute.notes || '',
          is_required: editingRoute.is_required
        })
      } else {
        reset({
          craft_code: craftCode,
          skill_code: '',
          order: maxOrder + 1,
          measurement_types: [],
          registration_types: [],
          notes: '',
          is_required: true
        })
      }
    }
  }, [open, editingRoute, craftCode, maxOrder, reset])

  const onFormSubmit = (data: CraftRouteFormData) => {
    if (editingRoute) {
      // 编辑模式：只发送可更新的字段
      const updateData: CraftRouteUpdateDto = {
        order: data.order,
        measurement_types: data.measurement_types,
        registration_types: data.registration_types,
        notes: data.notes,
        is_required: data.is_required
      }
      onSubmit({ id: editingRoute.id, data: updateData })
    } else {
      // 创建模式：发送完整数据
      const createData: CraftRouteCreateDto = {
        craft_code: data.craft_code,
        skill_code: data.skill_code,
        order: data.order,
        measurement_types: data.measurement_types,
        registration_types: data.registration_types,
        notes: data.notes,
        is_required: data.is_required
      }
      onSubmit(createData)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>{editingRoute ? '编辑工艺路线' : '新建工艺路线'}</DialogTitle>
          <DialogDescription>
            {editingRoute ? '修改工艺路线配置' : '为工艺添加新的路线步骤'}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          <div>
            <Label htmlFor="skill_code">技能 *</Label>
            <Controller
              name="skill_code"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={errors.skill_code ? 'border-red-500' : ''}>
                    <SelectValue placeholder="选择技能" />
                  </SelectTrigger>
                  <SelectContent>
                    {skills.map((skill) => (
                      <SelectItem key={skill.code} value={skill.code}>
                        {skill.name} ({skill.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.skill_code && (
              <p className="text-sm text-red-500 mt-1">{errors.skill_code.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="order">顺序</Label>
            <Input
              id="order"
              type="number"
              min="1"
              {...register('order', { valueAsNumber: true })}
              className={errors.order ? 'border-red-500' : ''}
            />
            {errors.order && (
              <p className="text-sm text-red-500 mt-1">{errors.order.message}</p>
            )}
          </div>

          <MultiSelectCheckbox
            label="测量类型"
            options={MEASUREMENT_TYPES}
            value={watchMeasurementTypes}
            onChange={(value) => setValue('measurement_types', value)}
          />

          <MultiSelectCheckbox
            label="录入方式"
            options={REGISTRATION_TYPES}
            value={watchRegistrationTypes}
            onChange={(value) => setValue('registration_types', value)}
          />

          <div className="flex items-center space-x-2">
            <Switch
              id="is_required"
              checked={watchIsRequired}
              onCheckedChange={(checked) => setValue('is_required', checked)}
            />
            <Label htmlFor="is_required">必需步骤</Label>
          </div>

          <div>
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="输入备注信息"
              rows={3}
              className={errors.notes ? 'border-red-500' : ''}
            />
            {errors.notes && (
              <p className="text-sm text-red-500 mt-1">{errors.notes.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button 
              type="submit"
              disabled={!isValid || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {editingRoute ? '更新中...' : '创建中...'}
                </div>
              ) : (
                editingRoute ? '更新' : '创建'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
