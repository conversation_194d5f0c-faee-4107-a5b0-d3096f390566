import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Users, X } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from 'sonner'
import { 
  assignSkillsToUserApiV1SkillsAssignPostMutation,
  getAllSkillsApiV1SkillsGetOptions,
  getAvailableUsersApiV1UserManagementAvailableUsersGetOptions,
  getUserSkillsApiV1SkillsUserUserIdSkillsGetOptions
} from '@/services/@tanstack/react-query.gen'

// 表单验证 schema
const assignSkillSchema = z.object({
  user_id: z.number().min(1, '请选择员工'),
  skill_ids: z.array(z.number()).min(1, '请至少选择一个技能'),
  proficiency_level: z.enum(['beginner', 'intermediate', 'advanced', 'expert'])
})

type AssignSkillFormData = z.infer<typeof assignSkillSchema>

// 熟练度级别选项
const PROFICIENCY_LEVELS = [
  { value: 'beginner', label: '初级' },
  { value: 'intermediate', label: '中级' },
  { value: 'advanced', label: '高级' },
  { value: 'expert', label: '专家' }
]

interface AssignSkillDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function AssignSkillDialog({ open, onOpenChange }: AssignSkillDialogProps) {
  const queryClient = useQueryClient()
  
  // 使用 react-hook-form
  const form = useForm<AssignSkillFormData>({
    resolver: zodResolver(assignSkillSchema),
    defaultValues: {
      user_id: 0,
      skill_ids: [],
      proficiency_level: 'beginner'
    }
  })

  // 获取所有技能
  const { data: skillsData } = useQuery(
    getAllSkillsApiV1SkillsGetOptions()
  )

  // 获取工厂用户列表
  const { data: usersData } = useQuery(
    getAvailableUsersApiV1UserManagementAvailableUsersGetOptions({
      query: {
        is_active: true
      }
    })
  )

  // 监听选中的用户ID
  const selectedUserId = form.watch('user_id')

  // 获取用户现有技能 (当选择了用户时)
  const { data: userSkillsData, isLoading: isLoadingUserSkills } = useQuery({
    ...getUserSkillsApiV1SkillsUserUserIdSkillsGetOptions({
      path: { user_id: selectedUserId }
    }),
    enabled: selectedUserId > 0, // 只有当选择了用户时才查询
  })

  // 分配技能mutation
  const assignSkillMutation = useMutation(assignSkillsToUserApiV1SkillsAssignPostMutation())

  // 简化数据访问
  const skills = skillsData?.skills || []

  // 当用户技能数据加载完成时，预填充表单
  useEffect(() => {
    if (userSkillsData?.skills && selectedUserId > 0) {
      const userSkillIds = userSkillsData.skills.map(userSkill => userSkill.skill.id)
      const commonProficiency = userSkillsData.skills[0]?.proficiency_level.toLowerCase() as 'beginner' | 'intermediate' | 'advanced' | 'expert' || 'beginner'
      
      form.setValue('skill_ids', userSkillIds)
      form.setValue('proficiency_level', commonProficiency)
    }
  }, [userSkillsData, selectedUserId, form])

  // 当用户改变时，重置技能选择
  useEffect(() => {
    if (selectedUserId > 0) {
      form.setValue('skill_ids', [])
      form.setValue('proficiency_level', 'beginner')
    }
  }, [selectedUserId, form])

  // 处理分配技能
  const handleAssignSkill = async (data: AssignSkillFormData) => {
    try {
      // 为每个选中的技能创建分配记录
      const skillAssignments = data.skill_ids.map(skill_id => ({
        user_id: data.user_id,
        skill_id,
        proficiency_level: data.proficiency_level
      }))

      await assignSkillMutation.mutateAsync({
        body: {
          user_id: data.user_id,
          skills: skillAssignments
        }
      })
      
      // 重置表单
      form.reset()
      onOpenChange(false)
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
      
      const isEdit = selectedUserId > 0 && userSkillsData?.skills
      toast.success(isEdit ? '技能更新成功' : '技能分配成功')
    } catch {
      const isEdit = selectedUserId > 0 && userSkillsData?.skills
      toast.error(isEdit ? '技能更新失败' : '技能分配失败')
    }
  }

  // 重置表单当对话框关闭时
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset()
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Users className="h-4 w-4 mr-2" />
          分配技能
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {selectedUserId > 0 && userSkillsData?.skills ? '编辑员工技能' : '分配技能给员工'}
          </DialogTitle>
          <DialogDescription>
            {selectedUserId > 0 && userSkillsData?.skills 
              ? '修改员工的技能分配和熟练度级别' 
              : '为员工分配新的技能并设置熟练度级别'
            }
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleAssignSkill)} className="space-y-6">
            {/* 选择员工 */}
            <FormField
              control={form.control}
              name="user_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>选择员工</FormLabel>
                  <Select 
                    value={field.value.toString()} 
                    onValueChange={(value) => field.onChange(parseInt(value))}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择员工" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {usersData?.users.map(user => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {user.full_name || user.username}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 选择技能 - 多选 */}
            <FormField
              control={form.control}
              name="skill_ids"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>选择技能（可多选）</FormLabel>
                  <FormDescription>
                    {selectedUserId > 0 && userSkillsData?.skills 
                      ? '当前显示的是该员工已有的技能，您可以添加或移除技能' 
                      : '选择要分配给员工的技能，可以选择多个技能'
                    }
                    {selectedUserId > 0 && isLoadingUserSkills && (
                      <span className="text-blue-600"> (正在加载用户技能...)</span>
                    )}
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-3 p-4 border rounded-md max-h-64 overflow-y-auto">
                    {skills.filter(s => s.is_active).map(skill => (
                      <div key={skill.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`skill-${skill.id}`}
                          checked={field.value.includes(skill.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              field.onChange([...field.value, skill.id])
                            } else {
                              field.onChange(field.value.filter(id => id !== skill.id))
                            }
                          }}
                        />
                        <label
                          htmlFor={`skill-${skill.id}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        >
                          {skill.name}
                        </label>
                      </div>
                    ))}
                  </div>
                  {field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value.map(skillId => {
                        const skill = skills.find(s => s.id === skillId)
                        return skill ? (
                          <Badge key={skillId} variant="secondary" className="flex items-center gap-1">
                            {skill.name}
                            <X 
                              className="h-3 w-3 cursor-pointer" 
                              onClick={() => {
                                field.onChange(field.value.filter(id => id !== skillId))
                              }}
                            />
                          </Badge>
                        ) : null
                      })}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 熟练度级别 */}
            <FormField
              control={form.control}
              name="proficiency_level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>熟练度级别</FormLabel>
                  <FormDescription>
                    所有选中的技能将使用相同的熟练度级别
                  </FormDescription>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择熟练度" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {PROFICIENCY_LEVELS.map(level => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => handleOpenChange(false)}>
                取消
              </Button>
              <Button 
                type="submit"
                disabled={assignSkillMutation.isPending || !form.watch('user_id') || form.watch('skill_ids').length === 0}
              >
                {assignSkillMutation.isPending 
                  ? (selectedUserId > 0 && userSkillsData?.skills ? '更新中...' : '分配中...') 
                  : (selectedUserId > 0 && userSkillsData?.skills ? '更新技能' : '分配技能')
                }
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
