import { useState, useMemo } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Users, Plus, Trash2, Award } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  getAvailableUsersApiV1UserManagementAvailableUsersGetOptions,
  assignSkillsToUserApiV1SkillsAssignPostMutation,
  removeUserSkillApiV1SkillsRemoveDeleteMutation,
  certifyUserSkillApiV1SkillsCertifyPostMutation,
  getSkillByIdApiV1SkillsSkillIdGetOptions,
} from "@/services/@tanstack/react-query.gen";
import type {
  SkillResponseDto,
} from "@/services/types.gen";

// 熟练度级别选项
const PROFICIENCY_LEVELS = [
  { value: "beginner", label: "初级", color: "bg-red-100 text-red-800" },
  {
    value: "intermediate",
    label: "中级",
    color: "bg-yellow-100 text-yellow-800",
  },
  { value: "advanced", label: "高级", color: "bg-blue-100 text-blue-800" },
  { value: "expert", label: "专家", color: "bg-green-100 text-green-800" },
];

// 添加用户表单schema
const addUserSchema = z.object({
  user_id: z.number().min(1, "请选择员工"),
  proficiency_level: z.enum(["beginner", "intermediate", "advanced", "expert"]),
});

type AddUserFormData = z.infer<typeof addUserSchema>;

interface SkillUsersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  skill: SkillResponseDto | null;
}

export default function SkillUsersDialog({
  open,
  onOpenChange,
  skill,
}: SkillUsersDialogProps) {
  const [showAddForm, setShowAddForm] = useState(false);

  // 添加用户表单
  const form = useForm<AddUserFormData>({
    resolver: zodResolver(addUserSchema),
    defaultValues: {
      user_id: 0,
      proficiency_level: "beginner",
    },
  });

  const { data: skillData, refetch } = useQuery({
    ...getSkillByIdApiV1SkillsSkillIdGetOptions({
      path: {
        skill_id: skill?.id || 0,
      },
    }),
    enabled: !!skill?.id,
  });

  const skillUsers = useMemo(() => {
    return skillData?.user_skills || []
  }, [skillData]);

  // 获取所有可用用户
  const { data: usersData } = useQuery(
    getAvailableUsersApiV1UserManagementAvailableUsersGetOptions({
      query: {
        is_active: true,
      },
    })
  );

  // 分配技能mutation
  const assignSkillMutation = useMutation(
    assignSkillsToUserApiV1SkillsAssignPostMutation()
  );

  // 移除技能mutation
  const removeSkillMutation = useMutation(
    removeUserSkillApiV1SkillsRemoveDeleteMutation()
  );

  // 认证技能mutation
  const certifySkillMutation = useMutation(
    certifyUserSkillApiV1SkillsCertifyPostMutation()
  );

  // 获取熟练度信息
  const getProficiencyInfo = (level: string) => {
    return (
      PROFICIENCY_LEVELS.find((p) => p.value === level.toLowerCase()) ||
      PROFICIENCY_LEVELS[0]
    );
  };

  // 处理添加用户
  const handleAddUser = async (data: AddUserFormData) => {
    if (!skill) return;

    try {
      await assignSkillMutation.mutateAsync({
        body: {
          user_id: data.user_id,
          skills: [
            {
              user_id: data.user_id,
              skill_id: skill.id,
              proficiency_level: data.proficiency_level,
            },
          ],
        },
      });

      form.reset();
      setShowAddForm(false);
      toast.success("用户添加成功");

      // 重新获取数据 - 这里应该重新获取技能用户列表
      // 实际实现时应该调用相应的API
    } catch {
      toast.error("用户添加失败");
    }
  };

  // 处理移除用户
  const handleRemoveUser = async (userSkillId: number) => {
    try {
      await removeSkillMutation.mutateAsync({
        body: {
          user_factory_skill_id: userSkillId,
        },
      });

      refetch();
      toast.success("用户移除成功");
    } catch {
      toast.error("用户移除失败");
    }
  };

  // 处理认证技能
  const handleCertifySkill = async (userSkillId: number) => {
    try {
      await certifySkillMutation.mutateAsync({
        body: {
          user_factory_skill_id: userSkillId,
        },
      });

      refetch(); // 重新获取技能数据
      toast.success("技能认证成功");
    } catch {
      toast.error("技能认证失败");
    }
  };

  // 获取未拥有此技能的用户
  const availableUsers =
    usersData?.users.filter(
      (user) =>
        !skillUsers.some((su) => {
            return user.id === su.user?.id;
        })
    ) || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            技能"{skill?.name}"的掌握情况
          </DialogTitle>
          <DialogDescription>
            管理掌握该技能的员工，可以添加新员工或移除现有员工
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 overflow-y-auto">
          {/* 统计信息 */}
          <div className="grid grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-4">
                <div className="text-2xl font-bold">{skillUsers.length}</div>
                <p className="text-xs text-muted-foreground">总掌握人数</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-4">
                <div className="text-2xl font-bold text-green-600">
                  {skillUsers.filter((u) => u.certified).length}
                </div>
                <p className="text-xs text-muted-foreground">已认证人数</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-4">
                <div className="text-2xl font-bold text-blue-600">
                  {
                    skillUsers.filter(
                      (u) => u.proficiency_level.toLowerCase() === "expert"
                    ).length
                  }
                </div>
                <p className="text-xs text-muted-foreground">专家级人数</p>
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* 添加用户表单 */}
          {showAddForm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">添加员工</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(handleAddUser)}
                    className="space-y-4"
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="user_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>选择员工</FormLabel>
                            <Select
                              value={field.value.toString()}
                              onValueChange={(value) =>
                                field.onChange(parseInt(value))
                              }
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择员工" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {availableUsers.map((user) => (
                                  <SelectItem
                                    key={user.id}
                                    value={user.id.toString()}
                                  >
                                    {user.full_name || user.username}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="proficiency_level"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>熟练度级别</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择熟练度" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {PROFICIENCY_LEVELS.map((level) => (
                                  <SelectItem
                                    key={level.value}
                                    value={level.value}
                                  >
                                    {level.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button
                        type="submit"
                        disabled={assignSkillMutation.isPending}
                      >
                        {assignSkillMutation.isPending
                          ? "添加中..."
                          : "添加员工"}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setShowAddForm(false)}
                      >
                        取消
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          )}

          {/* 用户列表 */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">掌握该技能的员工</h3>
              {!showAddForm && (
                <Button onClick={() => setShowAddForm(true)} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  添加员工
                </Button>
              )}
            </div>

            {skillUsers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无员工掌握该技能
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>员工</TableHead>
                    <TableHead>熟练度</TableHead>
                    <TableHead>认证状态</TableHead>
                    <TableHead>分配时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {skillUsers.map((userSkill) => {
                    const proficiencyInfo = getProficiencyInfo(
                      userSkill.proficiency_level
                    );
                    return (
                      <TableRow key={userSkill.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={`https://api.dicebear.com/7.x/avataaars/svg?seed=user${userSkill.id}`}
                              />
                              <AvatarFallback>U</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {userSkill.user?.full_name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {userSkill.user?.phone}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={proficiencyInfo.color}>
                            {proficiencyInfo.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {userSkill.certified ? (
                            <div className="flex items-center text-green-600">
                              <Award className="h-4 w-4 mr-1" />
                              已认证
                              {userSkill.certification_date && (
                                <span className="ml-1 text-xs text-muted-foreground">
                                  (
                                  {new Date(
                                    userSkill.certification_date
                                  ).toLocaleDateString()}
                                  )
                                </span>
                              )}
                            </div>
                          ) : (
                            <Badge variant="outline">未认证</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {new Date(userSkill.assigned_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            {!userSkill.certified && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleCertifySkill(userSkill.id)}
                                disabled={certifySkillMutation.isPending}
                              >
                                <Award className="h-4 w-4 mr-1" />
                                认证
                              </Button>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => handleRemoveUser(userSkill.id)}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  移除技能
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
