import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import type { SkillResponseDto, SkillCreateDto, SkillUpdateDto } from '@/services/types.gen'

// 技能分类选项
const SKILL_CATEGORIES = [
  { value: 'production', label: '生产技能' },
  { value: 'quality', label: '质量控制' },
  { value: 'maintenance', label: '设备维护' },
  { value: 'safety', label: '安全操作' },
  { value: 'management', label: '管理技能' },
  { value: 'technical', label: '技术技能' },
  { value: 'other', label: '其他' }
]

// Zod 验证模式
const skillSchema = z.object({
  name: z.string().min(1, '技能名称不能为空').max(100, '技能名称不能超过100个字符'),
  code: z.string().min(1, '技能代码不能为空').max(50, '技能代码不能超过50个字符'),
  category: z.string().min(1, '请选择技能分类'),
  description: z.string().optional(),
  is_active: z.boolean()
})

type SkillFormData = z.infer<typeof skillSchema>

interface SkillDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingSkill: SkillResponseDto | null
  onSubmit: (data: SkillCreateDto | { id: number; data: SkillUpdateDto }) => void
  isLoading?: boolean
}

export default function SkillDialog({ 
  open, 
  onOpenChange, 
  editingSkill, 
  onSubmit, 
  isLoading = false 
}: SkillDialogProps) {
  const form = useForm<SkillFormData>({
    resolver: zodResolver(skillSchema),
    defaultValues: {
      name: '',
      code: '',
      category: '',
      description: '',
      is_active: true
    }
  })

  // 当对话框打开时重置表单
  React.useEffect(() => {
    if (open) {
      if (editingSkill) {
        form.reset({
          name: editingSkill.name || '',
          code: editingSkill.code || '',
          category: editingSkill.category || '',
          description: editingSkill.description || '',
          is_active: editingSkill.is_active || false
        })
      } else {
        form.reset({
          name: '',
          code: '',
          category: '',
          description: '',
          is_active: true
        })
      }
    }
  }, [open, editingSkill, form])

  const onFormSubmit = (data: SkillFormData) => {
    if (editingSkill) {
      // 编辑模式：只发送可更新的字段（不包括code）
      const updateData: SkillUpdateDto = {
        name: data.name,
        category: data.category,
        description: data.description,
        is_active: data.is_active
      }
      onSubmit({ id: editingSkill.id, data: updateData })
    } else {
      // 创建模式：发送完整数据
      const createData: SkillCreateDto = {
        name: data.name,
        code: data.code,
        category: data.category,
        description: data.description,
        is_active: data.is_active
      }
      onSubmit(createData)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{editingSkill ? '编辑技能' : '创建新技能'}</DialogTitle>
          <DialogDescription>
            {editingSkill ? '修改技能信息' : '添加新的技能类型到系统中'}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>技能名称 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入技能名称"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>技能代码 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入技能代码"
                      disabled={!!editingSkill}
                      {...field}
                    />
                  </FormControl>
                  {editingSkill && (
                    <p className="text-xs text-gray-500 mt-1">技能代码在创建后不能修改</p>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>技能分类 *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择技能分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {SKILL_CATEGORIES.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>技能描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入技能描述"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel>启用状态</FormLabel>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button 
                type="submit"
                disabled={!form.formState.isValid || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {editingSkill ? '保存中...' : '创建中...'}
                  </div>
                ) : (
                  editingSkill ? '保存更改' : '创建技能'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
