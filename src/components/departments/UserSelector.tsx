import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Search, User, X } from "lucide-react"
import { getAvailableUsersApiV1UserManagementAvailableUsersGetOptions } from '@/services/@tanstack/react-query.gen'
import type { UserSummaryDto } from '@/services/types.gen'

interface UserSelectorProps {
  selectedUserId?: number | null
  selectedUserName?: string | null
  onUserSelect: (user: UserSummaryDto | null) => void
  label?: string
  placeholder?: string
  disabled?: boolean
}

export default function UserSelector({
  selectedUserId,
  selectedUserName,
  onUserSelect,
  label = "选择用户",
  placeholder = "点击选择用户",
  disabled = false
}: UserSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<UserSummaryDto | null>(null)

  // Fetch factory users
  const { data: usersData, isLoading } = useQuery({
    ...getAvailableUsersApiV1UserManagementAvailableUsersGetOptions(),
    enabled: isOpen, // Only fetch when dialog is open
    staleTime: 30000,
  })

  // Update selected user when props change
  useEffect(() => {
    if (selectedUserId && selectedUserName) {
      setSelectedUser({
        id: selectedUserId,
        username: selectedUserName,
        full_name: selectedUserName,
        email: '',
        is_active: true
      })
    } else {
      setSelectedUser(null)
    }
  }, [selectedUserId, selectedUserName])

  const users = usersData?.users || []
  
  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase()
    return (
      user.full_name?.toLowerCase().includes(searchLower) ||
      user.username.toLowerCase().includes(searchLower) ||
      user.email?.toLowerCase().includes(searchLower)
    )
  })

  const handleUserSelect = (user: UserSummaryDto) => {
    setSelectedUser(user)
    onUserSelect(user)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClearSelection = () => {
    setSelectedUser(null)
    onUserSelect(null)
  }

  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      
      <div className="flex items-center space-x-2">
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="flex-1 justify-start text-left font-normal"
              disabled={disabled}
            >
              {selectedUser ? (
                <div className="flex items-center space-x-2">
                  <Avatar className="w-6 h-6">
                    <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                      <User className="w-3 h-3 text-blue-600" />
                    </div>
                  </Avatar>
                  <span>{selectedUser.full_name || selectedUser.username}</span>
                </div>
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )}
            </Button>
          </DialogTrigger>
          
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>选择用户</DialogTitle>
              <DialogDescription>
                从工厂员工中选择一个用户
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索用户..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="max-h-64 overflow-y-auto space-y-2">
                {isLoading ? (
                  <div className="text-center py-4 text-muted-foreground">
                    加载中...
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground">
                    {searchTerm ? '未找到匹配的用户' : '暂无可选用户'}
                  </div>
                ) : (
                  filteredUsers.map((factoryUser) => (
                    <div
                      key={factoryUser.id}
                      className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => handleUserSelect(factoryUser)}
                    >
                      <Avatar className="w-8 h-8">
                        <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                          <User className="w-4 h-4 text-blue-600" />
                        </div>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {factoryUser.full_name || factoryUser.username}
                        </div>
                        <div className="text-sm text-muted-foreground truncate">
                          {factoryUser.email}
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {factoryUser.role?.name}
                          </Badge>
                          <Badge 
                            variant={factoryUser.status === 'APPROVED' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {factoryUser.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
        
        {selectedUser && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSelection}
            disabled={disabled}
            className="px-2"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>
    </div>
  )
}
