import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Loader2 } from "lucide-react"
import UserSelector from './UserSelector'
import type { DepartmentResponseDto, DepartmentCreateDto, DepartmentUpdateDto, UserSummaryDto } from '@/services/types.gen'

// Zod validation schema
const departmentSchema = z.object({
  name: z.string().min(1, '部门名称不能为空').max(100, '部门名称不能超过100个字符'),
  code: z.string().min(1, '部门代码不能为空').max(50, '部门代码不能超过50个字符'),
  manager_id: z.number().optional(),
  manager_name: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
  location: z.string().optional(),
  description: z.string().optional(),
  is_active: z.boolean()
})

type DepartmentFormData = z.infer<typeof departmentSchema>

interface DepartmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingDepartment: DepartmentResponseDto | null
  onSubmit: (data: DepartmentCreateDto | { id: number; data: DepartmentUpdateDto }) => void
  isLoading?: boolean
  currentUserId?: number
}

export default function DepartmentDialog({
  open,
  onOpenChange,
  editingDepartment,
  onSubmit,
  isLoading = false,
  currentUserId
}: DepartmentDialogProps) {
  const form = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: '',
      code: '',
      manager_id: undefined,
      manager_name: '',
      phone: '',
      email: '',
      location: '',
      description: '',
      is_active: true
    }
  })

  // Reset form when dialog opens
  React.useEffect(() => {
    if (open) {
      if (editingDepartment) {
        form.reset({
          name: editingDepartment.name || '',
          code: editingDepartment.code || '',
          manager_id: undefined, // We'll handle this separately
          manager_name: editingDepartment.manager_name || '',
          phone: editingDepartment.phone || '',
          email: editingDepartment.email || '',
          location: editingDepartment.location || '',
          description: editingDepartment.description || '',
          is_active: editingDepartment.is_active ?? true
        })
      } else {
        form.reset({
          name: '',
          code: '',
          manager_id: undefined,
          manager_name: '',
          phone: '',
          email: '',
          location: '',
          description: '',
          is_active: true
        })
      }
    }
  }, [open, editingDepartment, form])

  const onFormSubmit = (data: DepartmentFormData) => {
    if (!currentUserId) {
      console.error('Current user ID is required')
      return
    }

    if (editingDepartment) {
      // Edit mode: only send updatable fields
      const updateData: DepartmentUpdateDto = {
        name: data.name,
        manager_name: data.manager_name || null,
        phone: data.phone || null,
        email: data.email || null,
        location: data.location || null,
        description: data.description || null,
        is_active: data.is_active,
        operator_id: currentUserId
      }
      onSubmit({ id: editingDepartment.id, data: updateData })
    } else {
      // Create mode: send complete data
      const createData: DepartmentCreateDto = {
        name: data.name,
        code: data.code,
        manager_name: data.manager_name || null,
        phone: data.phone || null,
        email: data.email || null,
        location: data.location || null,
        description: data.description || null,
        is_active: data.is_active,
        operator_id: currentUserId
      }
      onSubmit(createData)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{editingDepartment ? '编辑部门' : '创建新部门'}</DialogTitle>
          <DialogDescription>
            {editingDepartment ? '修改部门信息' : '添加新的部门到系统中'}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>部门名称 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="输入部门名称"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>部门代码 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="输入部门代码"
                        disabled={!!editingDepartment}
                        {...field}
                      />
                    </FormControl>
                    {editingDepartment && (
                      <p className="text-xs text-gray-500 mt-1">部门代码在创建后不能修改</p>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>部门描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入部门描述"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="manager_id"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <UserSelector
                      selectedUserId={field.value}
                      selectedUserName={form.watch('manager_name')}
                      onUserSelect={(user: UserSummaryDto | null) => {
                        field.onChange(user?.id || undefined)
                        form.setValue('manager_name', user?.full_name || user?.username || '')
                      }}
                      label="部门主管"
                      placeholder="点击选择部门主管"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系电话</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="输入联系电话"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>邮箱地址</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="输入邮箱地址"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>部门位置</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入部门位置"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel>启用状态</FormLabel>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button 
                type="submit"
                disabled={!form.formState.isValid || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                    {editingDepartment ? '保存中...' : '创建中...'}
                  </div>
                ) : (
                  editingDepartment ? '保存更改' : '创建部门'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
