import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, User, Calendar, Clock, DollarSign, FileText, Factory } from "lucide-react"
import { getBillApiV1BillsBillIdGetOptions } from '@/services/@tanstack/react-query.gen'
import type { BillResponseDto, BillStatus } from '@/services/types.gen'

// Status badge component (reused from BillsPage)
const BillStatusBadge = ({ status }: { status: BillStatus }) => {
  const statusConfig = {
    draft: { label: '草稿', variant: 'secondary' as const, icon: FileText },
    pending: { label: '待审核', variant: 'default' as const, icon: Clock },
    approved: { label: '已审核', variant: 'default' as const, icon: FileText },
    paid: { label: '已支付', variant: 'default' as const, icon: DollarSign },
    rejected: { label: '已拒绝', variant: 'destructive' as const, icon: FileText },
    cancelled: { label: '已取消', variant: 'secondary' as const, icon: FileText }
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="w-3 h-3" />
      {config.label}
    </Badge>
  )
}

interface BillDetailDialogProps {
  billId: number | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function BillDetailDialog({ billId, open, onOpenChange }: BillDetailDialogProps) {
  // Get bill details
  const { data: bill, isLoading, error } = useQuery({
    ...getBillApiV1BillsBillIdGetOptions({
      path: { bill_id: billId! }
    }),
    enabled: !!billId && open
  })

  // Format currency
  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return `¥${num.toFixed(2)}`
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format datetime
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  if (!billId) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            账单详情
          </DialogTitle>
          <DialogDescription>
            查看账单的详细信息和统计数据
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-destructive">加载失败，请重试</p>
          </div>
        ) : bill ? (
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>基本信息</span>
                  <BillStatusBadge status={bill.status} />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">账单号</label>
                    <p className="font-mono">{bill.bill_no}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">账单日期</label>
                    <p>{formatDate(bill.bill_date)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">员工</label>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      <div>
                        <p className="font-medium">{bill.worker_user?.name || '-'}</p>
                        <p className="text-sm text-muted-foreground">{bill.worker_user?.phone || '-'}</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">工厂</label>
                    <div className="flex items-center gap-2">
                      <Factory className="w-4 h-4" />
                      <p>{bill.factory?.name || '-'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Work Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>工作统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{bill.total_completed_quantity}</p>
                    <p className="text-sm text-muted-foreground">完成数量</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{bill.total_work_instances}</p>
                    <p className="text-sm text-muted-foreground">工作实例</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">{Math.round(bill.total_work_duration_minutes / 60)}</p>
                    <p className="text-sm text-muted-foreground">工作小时</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle>财务信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">基础金额</span>
                    <span className="font-medium">{formatCurrency(bill.base_amount)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">奖金</span>
                    <span className="font-medium text-green-600">+{formatCurrency(bill.bonus_amount)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">扣款</span>
                    <span className="font-medium text-red-600">-{formatCurrency(bill.deduction_amount)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">总金额</span>
                    <span className="text-lg font-bold">{formatCurrency(bill.total_amount)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quality Information */}
            {(bill.quality_score_average || bill.defect_count > 0 || bill.rework_count > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle>质量信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-yellow-600">
                        {bill.quality_score_average ? parseFloat(bill.quality_score_average).toFixed(1) : '-'}
                      </p>
                      <p className="text-sm text-muted-foreground">平均质量分</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-red-600">{bill.defect_count}</p>
                      <p className="text-sm text-muted-foreground">缺陷数量</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-orange-600">{bill.rework_count}</p>
                      <p className="text-sm text-muted-foreground">返工数量</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Review and Payment Information */}
            {(bill.reviewed_by_user || bill.paid_by_user) && (
              <Card>
                <CardHeader>
                  <CardTitle>审核与支付信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {bill.reviewed_by_user && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">审核信息</label>
                      <div className="mt-1">
                        <p className="font-medium">审核人: {bill.reviewed_by_user.name}</p>
                        {bill.reviewed_at && <p className="text-sm text-muted-foreground">审核时间: {formatDateTime(bill.reviewed_at)}</p>}
                        {bill.review_notes && <p className="text-sm">备注: {bill.review_notes}</p>}
                      </div>
                    </div>
                  )}
                  
                  {bill.paid_by_user && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">支付信息</label>
                      <div className="mt-1">
                        <p className="font-medium">支付人: {bill.paid_by_user.name}</p>
                        {bill.paid_at && <p className="text-sm text-muted-foreground">支付时间: {formatDateTime(bill.paid_at)}</p>}
                        {bill.payment_method && <p className="text-sm">支付方式: {bill.payment_method}</p>}
                        {bill.payment_reference && <p className="text-sm">支付参考: {bill.payment_reference}</p>}
                        {bill.payment_notes && <p className="text-sm">支付备注: {bill.payment_notes}</p>}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Additional Information */}
            <Card>
              <CardHeader>
                <CardTitle>其他信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">自动生成</span>
                  <span>{bill.is_auto_generated ? '是' : '否'}</span>
                </div>
                {bill.generated_at && (
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">生成时间</span>
                    <span>{formatDateTime(bill.generated_at)}</span>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">创建时间</span>
                  <span>{formatDateTime(bill.created_at)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">更新时间</span>
                  <span>{formatDateTime(bill.updated_at)}</span>
                </div>
                {bill.notes && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">备注</label>
                    <p className="mt-1">{bill.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  )
}
