import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { 
  Check, 
  X, 
  DollarSign, 
  Clock, 
  FileText,
  TrendingUp,
  Calendar,
  Loader2
} from "lucide-react"
import { toast } from 'sonner'
import { formatDate } from '@/lib/dateUtils'
import {
  performAuditActionApiV1PriceAuditActionPostMutation,
  updateAuditRecordApiV1PriceAuditRecordIdPutMutation,
  setPriorityApiV1PriceAuditSetPriorityPostMutation,
  escalateAuditApiV1PriceAuditEscalatePostMutation
} from '@/services/@tanstack/react-query.gen'
import type { 
  CraftRoutePriceAuditListDto,
  PriceAuditAction,
  PriceErrorType
} from '@/services/types.gen'

// Status badge component (reused from main page)
const AuditStatusBadge = ({ action, isResolved }: { action: PriceAuditAction, isResolved: boolean }) => {
  const statusConfig = {
    submitted: { label: '已提交', variant: 'secondary' as const, icon: FileText },
    under_review: { label: '审核中', variant: 'default' as const, icon: Clock },
    approved: { label: '已批准', variant: 'default' as const, icon: Check },
    rejected: { label: '已拒绝', variant: 'destructive' as const, icon: X },
    price_corrected: { label: '价格已更正', variant: 'default' as const, icon: DollarSign },
    withdrawn: { label: '已撤销', variant: 'secondary' as const, icon: X }
  }

  const config = statusConfig[action]
  const Icon = config.icon

  return (
    <Badge variant={isResolved ? 'default' : config.variant} className="flex items-center gap-1">
      <Icon size={12} />
      {config.label}
      {isResolved && ' (已解决)'}
    </Badge>
  )
}

// Error type badge component (reused from main page)
const ErrorTypeBadge = ({ errorType }: { errorType: PriceErrorType | null | undefined }) => {
  if (!errorType) return <span className="text-gray-400">-</span>

  const errorConfig = {
    price_too_high: { label: '价格过高', variant: 'destructive' as const },
    price_too_low: { label: '价格过低', variant: 'destructive' as const },
    wrong_price: { label: '价格错误', variant: 'destructive' as const },
    missing_price: { label: '缺少价格', variant: 'outline' as const },
    calculation_error: { label: '计算错误', variant: 'secondary' as const },
    other: { label: '其他', variant: 'outline' as const }
  }

  const config = errorConfig[errorType]
  return <Badge variant={config.variant}>{config.label}</Badge>
}

// Priority badge component (reused from main page)
const PriorityBadge = ({ priority }: { priority: string }) => {
  const priorityConfig = {
    low: { label: '低', variant: 'secondary' as const },
    medium: { label: '中', variant: 'default' as const },
    high: { label: '高', variant: 'destructive' as const },
    critical: { label: '紧急', variant: 'destructive' as const }
  }

  const config = priorityConfig[priority as keyof typeof priorityConfig] || 
                  { label: priority, variant: 'outline' as const }
  
  return <Badge variant={config.variant}>{config.label}</Badge>
}

interface AuditRecordDetailDialogProps {
  record: CraftRoutePriceAuditListDto | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onRecordUpdated?: () => void
}

export default function AuditRecordDetailDialog({
  record,
  open,
  onOpenChange,
  onRecordUpdated
}: AuditRecordDetailDialogProps) {
  const [actionReason, setActionReason] = useState('')
  const [correctedPrice, setCorrectedPrice] = useState('')
  const [resolution, setResolution] = useState('')
  const [escalationReason, setEscalationReason] = useState('')

  // Mutations
  const performActionMutation = useMutation(performAuditActionApiV1PriceAuditActionPostMutation())
  const updateRecordMutation = useMutation(updateAuditRecordApiV1PriceAuditRecordIdPutMutation())
  const setPriorityMutation = useMutation(setPriorityApiV1PriceAuditSetPriorityPostMutation())
  const escalateMutation = useMutation(escalateAuditApiV1PriceAuditEscalatePostMutation())

  const handlePerformAction = async (action: PriceAuditAction) => {
    if (!record) return

    try {
      await performActionMutation.mutateAsync({
        body: {
          record_id: record.id,
          action,
          user_id: 1, // This should come from context/auth
          reason: actionReason || undefined,
          corrected_price: correctedPrice || undefined,
          resolution: resolution || undefined
        }
      })
      
      toast.success(`操作成功：${action}`)
      setActionReason('')
      setCorrectedPrice('')
      setResolution('')
      onRecordUpdated?.()
      onOpenChange(false)
    } catch {
      toast.error('操作失败')
    }
  }

  const handleUpdateRecord = async () => {
    if (!record) return

    try {
      await updateRecordMutation.mutateAsync({
        path: { record_id: record.id },
        body: {
          corrected_price: correctedPrice || undefined,
          reason: actionReason || undefined,
          resolution: resolution || undefined
        }
      })
      
      toast.success('记录更新成功')
      onRecordUpdated?.()
    } catch {
      toast.error('记录更新失败')
    }
  }

  const handleSetPriority = async (priority: string) => {
    if (!record) return

    try {
      await setPriorityMutation.mutateAsync({
        body: {
          record_ids: [record.id],
          priority_level: priority
        }
      })
      
      toast.success(`优先级已设置为：${priority}`)
      onRecordUpdated?.()
    } catch {
      toast.error('设置优先级失败')
    }
  }

  const handleEscalate = async () => {
    if (!record || !escalationReason.trim()) {
      toast.error('请输入升级原因')
      return
    }

    try {
      await escalateMutation.mutateAsync({
        body: {
          record_id: record.id,
          escalation_reason: escalationReason
        }
      })
      
      toast.success('审核记录已升级')
      setEscalationReason('')
      onRecordUpdated?.()
    } catch {
      toast.error('升级失败')
    }
  }

  if (!record) return null

  const isLoading = performActionMutation.isPending || 
                   updateRecordMutation.isPending || 
                   setPriorityMutation.isPending || 
                   escalateMutation.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            审核记录详情 - #{record.id}
          </DialogTitle>
          <DialogDescription>
            工艺路线实例 ID: {record.order_craft_route_instance_id}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Overview */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">当前状态</CardTitle>
              </CardHeader>
              <CardContent>
                <AuditStatusBadge action={record.audit_action} isResolved={record.is_resolved} />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">错误类型</CardTitle>
              </CardHeader>
              <CardContent>
                <ErrorTypeBadge errorType={record.error_type} />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">优先级</CardTitle>
              </CardHeader>
              <CardContent>
                <PriorityBadge priority={record.priority_level} />
              </CardContent>
            </Card>
          </div>

          {/* Price Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                价格信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-4">
                <div>
                  <Label className="text-sm font-medium">原价格</Label>
                  <div className="text-lg font-mono">
                    {record.original_price || '-'}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">报告价格</Label>
                  <div className="text-lg font-mono text-orange-600">
                    {record.reported_price || '-'}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">更正价格</Label>
                  <div className="text-lg font-mono text-green-600">
                    {record.corrected_price || '-'}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">财务影响</Label>
                  <div className={`text-lg font-mono ${record.financial_impact ? 'text-red-600' : ''}`}>
                    {record.financial_impact || '-'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timeline Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                时间轴
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">报告时间</div>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(record.reported_at)}
                  </div>
                </div>
              </div>
              
              {record.escalation_level > 0 && (
                <div className="flex items-center gap-3">
                  <TrendingUp className="h-4 w-4 text-orange-600" />
                  <div>
                    <div className="text-sm font-medium">升级级别</div>
                    <Badge variant="outline">Level {record.escalation_level}</Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions Section - Only show if not resolved */}
          {!record.is_resolved && (
            <>
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">审核操作</h3>
                
                {/* Action Reason */}
                <div>
                  <Label htmlFor="actionReason">操作原因</Label>
                  <Textarea
                    id="actionReason"
                    placeholder="请输入操作原因..."
                    value={actionReason}
                    onChange={(e) => setActionReason(e.target.value)}
                  />
                </div>

                {/* Corrected Price */}
                <div>
                  <Label htmlFor="correctedPrice">更正价格</Label>
                  <Input
                    id="correctedPrice"
                    type="number"
                    step="0.01"
                    placeholder="输入更正后的价格"
                    value={correctedPrice}
                    onChange={(e) => setCorrectedPrice(e.target.value)}
                  />
                </div>

                {/* Resolution */}
                <div>
                  <Label htmlFor="resolution">解决方案</Label>
                  <Textarea
                    id="resolution"
                    placeholder="请输入解决方案..."
                    value={resolution}
                    onChange={(e) => setResolution(e.target.value)}
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2">
                  <Button 
                    onClick={() => handlePerformAction('approved')}
                    disabled={isLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    批准
                  </Button>
                  
                  <Button 
                    onClick={() => handlePerformAction('rejected')}
                    disabled={isLoading}
                    variant="destructive"
                  >
                    <X className="mr-2 h-4 w-4" />
                    拒绝
                  </Button>
                  
                  <Button 
                    onClick={() => handlePerformAction('price_corrected')}
                    disabled={isLoading}
                    variant="outline"
                  >
                    <DollarSign className="mr-2 h-4 w-4" />
                    价格更正
                  </Button>
                  
                  <Button 
                    onClick={handleUpdateRecord}
                    disabled={isLoading}
                    variant="outline"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    更新记录
                  </Button>
                </div>

                {/* Priority Actions */}
                <div className="space-y-2">
                  <Label>设置优先级</Label>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleSetPriority('low')}
                      disabled={isLoading}
                    >
                      低
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleSetPriority('medium')}
                      disabled={isLoading}
                    >
                      中
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleSetPriority('high')}
                      disabled={isLoading}
                    >
                      高
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleSetPriority('critical')}
                      disabled={isLoading}
                    >
                      紧急
                    </Button>
                  </div>
                </div>

                {/* Escalation */}
                <div className="space-y-2">
                  <Label htmlFor="escalationReason">升级审核</Label>
                  <div className="flex gap-2">
                    <Input
                      id="escalationReason"
                      placeholder="输入升级原因..."
                      value={escalationReason}
                      onChange={(e) => setEscalationReason(e.target.value)}
                    />
                    <Button 
                      onClick={handleEscalate}
                      disabled={isLoading || !escalationReason.trim()}
                      variant="outline"
                    >
                      <TrendingUp className="mr-2 h-4 w-4" />
                      升级
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          {isLoading && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              处理中...
            </div>
          )}
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
