import React, { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Search, Plus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  getAllCraftsApiV1CraftsGetOptions,
  getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetOptions,
} from "@/services/@tanstack/react-query.gen";
import {
  MEASUREMENT_TYPES,
  REGISTRATION_TYPES,
} from "./EditableCraftRouteTable";
import type {
  CraftResponseDto,
  CraftRouteDetailDto,
} from "@/services/types.gen";

interface CraftsImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (
    selectedRoutes: { code: string, name: string, routes: CraftRouteDetailDto[] }[]
  ) => void;
}

// CraftCard 组件，用于显示单个工艺及其路线
const CraftCard: React.FC<{
  craft: CraftResponseDto;
  selectedRouteDtos: CraftRouteDetailDto[];
  onRouteToggle: (craftCode: string, data: CraftRouteDetailDto, checked: boolean) => void;
}> = ({ craft, selectedRouteDtos, onRouteToggle }) => {
  const [routeSearchTerm, setRouteSearchTerm] = useState("");

  // 获取工艺路线
  const { data: routesData, isLoading: routesLoading } = useQuery(
    getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetOptions({
      path: { craft_code: craft.code },
    })
  );

  const routes = useMemo(() => routesData?.routes || [], [routesData?.routes]);

  const selectedRoutes = useMemo(() => {
    const selectedSet = new Set(selectedRouteDtos.map(route => `${craft.code}-${route.id}`));
    return selectedSet;
  }, [craft.code, selectedRouteDtos]);

  // 筛选工艺路线
  const filteredRoutes = useMemo(() => {
    if (!routes.length) return [];

    if (!routeSearchTerm) return routes;

    return routes.filter(
      (route) =>
        route.skill_code
          .toLowerCase()
          .includes(routeSearchTerm.toLowerCase()) ||
        route.measurement_types?.some((type) =>
          type.toLowerCase().includes(routeSearchTerm.toLowerCase())
        ) ||
        route.registration_types?.some((type) =>
          type.toLowerCase().includes(routeSearchTerm.toLowerCase())
        )
    );
  }, [routes, routeSearchTerm]);

  return (
    <Card className="w-full h-fit min-h-[500px] flex flex-col gap-0">
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 flex-wrap">
            {craft.description ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <CardTitle className="text-base cursor-help truncate">
                    {craft.name}

                  </CardTitle>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{craft.description}</p>
                </TooltipContent>
              </Tooltip>
            ) : (
              <CardTitle className="text-base truncate">{craft.name}</CardTitle>
            )}
          </div>
          <div className="flex items-center space-x-2 flex-wrap">
            {routes.length > 0 && (
              <div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索路线..."
                    value={routeSearchTerm}
                    onChange={(e) => setRouteSearchTerm(e.target.value)}
                    className="pl-10 h-8 text-xs"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col">
        {/* 工艺路线搜索框 */}

        {/* 工艺路线列表 */}
        <div className={`flex-1 ${routes.length > 0 ? "border-t pt-2" : ""}`}>
          {routesLoading ? (
            <div className="text-sm text-muted-foreground py-4 text-center">
              加载中...
            </div>
          ) : routes.length === 0 ? (
            <div className="text-sm text-muted-foreground py-4 text-center">
              暂无工艺路线
            </div>
          ) : filteredRoutes.length === 0 ? (
            <div className="text-sm text-muted-foreground py-4 text-center">
              没有匹配的路线
            </div>
          ) : (
            <div className="space-y-2 max-h-[400px] overflow-y-auto">
              {filteredRoutes.map((route) => {
                const routeKey = `${craft.code}-${route.id}`;
                return (
                  <div
                    key={route.id}
                    className="flex items-start space-x-2 p-2 rounded border hover:bg-accent/50 text-xs"
                    onClick={() => {
                       onRouteToggle(craft.code, route, !selectedRoutes.has(routeKey))
                    }}
                  >
                    <Checkbox
                      id={`route-${routeKey}`}
                      checked={selectedRoutes.has(routeKey)}
                      onCheckedChange={(checked) =>
                        onRouteToggle(craft.code, route, checked as boolean)
                      }
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0 flex gap-2">
                      <div className="flex items-center space-x-1 mb-1">
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {route.order || 0}
                        </Badge>
                        <span className="font-medium truncate">
                          {route.skill_name || route.skill_code}
                        </span>
                      </div>
                      {route.measurement_types &&
                        route.measurement_types.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {route.measurement_types.map((type, idx) => (
                              <Badge
                                key={idx}
                                variant="default"
                                className="text-xs px-1 py-0"
                              >
                                {MEASUREMENT_TYPES.find((m) => m.value === type)
                                  ?.label || type}
                              </Badge>
                            ))}
                          </div>
                        )}
                      {route.registration_types &&
                        route.registration_types.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {route.registration_types.map((type, idx) => (
                              <Badge
                                key={idx}
                                variant="outline"
                                className="text-xs px-1 py-0"
                              >
                                {REGISTRATION_TYPES.find(
                                  (r) => r.value === type
                                )?.label || type}
                              </Badge>
                            ))}
                          </div>
                        )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const CraftsImportDialog: React.FC<CraftsImportDialogProps> = ({
  open,
  onOpenChange,
  onImport,
}) => {
  const [selectedRoutes, setSelectedRoutes] = useState<Record<string, CraftRouteDetailDto[]>>({});

  // 获取所有工艺
  const { data: craftsData, isLoading: craftsLoading } = useQuery(
    getAllCraftsApiV1CraftsGetOptions()
  );

  // 处理工艺路线选择
  const handleRouteSelection = (
    _craftCode: string,
    route: CraftRouteDetailDto,
    checked: boolean
  ) => {
    setSelectedRoutes(t => {
      let newSelected = [...(t[_craftCode] || [])];
      if (checked) {
        newSelected.push(route);
      } else {
        newSelected = newSelected.filter(c => c.id !== route.id);
      }
      return { ...t, [_craftCode]: newSelected };
    });
  };

  // 处理导入
  const handleImport = () => {
    const importData: {
      code: string;
      name: string;
      routes: CraftRouteDetailDto[];
    }[] = [];

    // 获取所有工艺，不管是否有选中的路线
    if (craftsData?.crafts) {
      craftsData.crafts.forEach((craft: CraftResponseDto) => {
        // 所有工艺都会被添加到导入数据中，路线为空数组
        // 这样调用方可以决定如何处理每个工艺
        importData.push({ 
          code: craft.code,
          name: craft.name,
          routes: selectedRoutes[craft.code] || []
        });
      });
    }

    onImport(importData);
    onOpenChange(false);

    // 重置状态
    setSelectedRoutes({});
  };

  return (
    <TooltipProvider>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-[95vw] w-[1400px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>导入工艺路线</DialogTitle>
            <DialogDescription>选择要导入到订单中的工艺路线</DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex flex-col">
            {/* 工艺卡片容器 */}
            <div className="flex-1 overflow-auto h-[600px]">
              {craftsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">加载中...</div>
                </div>
              ) : craftsData?.crafts.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">暂无工艺数据</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
                  {/* 工艺卡片 */}
                  {craftsData?.crafts.map((craft: CraftResponseDto) => (
                    <CraftCard
                      key={craft.code}
                      craft={craft}
                      selectedRouteDtos={selectedRoutes[craft.code] || []}
                      onRouteToggle={handleRouteSelection}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 底部操作栏 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-muted-foreground">
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button
                onClick={handleImport}
                disabled={!craftsData?.crafts || craftsData.crafts.length === 0}
              >
                <Plus className="w-4 h-4 mr-2" />
                导入所有工艺
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
};

export default CraftsImportDialog;
