import React, { useState } from 'react'
import { Card, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Plus, Trash2, Edit3, Save, X } from 'lucide-react'
import TemplateRouteCards from './TemplateRouteCards'
import TemplateRouteEditDialog from './TemplateRouteEditDialog'

interface CraftRoute {
  id: string
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  price: number
}

interface Craft {
  id: string
  craft_code: string
  craft_name: string
  order: number
  is_required: boolean
  estimated_duration_hours?: number
  craft_routes: CraftRoute[]
}

interface CraftTemplate {
  id: string
  code: string
  name: string
  part_type: string
  part_name: string
  description?: string
  crafts: Craft[]
  created_at: string
  updated_at: string
}

interface CraftTemplateDetailProps {
  template: CraftTemplate
}

const CraftTemplateDetail: React.FC<CraftTemplateDetailProps> = ({ template }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editedTemplate, setEditedTemplate] = useState<CraftTemplate>(template)
  const [editingRoute, setEditingRoute] = useState<{ craftIndex: number; route: CraftRoute } | null>(null)

  const handleEdit = () => {
    setIsEditing(true)
    setEditedTemplate({ ...template })
  }

  const handleSave = () => {
    // TODO: 实现保存模板的逻辑
    console.log('Save template:', editedTemplate)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditedTemplate(template)
  }

  const handleAddCraft = () => {
    const newCraft: Craft = {
      id: `craft-${Date.now()}`,
      craft_code: '',
      craft_name: '新工艺',
      order: editedTemplate.crafts.length + 1,
      is_required: false,
      estimated_duration_hours: 0,
      craft_routes: []
    }

    const updatedTemplate = { ...editedTemplate }
    updatedTemplate.crafts.push(newCraft)
    setEditedTemplate(updatedTemplate)
  }

  const handleRemoveCraft = (craftIndex: number) => {
    if (confirm('确定要删除这个工艺吗？')) {
      const updatedTemplate = { ...editedTemplate }
      updatedTemplate.crafts.splice(craftIndex, 1)
      setEditedTemplate(updatedTemplate)
    }
  }

  const handleAddRoute = (craftIndex: number) => {
    const newRoute: CraftRoute = {
      id: `route-${Date.now()}`,
      code: '',
      name: '',
      skill_code: '',
      skill_name: '',
      order: editedTemplate.crafts[craftIndex].craft_routes.length + 1,
      measurement_types: [],
      registration_types: [],
      price: 0
    }

    const updatedTemplate = { ...editedTemplate }
    updatedTemplate.crafts[craftIndex].craft_routes.push(newRoute)
    setEditedTemplate(updatedTemplate)
  }

  const handleEditRoute = (craftIndex: number, route: CraftRoute) => {
    setEditingRoute({ craftIndex, route })
  }

  const handleSaveRoute = (craftIndex: number, updatedRoute: CraftRoute) => {
    const updatedTemplate = { ...editedTemplate }
    const routeIndex = updatedTemplate.crafts[craftIndex].craft_routes.findIndex(r => r.id === updatedRoute.id)
    if (routeIndex !== -1) {
      updatedTemplate.crafts[craftIndex].craft_routes[routeIndex] = updatedRoute
      setEditedTemplate(updatedTemplate)
    }
    setEditingRoute(null)
  }

  const handleRemoveRoute = (craftIndex: number, routeId: string) => {
    const updatedTemplate = { ...editedTemplate }
    updatedTemplate.crafts[craftIndex].craft_routes = updatedTemplate.crafts[craftIndex].craft_routes.filter(
      route => route.id !== routeId
    )
    setEditedTemplate(updatedTemplate)
  }

  const currentTemplate = isEditing ? editedTemplate : template

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {isEditing ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">模板代码</label>
                    <Input
                      value={editedTemplate.code}
                      onChange={(e) => setEditedTemplate({ ...editedTemplate, code: e.target.value })}
                      placeholder="模板代码"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">模板名称</label>
                    <Input
                      value={editedTemplate.name}
                      onChange={(e) => setEditedTemplate({ ...editedTemplate, name: e.target.value })}
                      placeholder="模板名称"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-1 block">描述</label>
                  <Textarea
                    value={editedTemplate.description || ''}
                    onChange={(e) => setEditedTemplate({ ...editedTemplate, description: e.target.value })}
                    placeholder="模板描述"
                    rows={2}
                  />
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-xl font-bold">{currentTemplate.name}</h1>
                  <Badge variant="outline">{currentTemplate.code}</Badge>
                  <Badge variant="secondary">{currentTemplate.part_name}</Badge>
                </div>
                {currentTemplate.description && (
                  <p className="text-muted-foreground">{currentTemplate.description}</p>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {isEditing ? (
              <>
                <Button onClick={handleSave} size="sm">
                  <Save className="w-4 h-4 mr-2" />
                  保存
                </Button>
                <Button onClick={handleCancel} size="sm" variant="outline">
                  <X className="w-4 h-4 mr-2" />
                  取消
                </Button>
              </>
            ) : (
              <Button onClick={handleEdit} size="sm" variant="outline">
                <Edit3 className="w-4 h-4 mr-2" />
                编辑
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-6xl mx-auto space-y-4">
          {/* Crafts Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">工艺配置</h2>
            <Button onClick={handleAddCraft} size="sm" variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              添加工艺
            </Button>
          </div>

          {/* Crafts List */}
          {currentTemplate.crafts.length === 0 ? (
            <div className="text-center py-12 border border-dashed rounded-lg">
              <p className="text-muted-foreground mb-4">
                该模板暂无工艺配置
              </p>
              <Button onClick={handleAddCraft} variant="outline">
                添加第一个工艺
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {currentTemplate.crafts.map((craft, craftIndex) => (
                <Card key={craft.id} className="border-l-4 border-l-green-500">
                  <CardHeader className="px-4 py-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-base">{craft.craft_name}</CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          {craft.craft_code} • 顺序: {craft.order}
                          {craft.estimated_duration_hours && (
                            <span> • 预计时长: {craft.estimated_duration_hours}小时</span>
                          )}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={craft.is_required ? "default" : "secondary"} className="text-xs">
                          {craft.is_required ? "必需" : "可选"}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {craft.craft_routes.length} 路线
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveCraft(craftIndex)}
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 pb-4 pt-0">
                    {/* Craft Routes Display */}
                    <TemplateRouteCards
                      routes={currentTemplate.crafts[craftIndex].craft_routes}
                      onAddRoute={() => handleAddRoute(craftIndex)}
                      onEditRoute={(route) => handleEditRoute(craftIndex, route)}
                      onRemoveRoute={(routeId) => handleRemoveRoute(craftIndex, routeId)}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Edit Route Dialog */}
      {editingRoute && (
        <TemplateRouteEditDialog
          route={editingRoute.route}
          open={!!editingRoute}
          onOpenChange={(open: boolean) => {
            if (!open) {
              setEditingRoute(null)
            }
          }}
          onSave={(updatedRoute: CraftRoute) => handleSaveRoute(editingRoute.craftIndex, updatedRoute)}
        />
      )}
    </div>
  )
}

export default CraftTemplateDetail
