import React, { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Settings, Loader2, Play } from 'lucide-react'
import { RegistrationDialog } from './RegistrationDialog'
import { CraftCard } from './CraftCard'
import { startOrderApiV1OrdersOrderNoStartPostMutation } from '@/services/@tanstack/react-query.gen'
import type { OrderCraftResponseDto, OrderCraftRouteResponseDto } from '@/services/types.gen'
import { toast } from 'sonner'

interface OrderCraftStepsProps {
  orderCrafts: OrderCraftResponseDto[]
  orderNo?: string // 订单号，用于获取分床分扎数据
  orderStatus?: string // 订单状态，用于显示开始按钮
  showEditButton?: boolean
  onShowEdit?: () => void
}

// 简化的登记数据接口（用于向后兼容）
interface RegistrationData {
  routeId: number
  registrationType: string
  unitIdentifier: string
  orderPartNos: string[]
  orderBundleNos: string[]
  completionPerson: string
  startTime: string
  completionTime: string
  notes: string
}

export const OrderCraftSteps: React.FC<OrderCraftStepsProps> = ({
  orderCrafts,
  orderNo,
  orderStatus,
  showEditButton = false,
  onShowEdit,
}) => {
  const queryClient = useQueryClient()
  
  // 登记相关状态
  const [registrationDialogOpen, setRegistrationDialogOpen] = useState(false)
  const [registrationData, setRegistrationData] = useState<RegistrationData>({
    routeId: 0,
    registrationType: '',
    unitIdentifier: '',
    orderPartNos: [],
    orderBundleNos: [],
    completionPerson: '',
    startTime: '',
    completionTime: '',
    notes: ''
  })
  
  // 开始订单的 mutation
  const startOrderMutation = useMutation({
    ...startOrderApiV1OrdersOrderNoStartPostMutation(),
    onSuccess: () => {
      toast.success('订单已开始')
      // 刷新相关数据
      queryClient.invalidateQueries({
        queryKey: ['api', 'v1', 'orders', orderNo]
      })
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.length > 0 &&
                 typeof queryKey[0] === 'object' &&
                 queryKey[0] !== null &&
                 '_id' in queryKey[0] &&
                 (queryKey[0]._id === 'getOrderByOrderNoMainApiV1OrdersOrderNoGet' ||
                  queryKey[0]._id === 'getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet')
        }
      })
    },
    onError: (error) => {
      console.error('开始订单失败:', error)
      toast.error('开始订单失败')
    }
  })

  // 开始订单
  const handleStartOrder = () => {
    if (!orderNo) {
      toast.error('订单号不能为空')
      return
    }

    startOrderMutation.mutate({
      path: { order_no: orderNo }
    })
  }

  // 开始登记
  const startRegistration = (route: OrderCraftRouteResponseDto) => {
    const now = new Date()
    const currentDateTime = now.toISOString().slice(0, 16) // YYYY-MM-DDTHH:mm format
    
    setRegistrationData({
      routeId: route.id,
      registrationType: route.registration_types?.[0] || '', // 默认选择第一个登记类型
      unitIdentifier: '',
      orderPartNos: [],
      orderBundleNos: [],
      completionPerson: '',
      startTime: currentDateTime,
      completionTime: currentDateTime,
      notes: ''
    })
    setRegistrationDialogOpen(true)
  }

  // 提交登记
  const submitRegistration = async () => {
    try {
      // 这里应该调用API提交登记数据
      console.log('提交登记数据:', registrationData)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 成功后关闭对话框并重置数据
      setRegistrationDialogOpen(false)
      setRegistrationData({
        routeId: 0,
        registrationType: '',
        unitIdentifier: '',
        orderPartNos: [],
        orderBundleNos: [],
        completionPerson: '',
        startTime: '',
        completionTime: '',
        notes: ''
      })
      
      // 这里可以添加成功提示
      console.log('登记成功')
    } catch (error) {
      console.error('登记失败:', error)
    }
  }

  // 取消登记
  const cancelRegistration = () => {
    setRegistrationDialogOpen(false)
    setRegistrationData({
      routeId: 0,
      registrationType: '',
      unitIdentifier: '',
      orderPartNos: [],
      orderBundleNos: [],
      completionPerson: '',
      startTime: '',
      completionTime: '',
      notes: ''
    })
  }

  // 获取当前选择路线的信息
  const getSelectedRoute = (): OrderCraftRouteResponseDto | undefined => {
    for (const craft of orderCrafts) {
      const route = craft.order_craft_routes?.find(r => r.id === registrationData.routeId)
      if (route) return route
    }
    return undefined
  }
  
  // 按顺序排序工艺
  const sortedCrafts = [...orderCrafts].sort((a, b) => a.order - b.order)

  if (!orderCrafts || orderCrafts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              工艺流程
            </CardTitle>
            {showEditButton && (
              <Button size="sm" variant="outline" onClick={onShowEdit}>
                添加工艺
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">暂无工艺配置</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            工艺流程
          </CardTitle>
        <div className="flex items-center gap-2">
          {/* 开始订单按钮 - 仅在订单状态为 pending 时显示 */}
          {orderStatus === 'pending' && orderNo && (
            <Button
              size="sm"
              variant="default"
              onClick={handleStartOrder}
              disabled={startOrderMutation.isPending}
              className="bg-green-600 hover:bg-green-700"
            >
              {startOrderMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  开始中...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  开始订单
                </>
              )}
            </Button>
          )}

          {showEditButton && (
            <Button size="sm" variant="outline" onClick={onShowEdit}>
              编辑工艺
            </Button>
          )}
        </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* 水平滑动的工艺步骤卡片 */}
        <div className="relative">
          <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-thin">
            {sortedCrafts.map((craft) => (
              <CraftCard
                key={craft.id}
                craft={craft}
                orderNo={orderNo}
                onStartRegistration={startRegistration}
              />
            ))}
          </div>
        </div>
      </CardContent>
      
      {/* 登记对话框 */}
      <RegistrationDialog
        open={registrationDialogOpen}
        onOpenChange={setRegistrationDialogOpen}
        selectedRoute={getSelectedRoute()}
        orderNo={orderNo}
        onSubmit={submitRegistration}
        onCancel={cancelRegistration}
      />
    </Card>
  )
}
