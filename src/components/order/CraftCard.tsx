import React from 'react'
import { Badge } from '@/components/ui/badge'
import { CraftRouteItem } from './CraftRouteItem'
import type { OrderCraftResponseDto, OrderCraftRouteResponseDto } from '@/services/types.gen'

interface CraftCardProps {
  craft: OrderCraftResponseDto
  orderNo?: string
  onStartRegistration: (route: OrderCraftRouteResponseDto) => void
}

// 根据状态确定步骤状态
const getCraftStatus = (craft: OrderCraftResponseDto): 'pending' | 'current' | 'completed' => {
  if (craft.completed_at) return 'completed'
  if (craft.started_at) return 'current'
  return 'pending'
}

// 根据状态确定工艺路线状态
const getRouteStatus = (route: OrderCraftRouteResponseDto): 'pending' | 'current' | 'completed' => {
  if (route.completed_at) return 'completed'
  if (route.started_at) return 'current'
  return 'pending'
}

export const CraftCard: React.FC<CraftCardProps> = ({
  craft,
  orderNo,
  onStartRegistration
}) => {
  const status = getCraftStatus(craft)
  const routes = craft.order_craft_routes || []

  return (
    <div
      className={`w-[500px] flex-shrink-0 border rounded-lg p-4 transition-colors ${
        status === 'completed' ? 'bg-green-50 border-green-200' : 
        status === 'current' ? 'bg-blue-50 border-blue-200' : 
        'bg-gray-50 border-gray-200'
      }`}
    >
      <div className="space-y-4">
        {/* 工艺头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
              status === 'completed' ? 'bg-green-500' : 
              status === 'current' ? 'bg-blue-500' : 
              'bg-gray-400'
            }`}>
              {craft.order}
            </div>
            <div>
              <h3 className="text-lg font-semibold">
                {craft.craft_name || craft.craft_code}
              </h3>
              <div className="flex items-center gap-1 mt-1">
                <Badge variant="outline" className="text-xs">{craft.craft_code}</Badge>
                {craft.is_required && <Badge variant="secondary" className="text-xs">必需</Badge>}
              </div>
            </div>
          </div>
          <Badge variant={
            status === 'completed' ? 'default' : 
            status === 'current' ? 'secondary' : 
            'outline'
          }>
            {status === 'completed' ? '已完成' : 
             status === 'current' ? '进行中' : 
             '待开始'}
          </Badge>
        </div>

        {/* 工序进度 */}
        {routes.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>工序进度</span>
              <span>
                {routes.filter(r => getRouteStatus(r) === 'completed').length}/{routes.length} 
                ({Math.round((routes.filter(r => getRouteStatus(r) === 'completed').length / routes.length) * 100)}%)
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  status === 'completed' ? 'bg-green-500' : 
                  status === 'current' ? 'bg-blue-500' : 
                  'bg-gray-400'
                }`}
                style={{ 
                  width: `${(routes.filter(r => getRouteStatus(r) === 'completed').length / routes.length) * 100}%` 
                }}
              />
            </div>
          </div>
        )}

        {/* 基本信息 */}
        <div className="grid grid-cols-1 gap-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">工序顺序:</span>
            <span className="font-medium">第 {craft.order} 道</span>
          </div>
          {craft.estimated_duration_hours && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">预计时长:</span>
              <span className="font-medium">{craft.estimated_duration_hours}小时</span>
            </div>
          )}
          {craft.actual_duration_hours && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">实际时长:</span>
              <span className="font-medium">{craft.actual_duration_hours}小时</span>
            </div>
          )}
          {craft.completion_percentage !== undefined && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">完成度:</span>
              <span className="font-medium">{craft.completion_percentage}%</span>
            </div>
          )}
        </div>

        {/* 时间信息 */}
        {(craft.started_at || craft.completed_at) && (
          <div className="space-y-1 text-xs text-muted-foreground">
            {craft.started_at && (
              <div>开始: {new Date(craft.started_at).toLocaleString()}</div>
            )}
            {craft.completed_at && (
              <div>完成: {new Date(craft.completed_at).toLocaleString()}</div>
            )}
          </div>
        )}

        {/* 备注 */}
        {craft.notes && (
          <div className="text-sm">
            <span className="text-muted-foreground">备注:</span>
            <span className="ml-2">{craft.notes}</span>
          </div>
        )}

        {/* 工艺路线详情 */}
        {routes.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">工序详情</h4>
            <div className="space-y-2">
              {routes
                .sort((a, b) => a.order - b.order)
                .map((route) => (
                  <CraftRouteItem
                    key={route.id}
                    route={route}
                    orderNo={orderNo}
                    onStartRegistration={onStartRegistration}
                  />
                ))
              }
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
