import React, { useState } from 'react'
import { useForm<PERSON>onte<PERSON><PERSON>, Controller } from 'react-hook-form'
import { TableCell, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Edit2, X, Trash2, Check } from 'lucide-react'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'

interface OrderCraftRouteRowProps {
  craftIndex: number
  routeIndex: number
}

const OrderCraftRouteRow: React.FC<OrderCraftRouteRowProps> = ({
  craftIndex,
  routeIndex
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const { control, setValue, getValues } = useFormContext()
  
  const routeFieldName = `order_crafts.${craftIndex}.order_craft_routes.${routeIndex}`

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = () => {
    setIsEditing(false)
  }

  const handleCancel = () => {
    setIsEditing(false)
  }

  const handleDelete = () => {
    const crafts = getValues('order_crafts')
    const updatedRoutes = crafts[craftIndex].order_craft_routes.filter((_: unknown, index: number) => index !== routeIndex)
    setValue(`order_crafts.${craftIndex}.order_craft_routes`, updatedRoutes)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  const toggleMeasurementType = (type: string) => {
    const current = getValues(`${routeFieldName}.measurement_types`) || []
    const updated = current.includes(type)
      ? current.filter((t: string) => t !== type)
      : [...current, type]
    setValue(`${routeFieldName}.measurement_types`, updated)
  }

  const toggleRegistrationType = (type: string) => {
    const current = getValues(`${routeFieldName}.registration_types`) || []
    const updated = current.includes(type)
      ? current.filter((t: string) => t !== type)
      : [...current, type]
    setValue(`${routeFieldName}.registration_types`, updated)
  }

  if (isEditing) {
    return (
      <TableRow>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.order`}
            render={({ field }) => (
              <Input
                type="number"
                min="1"
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                onKeyDown={handleKeyDown}
                className="w-16"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`order_crafts.${craftIndex}.order_craft_routes.${routeIndex}.code`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                className="font-mono text-sm"
                placeholder="工艺代码"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`order_crafts.${craftIndex}.order_craft_routes.${routeIndex}.name`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                placeholder="工艺名称"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.skill_code`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                className="font-mono text-sm"
                placeholder="技能代码"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.skill_name`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                placeholder="技能名称"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.measurement_types`}
            render={({ field }) => (
              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {MEASUREMENT_TYPES.map((type) => (
                    <Badge
                      key={type.value}
                      variant={(field.value || []).includes(type.value) ? "default" : "outline"}
                      className="cursor-pointer text-xs"
                      onClick={() => toggleMeasurementType(type.value)}
                    >
                      {type.label}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.registration_types`}
            render={({ field }) => (
              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {REGISTRATION_TYPES.map((type) => (
                    <Badge
                      key={type.value}
                      variant={(field.value || []).includes(type.value) ? "default" : "outline"}
                      className="cursor-pointer text-xs"
                      onClick={() => toggleRegistrationType(type.value)}
                    >
                      {type.label}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.price`}
            render={({ field }) => (
              <Input
                type="number"
                min="0"
                step="0.01"
                {...field}
                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                onKeyDown={handleKeyDown}
                className="w-24"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleSave}
              className="text-green-600 hover:text-green-700"
            >
              <Check className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCancel}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    )
  }

  return (
    <TableRow>
      <TableCell>
        <Controller
          control={control}
          name={`${routeFieldName}.order`}
          render={({ field }) => (
            <Badge variant="outline">{field.value}</Badge>
          )}
        />
      </TableCell>
      <TableCell className="font-mono text-sm">
        <Controller
          control={control}
          name={`${routeFieldName}.code`}
          render={({ field }) => (
            <span>{field.value}</span>
          )}
        />
      </TableCell>
      <TableCell>
        <Controller
          control={control}
          name={`${routeFieldName}.name`}
          render={({ field }) => (
            <span>{field.value}</span>
          )}
        />
      </TableCell>
      <TableCell className="font-mono text-sm">
        <Controller
          control={control}
          name={`${routeFieldName}.skill_code`}
          render={({ field }) => (
            <span>{field.value}</span>
          )}
        />
      </TableCell>
      <TableCell>
        <Controller
          control={control}
          name={`${routeFieldName}.skill_name`}
          render={({ field }) => (
            <span>{field.value}</span>
          )}
        />
      </TableCell>
      <TableCell>
        <Controller
          control={control}
          name={`${routeFieldName}.measurement_types`}
          render={({ field }) => (
            <div className="flex flex-wrap gap-1">
              {(field.value || []).map((type: string, idx: number) => (
                <Badge key={idx} variant="default" className="text-xs">
                  {MEASUREMENT_TYPES.find(m => m.value === type)?.label || type}
                </Badge>
              ))}
            </div>
          )}
        />
      </TableCell>
      <TableCell>
        <Controller
          control={control}
          name={`${routeFieldName}.registration_types`}
          render={({ field }) => (
            <div className="flex flex-wrap gap-1">
              {(field.value || []).map((type: string, idx: number) => (
                <Badge key={idx} variant="outline" className="text-xs">
                  {REGISTRATION_TYPES.find(r => r.value === type)?.label || type}
                </Badge>
              ))}
            </div>
          )}
        />
      </TableCell>
      <TableCell>
        <Controller
          control={control}
          name={`${routeFieldName}.price`}
          render={({ field }) => (
            <span className="font-mono">¥{(field.value || 0).toFixed(2)}</span>
          )}
        />
      </TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleEdit}
          >
            <Edit2 className="w-4 h-4" />
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDelete}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  )
}

export default OrderCraftRouteRow