import React from 'react'
import { useFormContext, useFieldArray } from 'react-hook-form'
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import OrderCraftRouteRow from './OrderCraftRouteRow'

interface OrderCraftRouteTableProps {
  craftIndex: number
}

const OrderCraftRouteTable: React.FC<OrderCraftRouteTableProps> = ({
  craftIndex
}) => {
  const { control } = useFormContext()
  const { fields } = useFieldArray({
    control,
    name: `order_crafts.${craftIndex}.order_craft_routes`
  })

  if (fields.length === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center py-4">
        该工艺暂无路线配置
      </p>
    )
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[80px]">顺序</TableHead>
            <TableHead>工艺代码</TableHead>
            <TableHead>工艺名称</TableHead>
            <TableHead>技能代码</TableHead>
            <TableHead>技能名称</TableHead>
            <TableHead>计价方式</TableHead>
            <TableHead>登记方式</TableHead>
            <TableHead className="w-[120px]">价格(元)</TableHead>
            <TableHead className="w-[100px]">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {fields.map((route, routeIndex) => (
            <OrderCraftRouteRow
              key={route.id}
              craftIndex={craftIndex}
              routeIndex={routeIndex}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

export default OrderCraftRouteTable