import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Plus, Edit2, Trash2, Settings } from 'lucide-react'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'
import type { OrderCraftResponseDto, OrderCraftRouteResponseDto } from '@/services'

interface OrderCraftsProps {
  orderCrafts: OrderCraftResponseDto[]
  // onUpdate?: (crafts: OrderCraftResponseDto[]) => Promise<void> // TODO: 实现工艺更新功能
  editable?: boolean
}

export const OrderCrafts: React.FC<OrderCraftsProps> = ({
  orderCrafts,
  editable = true
}) => {
  const [isEditing, setIsEditing] = useState(false)

  if (!orderCrafts || orderCrafts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              工艺配置
            </CardTitle>
            {editable && (
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                添加工艺
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">暂无工艺配置</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          工艺配置
        </h3>
        {editable && !isEditing && (
          <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
            <Edit2 className="w-4 h-4 mr-2" />
            编辑工艺
          </Button>
        )}
      </div>

      <Accordion type="single" collapsible className="space-y-4">
        {orderCrafts.map((craft, craftIndex) => (
          <AccordionItem key={craftIndex} value={`craft-${craftIndex}`} className="border rounded-lg">
            <AccordionTrigger className="px-4 hover:no-underline">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-3">
                  <span className="font-medium">{craft.craft_name}</span>
                  <Badge variant="outline">{craft.craft_code}</Badge>
                  <Badge variant={craft.is_required ? 'default' : 'secondary'}>
                    {craft.is_required ? '必需' : '可选'}
                  </Badge>
                </div>
                {craft.estimated_duration_hours && (
                  <Badge variant="outline">
                    预计 {craft.estimated_duration_hours}h
                  </Badge>
                )}
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-4">
              <CraftRoutes 
                routes={craft.order_craft_routes || []} 
                editable={editable && isEditing}
              />
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}

// 工艺路线组件
const CraftRoutes: React.FC<{
  routes: OrderCraftRouteResponseDto[]
  editable: boolean
}> = ({ routes, editable }) => {
  if (!routes || routes.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-muted-foreground">该工艺暂无路线配置</p>
        {editable && (
          <Button size="sm" className="mt-2">
            <Plus className="w-4 h-4 mr-2" />
            添加路线
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[80px]">顺序</TableHead>
            <TableHead>技能代码</TableHead>
            <TableHead>技能名称</TableHead>
            <TableHead>计价方式</TableHead>
            <TableHead>登记方式</TableHead>
            <TableHead>指定用户</TableHead>
            <TableHead className="w-[120px]">价格(元)</TableHead>
            {editable && <TableHead className="w-[100px]">操作</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {routes.map((route, routeIndex) => (
            <TableRow key={routeIndex}>
              <TableCell>
                <Badge variant="outline">{route.order}</Badge>
              </TableCell>
              <TableCell className="font-mono text-sm">
                {route.skill_code}
              </TableCell>
              <TableCell>{route.skill_name || route.skill_code}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {route.measurement_types?.map((type: string, idx: number) => (
                    <Badge key={idx} variant="default" className="text-xs">
                      {MEASUREMENT_TYPES.find(m => m.value === type)?.label || type}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {route.registration_types?.map((type: string, idx: number) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {REGISTRATION_TYPES.find(r => r.value === type)?.label || type}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                {route.assigned_user_id ? `用户 ${route.assigned_user_id}` : '-'}
              </TableCell>
              <TableCell>
                <span className="font-mono">¥{route?.price}</span>
              </TableCell>
              {editable && (
                <TableCell>
                  <div className="flex gap-1">
                    <Button size="sm" variant="ghost">
                      <Edit2 className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Trash2 className="w-3 h-3 text-destructive" />
                    </Button>
                  </div>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
