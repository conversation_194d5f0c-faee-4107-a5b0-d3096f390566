import React, { useState } from 'react'
import { useF<PERSON>, FormProvider, useFieldArray } from 'react-hook-form'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Trash2, Target, Save } from 'lucide-react'
import CraftsImportDialog from '@/components/CraftsImportDialog'
import OrderCraftRouteTable from './OrderCraftRouteTable'
import { createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostMutation } from '@/services/@tanstack/react-query.gen'
import { toast } from 'sonner'
import type { CraftRouteDetailDto, OrderCraftResponseDto } from '@/services/types.gen'

export interface OrderCraftRoute {
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  assigned_user_id?: number | null
  price: number
  is_required?: boolean
}

export interface OrderCraft {
  craft_code: string
  craft_name: string
  order: number
  is_required: boolean
  estimated_duration_hours?: number
  order_craft_routes: OrderCraftRoute[]
}

interface OrderCraftsEditProps {
  orderNo: string
  orderCrafts?: OrderCraftResponseDto[]
  extraButtons?: React.ReactNode
}

const OrderCraftsEdit: React.FC<OrderCraftsEditProps> = ({ 
  orderNo,
  orderCrafts = [],
  extraButtons
}) => {
  const [showImportDialog, setShowImportDialog] = useState(false)
  const queryClient = useQueryClient()

  // 创建订单工艺的 mutation
  const createOrderCraftsMutation = useMutation({
    ...createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostMutation(),
    onSuccess: () => {
      toast.success('工艺配置已保存')
      // 刷新订单数据和工艺数据
      queryClient.invalidateQueries({ 
        queryKey: ['api', 'v1', 'orders', orderNo] 
      })
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.length > 0 && 
                 typeof queryKey[0] === 'object' &&
                 queryKey[0] !== null &&
                 '_id' in queryKey[0] &&
                 queryKey[0]._id === 'getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet' &&
                 'path' in queryKey[0] &&
                 typeof queryKey[0].path === 'object' &&
                 queryKey[0].path !== null &&
                 'order_no' in queryKey[0].path &&
                 queryKey[0].path.order_no === orderNo
        }
      })
    },
    onError: (error) => {
      console.error('Create order crafts failed:', error)
      toast.error('保存失败: ' + error.message)
    }
  })

  // 转换现有的 order_crafts 为表单数据格式
  const convertToFormData = (crafts: OrderCraftResponseDto[]): OrderCraft[] => {
    return crafts.map(craft => ({
      craft_code: craft.craft_code,
      craft_name: craft.craft_name || craft.craft_code,
      order: craft.order,
      is_required: craft.is_required,
      estimated_duration_hours: craft.estimated_duration_hours || 0,
      order_craft_routes: (craft.order_craft_routes || []).map(route => ({
        code: route.code || route.skill_code,
        name: route.name || route.code || route.skill_code,
        skill_code: route.skill_code,
        skill_name: route.skill_name || route.skill_code,
        order: route.order,
        measurement_types: route.measurement_types || [],
        registration_types: route.registration_types || [],
        assigned_user_id: route.assigned_user_id,
        price: typeof route.price === 'string' ? parseFloat(route.price) || 0 : route.price || 0,
      }))
    }))
  }

  const form = useForm({
    defaultValues: {
      order_crafts: convertToFormData(orderCrafts)
    }
  })

  const { fields, remove } = useFieldArray({
    control: form.control,
    name: "order_crafts"
  })

  const addOrderCraft = () => {
    setShowImportDialog(true)
  }

  // 删除工艺
  const removeCraft = (craftIndex: number) => {
    remove(craftIndex)
  }

  // 保存工艺配置
  const handleSave = form.handleSubmit((formData) => {
    const orderCraftsData = {
      path: { order_no: orderNo },
      body: {
        order_crafts: formData.order_crafts.map(craft => ({
          craft_code: craft.craft_code,
          craft_name: craft.craft_name,
          order: craft.order,
          is_required: craft.is_required,
          estimated_duration_hours: craft.estimated_duration_hours,
          order_craft_routes: craft.order_craft_routes.map(route => ({
            skill_code: route.skill_code,
            name: route.name,
            code: route.code,
            order: route.order,
            measurement_types: route.measurement_types,
            registration_types: route.registration_types,
            is_required: route.is_required || false,
            estimated_duration_minutes: null,
            assigned_user_id: route.assigned_user_id,
            price: route.price,
            total_cost: null,
            notes: null,
          }))
        }))
      }
    }

    createOrderCraftsMutation.mutate(orderCraftsData)
  })
  const handleImportCrafts = (importedData: { code: string; name: string; routes: CraftRouteDetailDto[] }[]) => {
    console.log('Importing crafts:', importedData)
    
    // 获取当前表单的工艺数据
    const currentCrafts = form.getValues('order_crafts')
    
    // 合并导入的工艺数据
    const newCrafts: OrderCraft[] = []
    
    // 保留现有的工艺
    newCrafts.push(...currentCrafts)
    
    // 添加新导入的工艺
    importedData.forEach(({ code, name, routes }) => {
      // 检查是否已存在
      const exists = newCrafts.some(craft => craft.craft_code === code)
      if (!exists) {
        const newCraft: OrderCraft = {
          craft_code: code,
          craft_name: name,
          order: newCrafts.length + 1,
          is_required: true,
          estimated_duration_hours: 0,
          order_craft_routes: routes.map((route): OrderCraftRoute => ({
            code: route.code || route.skill_code,
            name: route.name || route.code || route.skill_code,
            skill_code: route.skill_code,
            skill_name: route.skill_name || route.skill_code,
            order: route.order,
            measurement_types: route.measurement_types || [],
            registration_types: route.registration_types || [],
            assigned_user_id: undefined,
            price: 0, // 默认价格为0，后续可编辑
          }))
        }
        newCrafts.push(newCraft)
      }
    })
    
    // 使用 form.reset 重新设置表单数据
    form.reset({
      order_crafts: newCrafts
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            工艺路线配置
          </CardTitle>
          <div className="flex gap-2">
            <Button onClick={addOrderCraft} size="sm" variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              添加工艺
            </Button>
            <Button 
              onClick={handleSave} 
              size="sm" 
              disabled={createOrderCraftsMutation.isPending}
            >
              <Save className="w-4 h-4 mr-2" />
              {createOrderCraftsMutation.isPending ? '保存中...' : '保存配置'}
            </Button>
            {extraButtons}
          </div>
        </CardHeader>
        <CardContent>
          <FormProvider {...form}>
            {fields.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">暂无工艺配置</p>
                <Button onClick={addOrderCraft}>
                  <Target className="w-4 h-4 mr-2" />
                  配置工艺路线
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {fields.map((craft, craftIndex) => {
                  const craftData = craft as unknown as OrderCraft & { id: string }
                  return (
                    <div key={craftData.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-lg">{craftData.craft_name}</h4>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => removeCraft(craftIndex)}
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          删除工艺
                        </Button>
                      </div>
                      
                      <OrderCraftRouteTable craftIndex={craftIndex} />
                    </div>
                  )
                })}
              </div>
            )}
          </FormProvider>
        </CardContent>
      </Card>

      <CraftsImportDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
        onImport={handleImportCrafts}
      />
    </>
  )
}

export default OrderCraftsEdit
