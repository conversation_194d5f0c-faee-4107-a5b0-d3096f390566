import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Settings } from 'lucide-react'
import type { 
  OrderPartResponseDto, 
  OrderBundleResponseDto, 
  OrderLineResponseDto
} from '@/services/types.gen'
import { 
  searchOrderPartsApiV1OrderPartsGetOptions,
  searchOrderBundlesApiV1OrderBundlesGetOptions 
} from '@/services/@tanstack/react-query.gen'

// Import split components
import { OrderConfigurationSection } from './parts/OrderConfigurationSection'
import { OrderPartsSection } from './parts/OrderPartsSection'

interface OrderPartsAndBundlesProps {
  orderLines: OrderLineResponseDto[] // 订单行数据（尺码需求）
  orderNo: string  // 订单号
  skcNo: string    // SKC号
  onUpdateParts?: (parts: OrderPartResponseDto[]) => Promise<void>
  onUpdateBundles?: (bundles: OrderBundleResponseDto[]) => Promise<void>
  editable?: boolean
}

export const OrderPartsAndBundles: React.FC<OrderPartsAndBundlesProps> = ({
  orderLines = [],
  orderNo,
  skcNo,
  onUpdateParts,
  onUpdateBundles,
  editable = true
}) => {
  // Fetch order parts for this specific order
  const { data: orderPartsResponse, isLoading: isLoadingParts } = useQuery({
    ...searchOrderPartsApiV1OrderPartsGetOptions({
      query: {
        order_no: orderNo,
        limit: 1000 // Get all parts for this order
      }
    }),
    enabled: !!orderNo
  })

  // Fetch order bundles for this specific order
  const { data: orderBundlesResponse, isLoading: isLoadingBundles, refetch: refetchBundles } = useQuery({
    ...searchOrderBundlesApiV1OrderBundlesGetOptions({
      query: {
        order_no: orderNo,
        limit: 1000 // Get all bundles for this order
      }
    }),
    enabled: !!orderNo
  })

  const orderParts = orderPartsResponse?.order_parts || []
  const fetchedOrderBundles = orderBundlesResponse?.order_bundles || []

  const handleBundlesUpdate = async () => {
    await refetchBundles()
    if (onUpdateBundles) {
      await onUpdateBundles(fetchedOrderBundles)
    }
  }

  if (isLoadingParts || isLoadingBundles) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            分床分扎配置
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-muted-foreground">加载中...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 配置总览 */}
      <OrderConfigurationSection 
        orderLines={orderLines}
        orderBundles={fetchedOrderBundles}
      />

      {/* 床管理 */}
      <OrderPartsSection 
        orderLines={orderLines}
        parts={orderParts} 
        existingBundles={fetchedOrderBundles}
        orderNo={orderNo}
        skcNo={skcNo}
        onUpdate={onUpdateParts}
        onBundlesUpdate={handleBundlesUpdate}
        editable={editable}
      />

    </div>
  )
}
