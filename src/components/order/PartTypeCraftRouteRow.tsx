import React, { useState } from 'react'
import { useForm<PERSON>ontex<PERSON>, Controller } from 'react-hook-form'
import { TableCell, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Edit2, X, Trash2, Check } from 'lucide-react'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'

interface PartTypeCraftRouteRowProps {
  partTypeIndex: number
  craftIndex: number
  routeIndex: number
}

const PartTypeCraftRouteRow: React.FC<PartTypeCraftRouteRowProps> = ({
  partTypeIndex,
  craftIndex,
  routeIndex
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const { control, setValue, getValues } = useFormContext()
  
  const routeFieldName = `part_types.${partTypeIndex}.order_crafts.${craftIndex}.order_craft_routes.${routeIndex}`

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = () => {
    setIsEditing(false)
  }

  const handleCancel = () => {
    setIsEditing(false)
  }

  const handleDelete = () => {
    const partTypes = getValues('part_types')
    const updatedRoutes = partTypes[partTypeIndex].order_crafts[craftIndex].order_craft_routes.filter((_: unknown, index: number) => index !== routeIndex)
    setValue(`part_types.${partTypeIndex}.order_crafts.${craftIndex}.order_craft_routes`, updatedRoutes)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  const toggleMeasurementType = (type: string) => {
    const current = getValues(`${routeFieldName}.measurement_types`) || []
    const updated = current.includes(type)
      ? current.filter((t: string) => t !== type)
      : [...current, type]
    setValue(`${routeFieldName}.measurement_types`, updated)
  }

  const toggleRegistrationType = (type: string) => {
    const current = getValues(`${routeFieldName}.registration_types`) || []
    const updated = current.includes(type)
      ? current.filter((t: string) => t !== type)
      : [...current, type]
    setValue(`${routeFieldName}.registration_types`, updated)
  }

  if (isEditing) {
    return (
      <TableRow>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.order`}
            render={({ field }) => (
              <Input
                type="number"
                min="1"
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                onKeyDown={handleKeyDown}
                className="w-16"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.code`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                className="font-mono text-sm"
                placeholder="工艺代码"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.name`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                placeholder="工艺名称"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.skill_code`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                className="font-mono text-sm"
                placeholder="技能代码"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.skill_name`}
            render={({ field }) => (
              <Input
                {...field}
                onKeyDown={handleKeyDown}
                placeholder="技能名称"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <div className="flex flex-wrap gap-1">
            {MEASUREMENT_TYPES.map((type) => {
              const current = getValues(`${routeFieldName}.measurement_types`) || []
              const isSelected = current.includes(type.value)
              return (
                <Badge
                  key={type.value}
                  variant={isSelected ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => toggleMeasurementType(type.value)}
                >
                  {type.label}
                </Badge>
              )
            })}
          </div>
        </TableCell>
        <TableCell>
          <div className="flex flex-wrap gap-1">
            {REGISTRATION_TYPES.map((type) => {
              const current = getValues(`${routeFieldName}.registration_types`) || []
              const isSelected = current.includes(type.value)
              return (
                <Badge
                  key={type.value}
                  variant={isSelected ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => toggleRegistrationType(type.value)}
                >
                  {type.label}
                </Badge>
              )
            })}
          </div>
        </TableCell>
        <TableCell>
          <Controller
            control={control}
            name={`${routeFieldName}.price`}
            render={({ field }) => (
              <Input
                type="number"
                min="0"
                step="0.01"
                {...field}
                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                onKeyDown={handleKeyDown}
                className="w-20"
                placeholder="0.00"
              />
            )}
          />
        </TableCell>
        <TableCell>
          <div className="flex gap-1">
            <Button size="sm" variant="ghost" onClick={handleSave}>
              <Check className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="ghost" onClick={handleCancel}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    )
  }

  return (
    <Controller
      control={control}
      name={routeFieldName}
      render={({ field }) => {
        const route = field.value || {}
        return (
          <TableRow>
            <TableCell className="font-medium">{route.order || '-'}</TableCell>
            <TableCell className="font-mono text-sm">{route.code || '-'}</TableCell>
            <TableCell>{route.name || '-'}</TableCell>
            <TableCell className="font-mono text-sm">{route.skill_code || '-'}</TableCell>
            <TableCell>{route.skill_name || '-'}</TableCell>
            <TableCell>
              <div className="flex flex-wrap gap-1">
                {(route.measurement_types || []).map((type: string) => {
                  const typeInfo = MEASUREMENT_TYPES.find(t => t.value === type)
                  return (
                    <Badge key={type} variant="secondary" className="text-xs">
                      {typeInfo?.label || type}
                    </Badge>
                  )
                })}
              </div>
            </TableCell>
            <TableCell>
              <div className="flex flex-wrap gap-1">
                {(route.registration_types || []).map((type: string) => {
                  const typeInfo = REGISTRATION_TYPES.find(t => t.value === type)
                  return (
                    <Badge key={type} variant="secondary" className="text-xs">
                      {typeInfo?.label || type}
                    </Badge>
                  )
                })}
              </div>
            </TableCell>
            <TableCell className="font-medium">
              ¥{route.price?.toFixed(2) || '0.00'}
            </TableCell>
            <TableCell>
              <div className="flex gap-1">
                <Button size="sm" variant="ghost" onClick={handleEdit}>
                  <Edit2 className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="ghost" onClick={handleDelete}>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        )
      }}
    />
  )
}

export default PartTypeCraftRouteRow
