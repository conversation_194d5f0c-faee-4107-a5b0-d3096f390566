import React, { useState } from 'react'
import { useFormContext, useFieldArray } from 'react-hook-form'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Trash2 } from 'lucide-react'
import CraftsImportDialog from '@/components/CraftsImportDialog'
import OrderCraftRouteTable from './OrderCraftRouteTable'
import type { CraftRouteDetailDto } from '@/services/types.gen'

export interface OrderCraftRoute {
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  assigned_user_id?: number
  price: number
}

export interface OrderCraft {
  craft_code: string
  craft_name: string
  order: number
  is_required: boolean
  estimated_duration_hours?: number
  order_craft_routes: OrderCraftRoute[]
}

interface OrderCraftsConfigProps {
  crafts?: Array<{ code: string; name: string }>
}

const OrderCraftsConfig: React.FC<OrderCraftsConfigProps> = () => {
  const { control } = useFormContext()
  const { fields, append, remove } = useFieldArray({
    control,
    name: "order_crafts"
  })
  const [showImportDialog, setShowImportDialog] = useState(false)

  const addOrderCraft = () => {
    setShowImportDialog(true)
  }

  // 删除工艺
  const removeCraft = (craftIndex: number) => {
    remove(craftIndex)
  }

  const handleImportCrafts = (importedData: { code: string; name: string; routes: CraftRouteDetailDto[] }[]) => {
    importedData.forEach(({ code, name, routes }) => {
      console.log(`Importing craft: ${code} - ${name}`, routes)
      
      // 检查是否已存在
      const exists = fields.some((field) => (field as unknown as OrderCraft).craft_code === code)
      if (!exists) {
        const newCraft: OrderCraft = {
          craft_code: code,
          craft_name: name,
          order: fields.length + 1,
          is_required: true,
          estimated_duration_hours: 0,
          order_craft_routes: routes.map((route): OrderCraftRoute => ({
            code: route.code,
            name: route.name || route.code,
            skill_code: route.skill_code,
            skill_name: route.skill_name || route.skill_code,
            order: route.order,
            measurement_types: route.measurement_types || [],
            registration_types: route.registration_types || [],
            assigned_user_id: undefined,
            price: 0, // 默认价格为0，后续可编辑
          }))
        }
        append(newCraft)
      }
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>工艺配置</CardTitle>
          <Button onClick={addOrderCraft} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            添加工艺
          </Button>
        </CardHeader>
        <CardContent>
          {fields.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              暂无工艺配置，点击"添加工艺"开始配置
            </p>
          ) : (
            <div className="space-y-6">
              {fields.map((craft, craftIndex) => {
                const craftData = craft as unknown as OrderCraft & { id: string }
                return (
                <div key={craftData.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-lg">{craftData.craft_name}</h4>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => removeCraft(craftIndex)}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      删除工艺
                    </Button>
                  </div>
                  
                  <OrderCraftRouteTable craftIndex={craftIndex} />
                </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      <CraftsImportDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
        onImport={handleImportCrafts}
      />
    </>
  )
}

export default OrderCraftsConfig
