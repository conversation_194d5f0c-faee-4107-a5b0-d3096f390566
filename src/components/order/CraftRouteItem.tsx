import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronDown, User, Clock, Target, FileText, Users, Loader2 } from 'lucide-react'
import { searchCraftInstancesApiV1CraftInstancesSearchGetOptions } from '@/services/@tanstack/react-query.gen'
import type { OrderCraftRouteResponseDto } from '@/services/types.gen'

interface CraftRouteItemProps {
  route: OrderCraftRouteResponseDto
  orderNo?: string
  onStartRegistration: (route: OrderCraftRouteResponseDto) => void
}

// 根据状态确定工艺路线状态
const getRouteStatus = (route: OrderCraftRouteResponseDto): 'pending' | 'current' | 'completed' => {
  if (route.completed_at) return 'completed'
  if (route.started_at) return 'current'
  return 'pending'
}

// 格式化持续时间
const formatDuration = (minutes?: number | null): string => {
  if (!minutes) return '-'
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
}

export const CraftRouteItem: React.FC<CraftRouteItemProps> = ({ 
  route, 
  orderNo, 
  onStartRegistration 
}) => {
  const [showInstances, setShowInstances] = useState(false)
  const status = getRouteStatus(route)

  // 获取该工艺路线的实例
  const { data: craftInstancesData, isLoading: instancesLoading, error: instancesError } = useQuery({
    ...searchCraftInstancesApiV1CraftInstancesSearchGetOptions({
      query: {
        order_no: orderNo,
        limit: 100 // 获取足够多的实例
      }
    }),
    enabled: !!orderNo && showInstances,
    staleTime: 30000, // 30秒缓存
  })

  // 过滤出属于当前工艺路线的实例
  const routeInstances = craftInstancesData?.instances?.filter(
    instance => instance.order_craft_route_id === route.id
  ) || []

  const toggleInstances = () => {
    setShowInstances(!showInstances)
  }

  return (
    <div className="border rounded-lg bg-muted/20">
      <div className="flex items-center gap-4 p-3">
        <div className={`w-2 h-2 rounded-full shrink-0 ${
          status === 'completed' ? 'bg-green-500' :
          status === 'current' ? 'bg-blue-500' :
          'bg-gray-300'
        }`} />

        <div className="flex-1 space-y-1">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{route.skill_name || route.skill_code}</span>
            <Badge variant="outline" className="text-xs">{route.skill_code}</Badge>
            {route.is_required && <Badge variant="secondary" className="text-xs">必需</Badge>}
          </div>

          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {route.assigned_user_name && (
              <span className="flex items-center gap-1">
                <User className="w-3 h-3" />
                {route.assigned_user_name}
              </span>
            )}
            {route.estimated_duration_minutes && (
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                预计: {formatDuration(route.estimated_duration_minutes)}
              </span>
            )}
            {route.actual_duration_minutes && (
              <span className="flex items-center gap-1">
                <Target className="w-3 h-3" />
                实际: {formatDuration(route.actual_duration_minutes)}
              </span>
            )}
            {routeInstances.length > 0 && (
              <span className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                {routeInstances.length} 个实例
              </span>
            )}
          </div>

          {route.notes && (
            <p className="text-xs text-muted-foreground">{route.notes}</p>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* 实例查看按钮 */}
          {orderNo && (
            <Button
              size="sm"
              variant="ghost"
              onClick={toggleInstances}
              className="h-8 px-2 text-xs"
              title={showInstances ? "隐藏实例" : "查看实例"}
            >
              <Users className="w-3 h-3 mr-1" />
              {routeInstances.length}
              <ChevronDown className={`w-3 h-3 ml-1 transition-transform ${showInstances ? 'rotate-180' : ''}`} />
            </Button>
          )}

          {/* 登记按钮 */}
          {route.registration_types && route.registration_types.length > 0 && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onStartRegistration(route)}
              className="h-8 px-2 text-xs text-blue-600 border-blue-200 hover:bg-blue-50"
              title="登记此工序"
            >
              <FileText className="w-3 h-3 mr-1" />
              登记
            </Button>
          )}

          <Badge variant={
            status === 'completed' ? 'default' :
            status === 'current' ? 'secondary' :
            'outline'
          }>
            {status === 'completed' ? '已完成' :
             status === 'current' ? '进行中' :
             '待开始'}
          </Badge>
        </div>
      </div>

      {/* 工艺实例详情 */}
      {showInstances && (
        <div className="border-t bg-background/50 p-3">
          {instancesLoading && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">加载实例中...</span>
            </div>
          )}

          {instancesError && (
            <div className="text-center py-4">
              <p className="text-sm text-red-600">加载实例失败</p>
            </div>
          )}

          {!instancesLoading && !instancesError && routeInstances.length === 0 && (
            <div className="text-center py-4">
              <p className="text-sm text-muted-foreground">暂无工艺实例</p>
            </div>
          )}

          {!instancesLoading && !instancesError && routeInstances.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">工艺实例 ({routeInstances.length})</h4>
              <div className="grid gap-2">
                {routeInstances.map((instance) => (
                  <div key={instance.id} className="flex items-center justify-between p-2 bg-background rounded border">
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="font-medium">{instance.worker_name || `工人 ${instance.worker_user_id}`}</span>
                        <Badge variant="outline" className="text-xs">
                          {instance.completion_granularity === 'order' ? '整单' :
                           instance.completion_granularity === 'bed' ? '分床' : '分扎'}
                        </Badge>
                        {instance.quality_level && (
                          <Badge variant="secondary" className="text-xs">{instance.quality_level}</Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        {instance.completed_quantity && (
                          <span>数量: {instance.completed_quantity}</span>
                        )}
                        {instance.started_at && (
                          <span>开始: {new Date(instance.started_at).toLocaleString()}</span>
                        )}
                        {instance.completed_at && (
                          <span>完成: {new Date(instance.completed_at).toLocaleString()}</span>
                        )}
                      </div>

                      {instance.order_part_nos && instance.order_part_nos.length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          床号: {instance.order_part_nos.join(', ')}
                        </div>
                      )}

                      {instance.order_bundle_nos && instance.order_bundle_nos.length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          扎号: {instance.order_bundle_nos.join(', ')}
                        </div>
                      )}

                      {instance.notes && (
                        <div className="text-xs text-muted-foreground">
                          备注: {instance.notes}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-1">
                      <Badge variant={instance.status === 'completed' ? 'default' : 'secondary'} className="text-xs">
                        {instance.status === 'completed' ? '已完成' : instance.status || '进行中'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
