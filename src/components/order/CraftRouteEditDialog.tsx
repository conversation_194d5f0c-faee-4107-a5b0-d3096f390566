import React from 'react'
import { useFormContext } from 'react-hook-form'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'

interface CraftRouteEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  partTypeIndex: number
  craftIndex: number
  routeIndex: number
}

const CraftRouteEditDialog: React.FC<CraftRouteEditDialogProps> = ({
  open,
  onOpenChange,
  partTypeIndex,
  craftIndex,
  routeIndex
}) => {
  const { setValue, getValues, watch } = useFormContext()
  
  const routeFieldName = `part_types.${partTypeIndex}.order_crafts.${craftIndex}.order_craft_routes.${routeIndex}`
  const routeData = watch(routeFieldName) || {}

  const toggleMeasurementType = (type: string) => {
    const current = getValues(`${routeFieldName}.measurement_types`) || []
    const updated = current.includes(type)
      ? current.filter((t: string) => t !== type)
      : [...current, type]
    setValue(`${routeFieldName}.measurement_types`, updated)
  }

  const toggleRegistrationType = (type: string) => {
    const current = getValues(`${routeFieldName}.registration_types`) || []
    const updated = current.includes(type)
      ? current.filter((t: string) => t !== type)
      : [...current, type]
    setValue(`${routeFieldName}.registration_types`, updated)
  }

  const handleSave = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>编辑工艺路线</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="code" className="text-sm font-medium">代码</Label>
              <Input
                id="code"
                value={routeData.code || ''}
                onChange={(e) => setValue(`${routeFieldName}.code`, e.target.value)}
                placeholder="工艺路线代码"
              />
            </div>
            <div>
              <Label htmlFor="order" className="text-sm font-medium">顺序</Label>
              <Input
                id="order"
                type="number"
                min="1"
                value={routeData.order || 1}
                onChange={(e) => setValue(`${routeFieldName}.order`, parseInt(e.target.value) || 1)}
                placeholder="执行顺序"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="name" className="text-sm font-medium">名称</Label>
            <Input
              id="name"
              value={routeData.name || ''}
              onChange={(e) => setValue(`${routeFieldName}.name`, e.target.value)}
              placeholder="工艺路线名称"
            />
          </div>
          
          {/* Skill Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="skill_code" className="text-sm font-medium">技能代码</Label>
              <Input
                id="skill_code"
                value={routeData.skill_code || ''}
                onChange={(e) => setValue(`${routeFieldName}.skill_code`, e.target.value)}
                placeholder="技能代码"
              />
            </div>
            <div>
              <Label htmlFor="skill_name" className="text-sm font-medium">技能名称</Label>
              <Input
                id="skill_name"
                value={routeData.skill_name || ''}
                onChange={(e) => setValue(`${routeFieldName}.skill_name`, e.target.value)}
                placeholder="技能名称"
              />
            </div>
          </div>
          
          {/* Price */}
          <div>
            <Label htmlFor="price" className="text-sm font-medium">价格</Label>
            <Input
              id="price"
              type="number"
              min="0"
              step="0.01"
              value={routeData.price || 0}
              onChange={(e) => setValue(`${routeFieldName}.price`, parseFloat(e.target.value) || 0)}
              placeholder="0.00"
            />
          </div>
          
          {/* Measurement Types */}
          <div>
            <Label className="text-sm font-medium mb-3 block">计价方式</Label>
            <div className="flex flex-wrap gap-2">
              {MEASUREMENT_TYPES.map((type) => {
                const current = routeData.measurement_types || []
                const isSelected = current.includes(type.value)
                return (
                  <Badge
                    key={type.value}
                    variant={isSelected ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => toggleMeasurementType(type.value)}
                  >
                    {type.label}
                  </Badge>
                )
              })}
            </div>
          </div>
          
          {/* Registration Types */}
          <div>
            <Label className="text-sm font-medium mb-3 block">登记方式</Label>
            <div className="flex flex-wrap gap-2">
              {REGISTRATION_TYPES.map((type) => {
                const current = routeData.registration_types || []
                const isSelected = current.includes(type.value)
                return (
                  <Badge
                    key={type.value}
                    variant={isSelected ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => toggleRegistrationType(type.value)}
                  >
                    {type.label}
                  </Badge>
                )
              })}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSave}>
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default CraftRouteEditDialog
