import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Package, Edit2, Save, X, DollarSign } from 'lucide-react'
import type { OrderWithLinesDto, OrderUpdateDto } from '@/services/types.gen'

interface OrderBasicInfoProps {
  order: OrderWithLinesDto
  onUpdate?: (data: OrderUpdateDto) => Promise<void>
  editable?: boolean
}

export const OrderBasicInfo: React.FC<OrderBasicInfoProps> = ({ 
  order, 
  onUpdate,
  editable = true 
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState<OrderUpdateDto>({
    skc_no: order.skc_no,
    external_skc_no: order.external_skc_no,
    external_order_no: order.external_order_no,
    external_order_no2: order.external_order_no2,
    cost: order.cost,
    price: order.price,
    expect_finished_at: order.expect_finished_at,
    owner_user_id: order.owner_user_id,
    description: order.description,
    notes: order.notes,
  })
  const [isSaving, setIsSaving] = useState(false)

  const handleSave = async () => {
    if (!onUpdate) return
    
    setIsSaving(true)
    try {
      await onUpdate(editData)
      setIsEditing(false)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditData({
      skc_no: order.skc_no,
      external_skc_no: order.external_skc_no,
      external_order_no: order.external_order_no,
      external_order_no2: order.external_order_no2,
      cost: order.cost,
      price: order.price,
      expect_finished_at: order.expect_finished_at,
      owner_user_id: order.owner_user_id,
      description: order.description,
      notes: order.notes,
    })
    setIsEditing(false)
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending': { label: '待处理', variant: 'secondary' as const },
      'in_progress': { label: '进行中', variant: 'default' as const },
      'completed': { label: '已完成', variant: 'outline' as const },
      'cancelled': { label: '已取消', variant: 'destructive' as const }
    }
    
    return statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'outline' as const }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Package className="w-5 h-5 mr-2" />
            基本信息
          </CardTitle>
          {editable && !isEditing && (
            <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
              <Edit2 className="w-4 h-4 mr-2" />
              编辑
            </Button>
          )}
          {isEditing && (
            <div className="flex gap-2">
              <Button size="sm" onClick={handleSave} disabled={isSaving}>
                <Save className="w-4 h-4 mr-2" />
                保存
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel} disabled={isSaving}>
                <X className="w-4 h-4 mr-2" />
                取消
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 订单号 - 只读 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">订单号</label>
            <p className="font-mono text-lg">{order.order_no}</p>
          </div>
          
          {/* SKC号 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">SKC号</label>
            {isEditing ? (
              <Input
                value={editData.skc_no || ''}
                onChange={(e) => setEditData({ ...editData, skc_no: e.target.value })}
                className="font-mono"
              />
            ) : (
              <p className="font-mono text-lg">{order.skc_no}</p>
            )}
          </div>
          
          {/* 外部订单号 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">外部订单号</label>
            {isEditing ? (
              <Input
                value={editData.external_order_no || ''}
                onChange={(e) => setEditData({ ...editData, external_order_no: e.target.value })}
                className="font-mono"
                placeholder="可选"
              />
            ) : (
              <p className="font-mono">{order.external_order_no || '-'}</p>
            )}
          </div>
          
          {/* 外部订单号2 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">外部订单号2</label>
            {isEditing ? (
              <Input
                value={editData.external_order_no2 || ''}
                onChange={(e) => setEditData({ ...editData, external_order_no2: e.target.value })}
                className="font-mono"
                placeholder="可选"
              />
            ) : (
              <p className="font-mono">{order.external_order_no2 || '-'}</p>
            )}
          </div>
          
          {/* 状态 - 只读 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">状态</label>
            <Badge variant={getStatusBadge(order.status).variant}>
              {getStatusBadge(order.status).label}
            </Badge>
          </div>
          
          {/* 价格 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground flex items-center">
              <DollarSign className="w-4 h-4 mr-1" />
              价格
            </label>
            {isEditing ? (
              <Input
                type="number"
                value={editData.price || ''}
                onChange={(e) => setEditData({ ...editData, price: e.target.value })}
                placeholder="0.00"
                step="0.01"
              />
            ) : (
              <p className="text-lg font-semibold">
                {order.price ? `¥${parseFloat(order.price).toFixed(2)}` : '-'}
              </p>
            )}
          </div>
          
          {/* 创建时间 - 只读 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">创建时间</label>
            <p>{formatDate(order.created_at)}</p>
          </div>
        </div>
        
        {/* 备注 */}
        <div className="mt-6 space-y-2">
          <label className="text-sm font-medium text-muted-foreground">备注</label>
          {isEditing ? (
            <Textarea
              value={editData.notes || ''}
              onChange={(e) => setEditData({ ...editData, notes: e.target.value })}
              placeholder="添加备注..."
              rows={3}
            />
          ) : (
            order.notes && (
              <p className="text-sm bg-muted p-3 rounded">{order.notes}</p>
            )
          )}
        </div>
      </CardContent>
    </Card>
  )
}
