import React, { useCallback, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { getAvailableRegistrationDataOptions } from "@/services/@tanstack/react-query.gen";
import type { RegistrationFormData } from "./RegistrationDialog";
import type { OrderCraftRouteResponseDto } from "@/services/types.gen";
import { FormField, FormItem, FormLabel } from "../ui/form";

interface OrderPart {
  order_part_no?: string;
  available_quantity?: number;
  total_quantity?: number;
  registered_quantity?: number;
}

interface PartCardProps {
  part: OrderPart;
  selected: boolean;
  onCheckChanged: (partNo: string, checked: boolean) => void;
}

const PartCard: React.FC<PartCardProps> = ({
  part,
  selected,
  onCheckChanged,
}) => {
  const availableQuantity = part.available_quantity || 0;
  const totalQuantity = part.total_quantity || 0;
  const registeredQuantity = part.registered_quantity || 0;

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md ${
        selected
          ? "ring-2 ring-primary bg-primary/5"
          : "hover:border-primary/50"
      }`}
      onClick={(e) => {
        e.stopPropagation();
        const target = e.target as HTMLElement;
        if (!target.closest('[role="checkbox"]') && 
            !target.closest('input[type="checkbox"]') &&
            target.tagName.toLowerCase() !== 'input') {
          onCheckChanged(part.order_part_no || "", !selected);
        }
      }}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selected}
              onCheckedChange={(checked) => {
                onCheckChanged(part.order_part_no || "", checked as boolean);
              }}
            />
            <span className="font-medium text-lg">{part.order_part_no}</span>
          </div>
          <Badge variant={availableQuantity > 0 ? "default" : "secondary"}>
            {availableQuantity > 0 ? "可登记" : "已完成"}
          </Badge>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">总数量:</span>
            <span className="font-medium">{totalQuantity}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">已登记:</span>
            <span className="font-medium">{registeredQuantity}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">可登记:</span>
            <span className="font-medium text-primary">
              {availableQuantity}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const InnerOrderPartSelector: React.FC<{
  craftRouteId: number;
  orderNo: string;
  value: string[];
  onChange: (value: string[], amount?: number) => void;
}> = ({ value, onChange, craftRouteId, orderNo }) => {
  // 获取可用的登记数据（分床）
  const { data: availableRegistrationData } = useQuery({
    ...getAvailableRegistrationDataOptions({
      path: { order_no: orderNo || "" },
      query: {
        craft_route_id: craftRouteId,
        include_completed_routes: false,
        granularity_filter: "part",
      },
    }),
    enabled: !!orderNo && !!craftRouteId,
  });

  const orderParts = useMemo(
    () => availableRegistrationData?.order_parts || [],
    [availableRegistrationData]
  );

  // 处理分床选择的函数
  const handlePartSelection = useCallback(
    (partNo: string, checked: boolean) => {
      let newSelected: string[];
      if (checked) {
        newSelected = [...value, partNo];
      } else {
        newSelected = value.filter((p) => p !== partNo);
      }

      const totalAvailableQuantity = orderParts
        .filter((part) => newSelected.includes(part.order_part_no || ""))
        .reduce((sum, part) => sum + (part.available_quantity || 0), 0);

      onChange(newSelected, totalAvailableQuantity);
    },
    [orderParts, value, onChange]
  );

  if (orderParts.length == 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>当前订单暂无可登记的分床数据</p>
      </div>
    );
  }

  return <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {orderParts.map((part) => {
      return (
        <PartCard
          key={part.order_part_no}
          part={part}
          selected={value?.includes(part.order_part_no || "")}
          onCheckChanged={handlePartSelection}
        />
      );
    })}
  </div>
}
interface OrderPartSelectorProps {
  orderNo?: string;
  selectedRoute?: OrderCraftRouteResponseDto;
}

export const OrderPartSelector: React.FC<OrderPartSelectorProps> = ({
  orderNo,
  selectedRoute,
}) => {
  const { control, watch, setValue } = useFormContext<RegistrationFormData>();
  const registrationType = watch("registrationType");

  // 只在登记类型为 PART 时显示
  if (registrationType !== "PART" || !orderNo || !!selectedRoute?.id) {
    return null;
  }

  return (
    <FormField
      control={control}
      name="selectedParts"
      render={({ field }) => (
        <FormItem className="space-y-4">
          <FormLabel>请选择要登记完成的分床，可以选择多个。</FormLabel>
          <InnerOrderPartSelector
            craftRouteId={selectedRoute?.id || 0}
            orderNo={orderNo}
            value={field.value}
            onChange={(value, amount) => {
              field.onChange(value);
              setValue("completedQuantity", amount || 1);
            }}
          />
        </FormItem>
      )}
    />
  );
};
