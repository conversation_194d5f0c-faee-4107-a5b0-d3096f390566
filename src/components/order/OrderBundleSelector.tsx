import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { getAvailableRegistrationDataOptions } from "@/services/@tanstack/react-query.gen";
import type { RegistrationFormData } from "./RegistrationDialog";
import type { OrderCraftRouteResponseDto } from "@/services/types.gen";

interface OrderBundle {
  order_bundle_no?: string;
  available_quantity?: number;
  total_quantity?: number;
  registered_quantity?: number;
  order_part_no?: string; // 所属床号信息
  order_part_name?: string;
  size?: string;
}

interface OrderBundleItemProps {
  bundle: OrderBundle;
  selected: boolean;
  onCheckChanged: (bundleNo: string, checked: boolean) => void;
}

const OrderBundleItem: React.FC<OrderBundleItemProps> = ({
  bundle,
  selected,
  onCheckChanged,
}) => {
  const availableQuantity = bundle.available_quantity || 0;

  // 获取扎号的最后一部分（'_'分割后的最后一个元素）
  const displayBundleNo = bundle.order_bundle_no
    ? bundle.order_bundle_no.split('_').pop() || bundle.order_bundle_no
    : "";

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md ${
        selected
          ? "ring-2 ring-primary bg-primary/5"
          : "hover:border-primary/50"
      }`}
      onClick={(e) => {
        // Only trigger if not clicking on the checkbox itself
        const target = e.target as HTMLElement;
        if (!target.closest('[role="checkbox"]') && 
            !target.closest('input[type="checkbox"]') &&
            target.tagName.toLowerCase() !== 'input') {
          onCheckChanged(bundle.order_bundle_no || "", !selected);
        }
      }}
    >
      <CardContent className="p-3">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selected}
                onCheckedChange={(checked) => {
                  console.log("checkbox changed", bundle.order_bundle_no, checked);
                  onCheckChanged(
                    bundle.order_bundle_no || "",
                    checked as boolean
                  );
                }}
              />
              <span
                className="font-medium text-sm"
                title={bundle.order_bundle_no}
              >
                {displayBundleNo}
              </span>
            </div>
            <Badge variant="outline" className="text-xs">
              {availableQuantity}
            </Badge>
          </div>
          
          {/* 显示床号名称和尺码 */}
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {bundle.order_part_name && (
              <Badge variant="secondary" className="text-xs py-0 px-1">
                {bundle.order_part_name}
              </Badge>
            )}
            {bundle.size && (
              <Badge variant="secondary" className="text-xs py-0 px-1">
                {bundle.size}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const InnerOrderBundleSelector: React.FC<{
  orderNo?: string;
  selectedRoute?: OrderCraftRouteResponseDto;
  value: string[];
  onChange: (value: string[], amount?: number) => void;
}> = ({ orderNo, selectedRoute, value, onChange }) => {
  const [selectedPartName, setSelectedPartName] = useState<string | null>(null);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);

  const params = useMemo(() => ({
    path: { order_no: orderNo || "" },
    query: {
      craft_route_id: selectedRoute?.id,
      include_completed_routes: false,
      granularity_filter: "bundle",
    },
  }), [orderNo, selectedRoute]);

  // 获取可用的登记数据（分扎）
  const { data: availableRegistrationData } = useQuery({
    ...getAvailableRegistrationDataOptions(params),
    enabled: !!orderNo && !!selectedRoute?.id,
  });

  // 从可用登记数据中提取分扎信息
  const orderBundles = useMemo(() => {
    const orderParts = availableRegistrationData?.order_parts || [];
    return orderParts.flatMap((part) =>
      (part.order_bundles || []).map((bundle) => ({
        ...bundle,
        order_part_no: part.order_part_no, // 添加所属床号信息
        order_part_name: part.order_part_name
      }))
    );
  }, [availableRegistrationData]);

  // 获取唯一的床号名称和尺码用于筛选
  const uniquePartNames = useMemo(() => {
     const orderParts = availableRegistrationData?.order_parts || [];
    const names = new Set(orderParts.map(part => part.order_part_name).filter(Boolean));
    return Array.from(names);
  }, [availableRegistrationData]);

  const uniqueSizes = useMemo(() => {
    const sizes = new Set(orderBundles.map(bundle => bundle.size).filter(Boolean));
    return Array.from(sizes);
  }, [orderBundles]);

  // 根据筛选条件过滤分扎
  const filteredBundles = useMemo(() => {
    return orderBundles.filter(bundle => {
      if (selectedPartName && bundle.order_part_name !== selectedPartName) {
        return false;
      }
      if (selectedSize && bundle.size !== selectedSize) {
        return false;
      }
      return true;
    });
  }, [orderBundles, selectedPartName, selectedSize]);

  // 处理分扎选择的函数
  const handleBundleSelection = useCallback((bundleNo: string, checked: boolean) => {
    let newSelected: string[];
    if (checked) {
      newSelected = [...value, bundleNo];
    } else {
      newSelected = value.filter((b) => b !== bundleNo);
    }
    const totalAvailableQuantity = orderBundles
      .filter((bundle) => newSelected.includes(bundle.order_bundle_no || ""))
      .reduce((sum, bundle) => sum + (bundle.available_quantity || 0), 0);

    onChange(newSelected, totalAvailableQuantity);
  }, [orderBundles, value, onChange]);

  if (orderBundles.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>当前订单暂无可登记的分扎数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">选择分扎</h3>
        <div className="text-sm text-muted-foreground">
          已选择 {value.length} 个分扎
        </div>
      </div>
      
      {/* 筛选器 */}
      <div className="flex flex-wrap gap-2">
        {/* 床号名称筛选 */}
        {uniquePartNames.length > 1 && (
          <div className="flex flex-wrap gap-1">
            <Badge
              variant={selectedPartName === null ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSelectedPartName(null)}
            >
              全部床号
            </Badge>
            {uniquePartNames.map((name) => (
              <Badge
                key={name}
                variant={selectedPartName === name ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedPartName(selectedPartName === name ? null : name)}
              >
                {name}
              </Badge>
            ))}
          </div>
        )}
        
        {/* 尺码筛选 */}
        {uniqueSizes.length > 1 && (
          <div className="flex flex-wrap gap-1">
            <Badge
              variant={selectedSize === null ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSelectedSize(null)}
            >
              全部尺码
            </Badge>
            {uniqueSizes.map((size) => (
              <Badge
                key={size}
                variant={selectedSize === size ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedSize(selectedSize === size ? null : size)}
              >
                {size}
              </Badge>
            ))}
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3">
        {filteredBundles.map((bundle) => {
          return (
            <OrderBundleItem
              key={bundle.order_bundle_no}
              bundle={bundle}
              selected={Boolean(bundle.order_bundle_no && value.includes(bundle.order_bundle_no))}
              onCheckChanged={handleBundleSelection}
            />
          );
        })}
      </div>
      
      {filteredBundles.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>没有符合筛选条件的分扎</p>
        </div>
      )}
    </div>
  );
};

interface OrderBundleSelectorProps {
  orderNo?: string;
  selectedRoute?: OrderCraftRouteResponseDto;
}

export const OrderBundleSelector: React.FC<OrderBundleSelectorProps> = ({
  orderNo,
  selectedRoute,
}) => {
  const { control, watch, setValue } = useFormContext<RegistrationFormData>();
  const registrationType = watch("registrationType");

  useEffect(() => {
    console.log("data changed", registrationType, control, watch);

  }, [control, watch, setValue, registrationType])

  // 只在登记类型为 BUNDLER 时显示
  if (registrationType !== "BUNDLER") {
    return null;
  }
  return (
    <Controller
      control={control}
      name="selectedBundles"
      render={({ field }) => (
        <InnerOrderBundleSelector
          orderNo={orderNo}
          selectedRoute={selectedRoute}
          value={field.value}
          onChange={(value, amount) => {
            console.log(value, amount)
            field.onChange(value);
            setValue("completedQuantity", amount || 1);
          }}
        />
      )}
    />
  );
};
