import React, { useState } from 'react'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Plus, Trash2, Sparkles } from 'lucide-react'

export interface OrderLine {
  size: string
  amount: number
  notes?: string
}

const OrderLinesConfig: React.FC = () => {
  const { control } = useFormContext()
  const [aiInput, setAiInput] = useState('')
  const [isAiProcessing, setIsAiProcessing] = useState(false)
  
  const { fields, append, remove } = useFieldArray({
    control,
    name: "order_lines",
  })

  const addOrderLine = () => {
    append({ size: '', amount: 1, notes: '' })
  }

  const handleAiGenerate = async () => {
    if (!aiInput.trim()) return
    
    setIsAiProcessing(true)
    try {
      // TODO: 调用后端AI接口生成订单行数据
      // const response = await fetch('/api/ai/generate-order-lines', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ input: aiInput })
      // })
      // const aiGeneratedLines = await response.json()
      
      // 临时模拟数据，实际开发时替换为真实的AI接口调用
      console.log('AI输入内容:', aiInput)
      
      // 模拟AI生成的订单行数据
      const simulatedLines = [
        { size: 'S', amount: 10, notes: 'AI生成 - 小码' },
        { size: 'M', amount: 15, notes: 'AI生成 - 中码' },
        { size: 'L', amount: 12, notes: 'AI生成 - 大码' }
      ]
      
      // 将AI生成的数据添加到表单中
      simulatedLines.forEach(line => {
        append(line)
      })
      
      // 清空输入框
      setAiInput('')
    } catch (error) {
      console.error('AI生成失败:', error)
      // TODO: 显示错误提示
    } finally {
      setIsAiProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex justify-between align-middle'>
            <div className='flex align-middle pt-1'>
                订单行配置
            </div>
            <div className="flex gap-2">
            {/* AI快速输入 */}
            <div className="flex flex-1 gap-2">
                <Input
                value={aiInput}
                onChange={(e) => setAiInput(e.target.value)}
                placeholder="快速输入，AI识别"
                className="flex-1"
                onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    handleAiGenerate()
                    }
                }}
                />
                <Button
                onClick={handleAiGenerate}
                disabled={!aiInput.trim() || isAiProcessing}
                size="sm"
                variant="outline"
                className="shrink-0"
                >
                <Sparkles className="w-4 h-4" />
                {isAiProcessing ? '处理中...' : 'AI'}
                </Button>
            </div>
            
            {/* 添加订单行按钮 */}
            <Button onClick={addOrderLine} size="sm">
                <Plus className="w-4 h-4 mr-2" />
                添加订单行
            </Button>
            </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {fields.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">
            暂无订单行，点击"添加订单行"开始配置
          </p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">序号</TableHead>
                <TableHead className="w-[150px]">尺码 <span className="text-red-500">*</span></TableHead>
                <TableHead className="w-[120px]">数量 <span className="text-red-500">*</span></TableHead>
                <TableHead>备注</TableHead>
                <TableHead className="w-[80px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fields.map((field, index) => (
                <TableRow key={field.id}>
                  <TableCell className="font-medium">{index + 1}</TableCell>
                  
                  {/* 尺码 */}
                  <TableCell>
                    <FormField
                      control={control}
                      name={`order_lines.${index}.size`}
                      rules={{ required: "尺码是必填项" }}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="请输入尺码"
                              className="h-8"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* 数量 */}
                  <TableCell>
                    <FormField
                      control={control}
                      name={`order_lines.${index}.amount`}
                      rules={{ 
                        required: "数量是必填项",
                        min: { value: 1, message: "数量必须大于0" }
                      }}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              min="1"
                              placeholder="数量"
                              className="h-8"
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* 备注 */}
                  <TableCell>
                    <FormField
                      control={control}
                      name={`order_lines.${index}.notes`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="请输入备注"
                              rows={1}
                              className="resize-none"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* 操作 */}
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => remove(index)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
        
        {fields.length > 0 && (
          <div className="mt-4 text-sm text-muted-foreground">
            <span className="font-medium">总条目: {fields.length}</span>
            <span className="ml-4 font-medium">
              总数量: {fields.reduce((sum, _, index) => {
                const amount = control._getWatch(`order_lines.${index}.amount`) || 0
                return sum + (typeof amount === 'number' ? amount : parseInt(amount) || 0)
              }, 0)}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default OrderLinesConfig
