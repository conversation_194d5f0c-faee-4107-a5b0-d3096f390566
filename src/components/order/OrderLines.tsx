import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Plus, Edit2, Trash2, Save, X } from 'lucide-react'
import type { OrderLineResponseDto } from '@/services/types.gen'

interface OrderLinesProps {
  orderLines: OrderLineResponseDto[]
  onUpdate?: (lines: OrderLineResponseDto[]) => Promise<void>
  editable?: boolean
}

export const OrderLines: React.FC<OrderLinesProps> = ({
  orderLines,
  onUpdate,
  editable = true
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editLines, setEditLines] = useState(orderLines)
  const [isSaving, setIsSaving] = useState(false)

  const handleAddLine = () => {
    const newLine: OrderLineResponseDto = {
      id: 0, // Temporary ID for new lines
      order_no: '',
      order_line_no: '',
      size: '',
      amount: 0,
      notes: '',
      produced_amount: null,
      completed_amount: null,
      completion_percentage: null,
      production_percentage: null,
      remaining_amount: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    setEditLines([...editLines, newLine])
  }

  const handleUpdateLine = (index: number, field: keyof OrderLineResponseDto, value: string | number | null) => {
    const updated = [...editLines]
    updated[index] = { ...updated[index], [field]: value }
    setEditLines(updated)
  }

  const handleDeleteLine = (index: number) => {
    setEditLines(editLines.filter((_, i) => i !== index))
  }

  const handleSave = async () => {
    if (!onUpdate) return
    
    setIsSaving(true)
    try {
      await onUpdate(editLines)
      setIsEditing(false)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditLines(orderLines)
    setIsEditing(false)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>订单行</CardTitle>
          {editable && !isEditing && (
            <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
              <Edit2 className="w-4 h-4 mr-2" />
              编辑
            </Button>
          )}
          {isEditing && (
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={handleAddLine}>
                <Plus className="w-4 h-4 mr-2" />
                添加行
              </Button>
              <Button size="sm" onClick={handleSave} disabled={isSaving}>
                <Save className="w-4 h-4 mr-2" />
                保存
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel} disabled={isSaving}>
                <X className="w-4 h-4 mr-2" />
                取消
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {editLines.length === 0 && !isEditing ? (
          <p className="text-muted-foreground text-center py-4">暂无订单行数据</p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>尺码</TableHead>
                  <TableHead>数量</TableHead>
                  <TableHead>备注</TableHead>
                  {isEditing && <TableHead className="w-[100px]">操作</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {editLines.map((line, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      {isEditing ? (
                        <Input
                          value={line.size}
                          onChange={(e) => handleUpdateLine(index, 'size', e.target.value)}
                          placeholder="输入尺码"
                        />
                      ) : (
                        <span className="font-medium">{line.size}</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {isEditing ? (
                        <Input
                          type="number"
                          value={line.amount}
                          onChange={(e) => handleUpdateLine(index, 'amount', parseInt(e.target.value) || 0)}
                          placeholder="0"
                        />
                      ) : (
                        line.amount
                      )}
                    </TableCell>
                    <TableCell>
                      {isEditing ? (
                        <Input
                          value={line.notes || ''}
                          onChange={(e) => handleUpdateLine(index, 'notes', e.target.value)}
                          placeholder="可选"
                        />
                      ) : (
                        line.notes || '-'
                      )}
                    </TableCell>
                    {isEditing && (
                      <TableCell>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDeleteLine(index)}
                        >
                          <Trash2 className="w-4 h-4 text-destructive" />
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
