import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Save, ArrowLeft } from 'lucide-react'

interface OrderSummaryProps {
  baseInfo: {
    skc_no: string
    order_no: string
    price?: number | string
    notes?: string
  }
  orderLines: Array<{
    size: string
    amount: number
    notes?: string
  }>
  orderCrafts: Array<{
    craft_code: string
    order: number
    is_required: boolean
  }>
  onSave: () => void
  onCancel: () => void
  isLoading?: boolean
}

const OrderSummary: React.FC<OrderSummaryProps> = ({
  baseInfo,
  orderLines,
  orderCrafts,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const totalAmount = orderLines.reduce((sum, line) => sum + line.amount, 0)
  const totalPrice = typeof baseInfo.price === 'number' 
    ? baseInfo.price * totalAmount 
    : 0

  return (
    <Card>
      <CardHeader>
        <CardTitle>订单汇总</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 基础信息汇总 */}
        <div>
          <h4 className="font-medium mb-3">基础信息</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">SKC号:</span>
              <span className="ml-2 font-medium">{baseInfo.skc_no || '-'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">订单号:</span>
              <span className="ml-2 font-medium">{baseInfo.order_no || '-'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">单件价格:</span>
              <span className="ml-2 font-medium">
                {baseInfo.price ? `¥${baseInfo.price}` : '-'}
              </span>
            </div>
          </div>
          {baseInfo.notes && (
            <div className="mt-3 text-sm">
              <span className="text-muted-foreground">订单备注:</span>
              <p className="mt-1 text-gray-700">{baseInfo.notes}</p>
            </div>
          )}
        </div>

        {/* 订单行汇总 */}
        <div>
          <h4 className="font-medium mb-3">订单行汇总</h4>
          {orderLines.length > 0 ? (
            <div className="space-y-2">
              <div className="grid grid-cols-3 gap-4 text-sm font-medium text-muted-foreground">
                <span>尺码</span>
                <span>数量</span>
                <span>备注</span>
              </div>
              {orderLines.map((line, index) => (
                <div key={index} className="grid grid-cols-3 gap-4 text-sm py-2 border-b border-gray-100">
                  <span>{line.size}</span>
                  <span>{line.amount}</span>
                  <span className="text-muted-foreground">{line.notes || '-'}</span>
                </div>
              ))}
              <div className="pt-2 text-sm font-medium">
                <span className="text-muted-foreground">总数量:</span>
                <span className="ml-2">{totalAmount}</span>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground text-sm">暂无订单行</p>
          )}
        </div>

        {/* 工艺配置汇总 */}
        <div>
          <h4 className="font-medium mb-3">工艺配置汇总</h4>
          {orderCrafts.length > 0 ? (
            <div className="space-y-2">
              {orderCrafts.map((craft, index) => (
                <div key={index} className="text-sm py-2 border-b border-gray-100">
                  <span className="font-medium">工艺 {craft.order}:</span>
                  <span className="ml-2">{craft.craft_code}</span>
                  <span className="ml-4 text-muted-foreground">
                    ({craft.is_required ? '必需' : '可选'})
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-sm">暂无工艺配置</p>
          )}
        </div>

        {/* 价格汇总 */}
        {totalPrice > 0 && (
          <div>
            <h4 className="font-medium mb-3">价格汇总</h4>
            <div className="grid grid-cols-1 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">总价格:</span>
                <span className="ml-2 font-medium text-green-600">¥{totalPrice.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={onCancel}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            取消
          </Button>
          <Button onClick={onSave} disabled={isLoading}>
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? '保存中...' : '保存订单'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default OrderSummary
