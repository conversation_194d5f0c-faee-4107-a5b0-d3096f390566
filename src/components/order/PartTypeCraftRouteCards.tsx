import React from 'react'
import { useFormContext, useFieldArray } from 'react-hook-form'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Edit2, Trash2, Plus } from 'lucide-react'
import { useState } from 'react'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'
import CraftRouteEditDialog from './CraftRouteEditDialog'

interface PartTypeCraftRouteCardsProps {
  partTypeIndex: number
  craftIndex: number
}

const PartTypeCraftRouteCards: React.FC<PartTypeCraftRouteCardsProps> = ({
  partTypeIndex,
  craftIndex
}) => {
  const { control } = useFormContext()
  const { fields, append, remove } = useFieldArray({
    control,
    name: `part_types.${partTypeIndex}.order_crafts.${craftIndex}.order_craft_routes`
  })

  const addNewRoute = () => {
    const newRoute = {
      code: '',
      name: '',
      skill_code: '',
      skill_name: '',
      order: fields.length + 1,
      measurement_types: [],
      registration_types: [],
      assigned_user_id: undefined,
      price: 0
    }
    append(newRoute)
  }

  if (fields.length === 0) {
    return (
      <div className="text-center py-6 border border-dashed rounded-lg">
        <p className="text-sm text-muted-foreground mb-4">
          该工艺暂无路线配置
        </p>
        <Button onClick={addNewRoute} size="sm" variant="outline">
          添加第一个工艺路线
        </Button>
      </div>
    )
  }

  return (
    <div className="flex gap-2 overflow-x-auto pb-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      {/* Existing Route Cards */}
      {fields.map((route, routeIndex) => (
        <CraftRouteCard
          key={route.id}
          partTypeIndex={partTypeIndex}
          craftIndex={craftIndex}
          routeIndex={routeIndex}
          onRemove={() => remove(routeIndex)}
        />
      ))}
      
      {/* Add New Route Card - at the end */}
      <Card className="min-w-[240px] max-w-[240px] flex-shrink-0 py-0 border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors cursor-pointer"
            onClick={addNewRoute}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="w-10 h-10 mx-auto mb-2 rounded-full bg-gray-100 flex items-center justify-center">
              <Plus className="w-5 h-5 text-gray-400" />
            </div>
            <p className="text-xs text-muted-foreground">添加工艺路线</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

interface CraftRouteCardProps {
  partTypeIndex: number
  craftIndex: number
  routeIndex: number
  onRemove: () => void
}

const CraftRouteCard: React.FC<CraftRouteCardProps> = ({
  partTypeIndex,
  craftIndex,
  routeIndex,
  onRemove
}) => {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const { watch } = useFormContext()
  
  const routeFieldName = `part_types.${partTypeIndex}.order_crafts.${craftIndex}.order_craft_routes.${routeIndex}`
  const routeData = watch(routeFieldName) || {}

  const handleEdit = () => {
    setIsEditDialogOpen(true)
  }

  return (
    <>
      <Card className="min-w-[240px] max-w-[240px] flex-shrink-0 shadow-sm border-l-4 border-l-blue-500 py-0">
        <CardContent className="p-3 space-y-2">
          {/* First Row: Sequence, Name, Edit/Delete */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-5 flex-shrink-0">
                #{routeData.order || routeIndex + 1}
              </Badge>
              <span className="text-sm font-medium truncate">
                {routeData.name || routeData.code || '未命名'}
              </span>
            </div>
            <div className="flex gap-1 flex-shrink-0">
              <Button size="sm" variant="ghost" onClick={handleEdit} className="h-6 w-6 p-0">
                <Edit2 className="w-3 h-3" />
              </Button>
              <Button size="sm" variant="ghost" onClick={onRemove} className="h-6 w-6 p-0">
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          </div>
          
          {/* Second Row: Badges for Measurement and Registration Types */}
          <div className="flex flex-wrap gap-1">
            {routeData.measurement_types && routeData.measurement_types.length > 0 && (
              <>
                {routeData.measurement_types.slice(0, 2).map((type: string) => {
                  const typeInfo = MEASUREMENT_TYPES.find(t => t.value === type)
                  return (
                    <Badge key={type} variant="secondary" className="text-xs px-1.5 py-0 h-4">
                      {typeInfo?.label || type}
                    </Badge>
                  )
                })}
                {routeData.measurement_types.length > 2 && (
                  <Badge variant="outline" className="text-xs px-1.5 py-0 h-4">
                    +{routeData.measurement_types.length - 2}
                  </Badge>
                )}
              </>
            )}
            {routeData.registration_types && routeData.registration_types.length > 0 && (
              <>
                {routeData.registration_types.slice(0, 2).map((type: string) => {
                  const typeInfo = REGISTRATION_TYPES.find(t => t.value === type)
                  return (
                    <Badge key={type} variant="default" className="text-xs px-1.5 py-0 h-4">
                      {typeInfo?.label || type}
                    </Badge>
                  )
                })}
                {routeData.registration_types.length > 2 && (
                  <Badge variant="outline" className="text-xs px-1.5 py-0 h-4">
                    +{routeData.registration_types.length - 2}
                  </Badge>
                )}
              </>
            )}
          </div>
          
          {/* Third Row: Skill Name (left) and Price (right) */}
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground truncate flex-1 min-w-0">
              {routeData.skill_name || routeData.skill_code || '未设置技能'}
            </span>
            <span className="font-medium text-green-600 flex-shrink-0 ml-2">
              ¥{routeData.price?.toFixed(2) || '0.00'}
            </span>
          </div>
        </CardContent>
      </Card>
      
      <CraftRouteEditDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        partTypeIndex={partTypeIndex}
        craftIndex={craftIndex}
        routeIndex={routeIndex}
      />
    </>
  )
}

export default PartTypeCraftRouteCards
