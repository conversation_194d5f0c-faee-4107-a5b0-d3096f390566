import React, { useState, useEffect } from 'react'
import { useMutation } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Link } from 'lucide-react'
import { toast } from 'sonner'
import type { 
  OrderBundleResponseDto, 
  OrderPartResponseDto, 
  OrderLineResponseDto,
  OrderBundleCreateDto,
  OrderBundleUpdateDto
} from '@/services/types.gen'
import { createOrderBundleApiV1OrderBundlesPostMutation, updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation } from '@/services/@tanstack/react-query.gen'

interface ConfigureBundlesDialogProps {
  orderLines: OrderLineResponseDto[]
  orderParts: OrderPartResponseDto[]
  existingBundles?: OrderBundleResponseDto[]  // Add existing bundles prop
  orderNo: string  // Add order number
  skcNo: string    // Add SKC number
  onSave?: (bundles: OrderBundleResponseDto[]) => Promise<void>
  trigger?: React.ReactNode
}

export const ConfigureBundlesDialog: React.FC<ConfigureBundlesDialogProps> = ({ 
  orderLines, 
  orderParts, 
  existingBundles = [],
  orderNo,
  skcNo,
  onSave,
  trigger
}) => {
  const [open, setOpen] = useState(false)
  const [bundleConfigs, setBundleConfigs] = useState<Record<string, { bundles: number; quantityPerBundle: number }>>({})
  const [isCreating, setIsCreating] = useState(false)

  // Mutations for creating and updating bundles
  const createBundleMutation = useMutation(createOrderBundleApiV1OrderBundlesPostMutation())
  const updateBundleMutation = useMutation(updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation())

  // Initialize bundle configs from existing bundles
  useEffect(() => {
    if (existingBundles.length > 0 && Object.keys(bundleConfigs).length === 0) {
      const configs: Record<string, { bundles: number; quantityPerBundle: number }> = {}
      
      existingBundles.forEach(bundle => {
        const key = `${bundle.order_part_no}-${bundle.size}`
        if (!configs[key]) {
          configs[key] = {
            bundles: 0,
            quantityPerBundle: bundle.quantity
          }
        }
        configs[key].bundles += 1
      })
      
      setBundleConfigs(configs)
    }
  }, [existingBundles, bundleConfigs])

  const generateBundles = (): { toCreate: OrderBundleCreateDto[], toUpdate: { bundle: OrderBundleResponseDto, updates: Partial<OrderBundleUpdateDto> }[] } => {
    const toCreate: OrderBundleCreateDto[] = []
    const toUpdate: { bundle: OrderBundleResponseDto, updates: Partial<OrderBundleUpdateDto> }[] = []
    
    orderParts.forEach(part => {
      orderLines.forEach(line => {
        const key = `${part.order_part_no}-${line.size}`
        const config = bundleConfigs[key]
        
        if (config && config.bundles > 0) {
          // Find existing bundles for this part and size
          const existingBundlesForSize = existingBundles.filter(
            bundle => bundle.order_part_no === part.order_part_no && bundle.size === line.size
          )
          
          // Update existing bundles if quantity changed
          existingBundlesForSize.slice(0, config.bundles).forEach((bundle) => {
            if (bundle.quantity !== config.quantityPerBundle) {
              toUpdate.push({
                bundle,
                updates: {
                  quantity: config.quantityPerBundle
                }
              })
            }
          })
          
          // Create new bundles if we need more than existing
          const bundlesToCreate = Math.max(0, config.bundles - existingBundlesForSize.length)
          for (let i = 0; i < bundlesToCreate; i++) {
            const bundle: OrderBundleCreateDto = {
              size: line.size,
              quantity: config.quantityPerBundle,
              color: part.color,
              order_no: orderNo,
              order_part_no: part.order_part_no,
              skc_no: skcNo,
              bundle_sequence: existingBundlesForSize.length + i + 1,
              notes: null,
              planned_start_date: null,
              planned_end_date: null,
              cutting_machine: null,
              sewing_machine: null,
              cutter_user_id: null,
              sewer_user_id: null,
              qc_user_id: null
            }
            toCreate.push(bundle)
          }
        }
      })
    })
    
    return { toCreate, toUpdate }
  }

  const updateBundleConfig = (partNo: string, size: string, field: 'bundles' | 'quantityPerBundle', value: number) => {
    const key = `${partNo}-${size}`
    setBundleConfigs(prev => ({
      ...prev,
      [key]: {
        ...(prev[key] || { bundles: 1, quantityPerBundle: 1 }),
        [field]: value
      }
    }))
  }

  const handleSave = async () => {
    setIsCreating(true)
    try {
      const { toCreate, toUpdate } = generateBundles()
      
      const allUpdatedBundles: OrderBundleResponseDto[] = []
      
      // Create new bundles
      for (const bundleData of toCreate) {
        const result = await createBundleMutation.mutateAsync({
          body: bundleData
        })
        allUpdatedBundles.push(result)
      }
      
      // Update existing bundles
      for (const { bundle, updates } of toUpdate) {
        const result = await updateBundleMutation.mutateAsync({
          body: updates,
          path: {
            order_bundle_id: bundle.id
          }
        })
        allUpdatedBundles.push(result)
      }
      
      // Call onSave callback if provided
      if (onSave) {
        await onSave(allUpdatedBundles)
      }
      
      const totalChanges = toCreate.length + toUpdate.length
      toast.success(`成功${toCreate.length > 0 ? '创建' : ''}${toCreate.length > 0 && toUpdate.length > 0 ? '和' : ''}${toUpdate.length > 0 ? '更新' : ''} ${totalChanges} 个扎配置`)
      setOpen(false)
    } catch (error) {
      console.error('保存扎配置失败:', error)
      toast.error('保存失败，请稍后重试')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm" variant="outline">
            <Link className="w-4 h-4 mr-2" />
            配置分扎
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>配置分扎</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {orderParts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">请先配置分床</p>
            </div>
          ) : (
            <div className="space-y-6">
              {orderParts.map(part => (
                <Card key={part.id}>
                  <CardHeader>
                    <CardTitle className="text-lg">{part.part_name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {orderLines.map(line => {
                        const key = `${part.order_part_no}-${line.size}`
                        const config = bundleConfigs[key] || { bundles: 1, quantityPerBundle: line.amount }
                        
                        return (
                          <div key={key} className="grid grid-cols-5 gap-4 items-center p-4 border rounded-lg">
                            <div className="text-center">
                              <div className="w-12 h-8 bg-primary/10 rounded flex items-center justify-center text-sm font-medium mx-auto">
                                {line.size}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                需求: {line.amount} 件
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium">扎数</label>
                              <Input
                                type="number"
                                min="1"
                                value={config.bundles}
                                onChange={(e) => updateBundleConfig(part.order_part_no, line.size, 'bundles', parseInt(e.target.value) || 1)}
                              />
                            </div>
                            <div>
                              <label className="text-sm font-medium">每扎件数</label>
                              <Input
                                type="number"
                                min="1"
                                value={config.quantityPerBundle}
                                onChange={(e) => updateBundleConfig(part.order_part_no, line.size, 'quantityPerBundle', parseInt(e.target.value) || 1)}
                              />
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-medium">总计</div>
                              <div className="text-lg">{config.bundles * config.quantityPerBundle}</div>
                            </div>
                            <div className="text-center">
                              {config.bundles * config.quantityPerBundle !== line.amount && (
                                <Badge variant="destructive" className="text-xs">
                                  差额: {line.amount - (config.bundles * config.quantityPerBundle)}
                                </Badge>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setOpen(false)} disabled={isCreating}>
                  取消
                </Button>
                <Button onClick={handleSave} disabled={isCreating}>
                  {isCreating ? '保存中...' : '保存配置'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
