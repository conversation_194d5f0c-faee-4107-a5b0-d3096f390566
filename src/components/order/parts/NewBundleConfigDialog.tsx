import React, { useState, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Package2, Plus, Trash2, Zap, Divide, RotateCcw } from 'lucide-react'
import { toast } from 'sonner'
import type { 
  OrderBundleResponseDto, 
  OrderPartResponseDto, 
  OrderLineResponseDto,
  OrderBundleCreateDto
} from '@/services/types.gen'
import { 
  createOrderBundleApiV1OrderBundlesPostMutation, 
  updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation,
  searchOrderBundlesApiV1OrderBundlesGetQueryKey
} from '@/services/@tanstack/react-query.gen'

interface NewBundleConfigDialogProps {
  orderPart: OrderPartResponseDto
  orderLines: OrderLineResponseDto[]
  existingBundles?: OrderBundleResponseDto[]
  orderNo: string
  skcNo?: string
  onSave?: () => Promise<void>
  trigger?: React.ReactNode
}

interface BundleConfig {
  id?: number  // existing bundle ID, undefined for new bundles
  quantity: number
  isNew: boolean
}

interface SizeBundleState {
  size: string
  requiredQuantity: number
  bundles: BundleConfig[]
  totalAllocated: number
}

export const NewBundleConfigDialog: React.FC<NewBundleConfigDialogProps> = ({ 
  orderPart,
  orderLines,
  existingBundles = [],
  orderNo,
  skcNo,
  onSave,
  trigger
}) => {
  const [open, setOpen] = useState(false)
  const [sizeStates, setSizeStates] = useState<SizeBundleState[]>([])
  const [isCreating, setIsCreating] = useState(false)
  const [bundleCountInputs, setBundleCountInputs] = useState<{[key: number]: string}>({})
  
  const queryClient = useQueryClient()

  // Mutations
  const createBundleMutation = useMutation(createOrderBundleApiV1OrderBundlesPostMutation())
  const updateBundleMutation = useMutation(updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation())

  // Initialize size states
  useEffect(() => {
    if (open) {
      const states: SizeBundleState[] = orderLines.map(line => {
        // 找到该尺码现有的分扎
        const existingBundlesForSize = existingBundles.filter(
          bundle => bundle.size === line.size && bundle.order_part_no === orderPart.order_part_no
        )
        
        // 转换现有分扎为配置对象
        const existingConfigs: BundleConfig[] = existingBundlesForSize.map(bundle => ({
          id: bundle.id,
          quantity: bundle.quantity,
          isNew: false
        }))
        
        // 如果没有现有分扎，创建一个默认分扎
        const bundles = existingConfigs.length > 0 ? existingConfigs : [{
          quantity: Math.min(line.amount, 10),
          isNew: true
        }]
        
        const totalAllocated = bundles.reduce((sum, bundle) => sum + bundle.quantity, 0)
        
        return {
          size: line.size,
          requiredQuantity: line.amount,
          bundles,
          totalAllocated
        }
      })
      
      setSizeStates(states)
    }
  }, [open, orderLines, existingBundles, orderPart.order_part_no])

  // 重新计算总分配数量
  const recalculateTotal = (states: SizeBundleState[]) => {
    return states.map(state => ({
      ...state,
      totalAllocated: state.bundles.reduce((sum, bundle) => sum + bundle.quantity, 0)
    }))
  }

  // 更新特定尺码的分扎数量
  const updateBundleQuantity = (sizeIndex: number, bundleIndex: number, quantity: number) => {
    setSizeStates(prev => {
      const updated = [...prev]
      updated[sizeIndex] = {
        ...updated[sizeIndex],
        bundles: updated[sizeIndex].bundles.map((bundle, idx) => 
          idx === bundleIndex ? { ...bundle, quantity: Math.max(1, quantity) } : bundle
        )
      }
      return recalculateTotal(updated)
    })
  }

  // 添加新分扎
  const addBundle = (sizeIndex: number) => {
    setSizeStates(prev => {
      const updated = [...prev]
      const avgQuantity = Math.ceil(updated[sizeIndex].requiredQuantity / (updated[sizeIndex].bundles.length + 1))
      updated[sizeIndex] = {
        ...updated[sizeIndex],
        bundles: [...updated[sizeIndex].bundles, {
          quantity: avgQuantity,
          isNew: true
        }]
      }
      return recalculateTotal(updated)
    })
  }

  // 删除分扎
  const removeBundle = (sizeIndex: number, bundleIndex: number) => {
    setSizeStates(prev => {
      const updated = [...prev]
      if (updated[sizeIndex].bundles.length > 1) {
        updated[sizeIndex] = {
          ...updated[sizeIndex],
          bundles: updated[sizeIndex].bundles.filter((_, idx) => idx !== bundleIndex)
        }
      }
      return recalculateTotal(updated)
    })
  }

  // 快速分扎选项
  const quickAllocate = (sizeIndex: number, strategy: 'even' | 'optimal' | 'reset') => {
    setSizeStates(prev => {
      const updated = [...prev]
      const state = updated[sizeIndex]
      
      switch (strategy) {
        case 'even': {
          // 平均分配到现有扎数
          const bundleCount = state.bundles.length
          const avgQuantity = Math.floor(state.requiredQuantity / bundleCount)
          const remainder = state.requiredQuantity % bundleCount
          
          updated[sizeIndex] = {
            ...state,
            bundles: state.bundles.map((bundle, idx) => ({
              ...bundle,
              quantity: avgQuantity + (idx < remainder ? 1 : 0)
            }))
          }
          break
        }
        case 'optimal': {
          // 智能分配：尽量每扎10-15件
          const idealQuantity = Math.min(Math.max(Math.ceil(state.requiredQuantity / 8), 10), 15)
          const optimalBundleCount = Math.ceil(state.requiredQuantity / idealQuantity)
          const actualQuantity = Math.ceil(state.requiredQuantity / optimalBundleCount)
          
          const newBundles: BundleConfig[] = []
          for (let i = 0; i < optimalBundleCount; i++) {
            newBundles.push({
              quantity: actualQuantity,
              isNew: true
            })
          }
          
          updated[sizeIndex] = {
            ...state,
            bundles: newBundles
          }
          break
        }
        case 'reset': {
          // 重置为单个分扎
          updated[sizeIndex] = {
            ...state,
            bundles: [{
              quantity: state.requiredQuantity,
              isNew: true
            }]
          }
          break
        }
      }
      
      return recalculateTotal(updated)
    })
  }

  // 设置扎数
  const setBundleCount = (sizeIndex: number, count: number) => {
    if (count < 1) return
    
    setSizeStates(prev => {
      const updated = [...prev]
      const state = updated[sizeIndex]
      const avgQuantity = Math.floor(state.requiredQuantity / count)
      const remainder = state.requiredQuantity % count
      
      const newBundles: BundleConfig[] = []
      for (let i = 0; i < count; i++) {
        newBundles.push({
          quantity: avgQuantity + (i < remainder ? 1 : 0),
          isNew: true
        })
      }
      
      updated[sizeIndex] = {
        ...state,
        bundles: newBundles
      }
      
      return recalculateTotal(updated)
    })
  }

  // 处理扎数输入
  const handleBundleCountInput = (sizeIndex: number, value: string) => {
    setBundleCountInputs(prev => ({
      ...prev,
      [sizeIndex]: value
    }))
  }

  // 应用扎数设置
  const applyBundleCount = (sizeIndex: number) => {
    const inputValue = bundleCountInputs[sizeIndex]
    if (inputValue) {
      const count = parseInt(inputValue)
      if (count > 0) {
        setBundleCount(sizeIndex, count)
        setBundleCountInputs(prev => ({
          ...prev,
          [sizeIndex]: ''
        }))
      }
    }
  }

  // 保存分扎配置
  const handleSave = async () => {
    try {
      setIsCreating(true)
      
      for (const sizeState of sizeStates) {
        // 找到该尺码的现有分扎
        const existingBundlesForSize = existingBundles.filter(
          bundle => bundle.size === sizeState.size && bundle.order_part_no === orderPart.order_part_no
        )
        
        // 删除多余的现有分扎
        const bundlesToKeep = sizeState.bundles.filter(b => !b.isNew).length
        for (let i = bundlesToKeep; i < existingBundlesForSize.length; i++) {
          // TODO: 实现删除分扎的API调用
          console.log(`删除分扎 ${existingBundlesForSize[i].id}`)
        }
        
        // 更新现有分扎
        for (const bundle of sizeState.bundles) {
          if (!bundle.isNew && bundle.id) {
            await updateBundleMutation.mutateAsync({
              path: { order_bundle_id: bundle.id },
              body: { quantity: bundle.quantity }
            })
          }
        }
        
        // 创建新分扎
        let bundleSequence = existingBundlesForSize.length + 1
        for (const bundle of sizeState.bundles) {
          if (bundle.isNew) {
            const newBundle: OrderBundleCreateDto = {
              size: sizeState.size,
              quantity: bundle.quantity,
              color: orderPart.color,
              order_no: orderNo,
              order_part_no: orderPart.order_part_no,
              skc_no: skcNo || orderNo,
              bundle_sequence: bundleSequence++,
              notes: null,
              planned_start_date: null,
              planned_end_date: null,
              cutting_machine: null,
              sewing_machine: null,
              cutter_user_id: null,
              sewer_user_id: null,
              qc_user_id: null
            }
            
            await createBundleMutation.mutateAsync({
              body: newBundle
            })
          }
        }
      }
      
      // 刷新数据
      await queryClient.invalidateQueries({
        queryKey: searchOrderBundlesApiV1OrderBundlesGetQueryKey()
      })
      
      if (onSave) {
        await onSave()
      }
      
      toast.success(`${orderPart.part_name} 分扎配置已保存`)
      setOpen(false)
      
    } catch (error) {
      console.error('保存分扎配置失败:', error)
      toast.error('保存失败，请稍后重试')
    } finally {
      setIsCreating(false)
    }
  }

  const totalRequired = sizeStates.reduce((sum, state) => sum + state.requiredQuantity, 0)
  const totalAllocated = sizeStates.reduce((sum, state) => sum + state.totalAllocated, 0)
  const hasDiscrepancy = sizeStates.some(state => state.totalAllocated !== state.requiredQuantity)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm">
            <Package2 className="w-4 h-4 mr-2" />
            配置分扎
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-6xl h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Package2 className="w-4 h-4" />
            分扎配置 - {orderPart.part_name}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 py-4">
          {/* 总体统计 */}
          <div className="grid grid-cols-3 gap-3 p-2 bg-gray-50 rounded text-center text-sm">
            <div>
              <div className="text-base font-bold">{totalRequired}</div>
              <div className="text-muted-foreground text-xs">总需求</div>
            </div>
            <div>
              <div className="text-base font-bold">{totalAllocated}</div>
              <div className="text-muted-foreground text-xs">已分配</div>
            </div>
            <div>
              <div className={`text-base font-bold ${
                totalAllocated === totalRequired ? 'text-green-600' : 
                totalAllocated > totalRequired ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {totalAllocated - totalRequired}
              </div>
              <div className="text-muted-foreground text-xs">差额</div>
            </div>
          </div>

          {/* 尺码分扎配置 */}
          <div className="space-y-3">
            {sizeStates.map((sizeState, sizeIndex) => {
              const isOverAllocated = sizeState.totalAllocated > sizeState.requiredQuantity
              const isUnderAllocated = sizeState.totalAllocated < sizeState.requiredQuantity
              const isExact = sizeState.totalAllocated === sizeState.requiredQuantity
              
              return (
                <Card key={sizeState.size} className={`${
                  isOverAllocated ? 'border-red-200 bg-red-50' : 
                  isUnderAllocated ? 'border-yellow-200 bg-yellow-50' : 
                  'border-green-200 bg-green-50'
                }`}>
                  <CardContent className="p-3">
                    {/* 上部分：快速操作 */}
                    <div className="flex justify-between items-center mb-2 pb-1 border-b border-gray-200">
                      <Badge variant="outline" className="text-lg px-3 py-1">
                        {sizeState.size}
                      </Badge>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addBundle(sizeIndex)}
                          className="h-6 text-xs px-2"
                        >
                          <Plus className="w-3 h-3 mr-1" />
                          添加扎
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => quickAllocate(sizeIndex, 'even')}
                          className="h-6 text-xs px-2"
                        >
                          <Divide className="w-3 h-3 mr-1" />
                          平均分配
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => quickAllocate(sizeIndex, 'optimal')}
                          className="h-6 text-xs px-2"
                        >
                          <Zap className="w-3 h-3 mr-1" />
                          智能分配
                        </Button>
                        <div className="flex items-center">
                          <Input
                            type="number"
                            placeholder="扎数"
                            value={bundleCountInputs[sizeIndex] || ''}
                            onChange={(e) => handleBundleCountInput(sizeIndex, e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                applyBundleCount(sizeIndex)
                              }
                            }}
                            className="h-6 text-xs w-16 rounded-r-none"
                            min="1"
                          />
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => applyBundleCount(sizeIndex)}
                            className="h-6 text-xs px-1 rounded-l-none border-l-0"
                          >
                            <RotateCcw className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* 下部分：详细信息和分扎配置 */}
                    <div className="grid grid-cols-12 gap-3 items-start">
                      {/* 左侧：尺码需求信息 */}
                      <div className="col-span-2">
                        <div className="text-center space-y-1">
                          <div className="text-xs text-muted-foreground">
                            需求: {sizeState.requiredQuantity}
                          </div>
                          <div className={`text-xs font-medium ${
                            isOverAllocated ? 'text-red-600' : 
                            isUnderAllocated ? 'text-yellow-600' : 
                            'text-green-600'
                          }`}>
                            已分: {sizeState.totalAllocated}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            扎数: {sizeState.bundles.length}
                          </div>
                          {!isExact && (
                            <Badge variant={isOverAllocated ? 'destructive' : 'secondary'} className="text-xs">
                              {isOverAllocated ? '超' : '缺'} {Math.abs(sizeState.totalAllocated - sizeState.requiredQuantity)}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* 右侧：分扎详细配置 */}
                      <div className="col-span-10">
                        <div className="space-y-1">
                          <Label className="text-xs font-medium">分扎详情</Label>
                          
                          {/* 可滑动的分扎卡片容器 */}
                          <div className="relative">
                            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                              {sizeState.bundles.map((bundle, bundleIndex) => (
                                <div 
                                  key={bundleIndex} 
                                  className="flex-shrink-0 w-32 bg-white rounded border shadow-sm hover:shadow-md transition-shadow relative"
                                >
                                  {/* 删除按钮 - 右上角 */}
                                  {sizeState.bundles.length > 1 && (
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => removeBundle(sizeIndex, bundleIndex)}
                                      className="absolute top-0.5 right-0.5 h-4 w-4 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <Trash2 className="w-2.5 h-2.5" />
                                    </Button>
                                  )}
                                  
                                  {/* 卡片内容 */}
                                  <div className="p-2 pt-5">
                                    <div className="text-center space-y-1.5">
                                      {/* 扎号 */}
                                      <Badge 
                                        variant={bundle.isNew ? 'default' : 'secondary'} 
                                        className="text-xs px-1.5 py-0.5"
                                      >
                                        第 {bundleIndex + 1} 扎
                                      </Badge>
                                      
                                      {/* 数量控制 */}
                                      <div>
                                        <Input
                                          type="number"
                                          value={bundle.quantity}
                                          onChange={(e) => updateBundleQuantity(sizeIndex, bundleIndex, parseInt(e.target.value) || 1)}
                                          className="w-full h-6 text-center text-xs"
                                          min="1"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                              
                              {/* 添加新扎的占位卡片 */}
                              <div className="flex-shrink-0 w-32">
                                <Button
                                  variant="outline"
                                  onClick={() => addBundle(sizeIndex)}
                                  className="w-full h-20 border-2 border-dashed border-gray-300 hover:border-gray-400 flex flex-col items-center justify-center gap-1 text-gray-500 hover:text-gray-600 bg-gray-50 hover:bg-gray-100"
                                >
                                  <Plus className="w-4 h-4" />
                                  <span className="text-xs">添加新扎</span>
                                </Button>
                              </div>
                            </div>
                            
                            {/* 滑动提示 */}
                            {sizeState.bundles.length > 2 && (
                              <div className="absolute top-0 right-0 text-xs text-muted-foreground bg-white px-1 py-0.5 rounded-bl border-l border-b">
                                可滑动
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* 操作按钮 - 固定在底部 */}
        <div className="flex-shrink-0 flex justify-between pt-3 border-t bg-white">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isCreating}
            size="sm"
          >
            取消
          </Button>
          <div className="flex gap-2 items-center">
            {hasDiscrepancy && (
              <div className="flex items-center text-xs text-yellow-600">
                ⚠️ 存在差异
              </div>
            )}
            <Button
              onClick={handleSave}
              disabled={isCreating}
              size="sm"
              className={!hasDiscrepancy ? '' : 'bg-yellow-600 hover:bg-yellow-700'}
            >
              {isCreating ? '保存中...' : '保存配置'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
