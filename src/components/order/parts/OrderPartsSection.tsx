import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Plus, Edit2, Package2 } from 'lucide-react'
import type { OrderPartResponseDto, OrderLineResponseDto, OrderBundleResponseDto } from '@/services/types.gen'
import { ConfigurePartsDialog } from './ConfigurePartsDialog'
import { NewBundleConfigDialog } from './NewBundleConfigDialog'
import { searchOrderBundlesApiV1OrderBundlesGetOptions } from '@/services/@tanstack/react-query.gen'

interface OrderPartsSectionProps {
  orderLines: OrderLineResponseDto[]
  parts: OrderPartResponseDto[]
  existingBundles?: OrderBundleResponseDto[]  // 现有分扎数据
  orderNo: string
  skcNo: string
  onUpdate?: (parts: OrderPartResponseDto[]) => Promise<void>
  onBundlesUpdate?: () => Promise<void>  // 分扎更新回调
  editable: boolean
}

export const OrderPartsSection: React.FC<OrderPartsSectionProps> = ({ 
  orderLines, 
  parts, 
  existingBundles = [],
  orderNo,
  skcNo,
  onUpdate, 
  onBundlesUpdate,
  editable 
}) => {
  // Fetch bundles for all parts
  const { data: bundlesResponse, refetch: refetchBundles } = useQuery(
    searchOrderBundlesApiV1OrderBundlesGetOptions({
      query: {
        order_no: orderNo,
      }
    })
  )

  const allBundles = bundlesResponse?.order_bundles || []

  const handleBundlesUpdate = async () => {
    await refetchBundles()
    if (onBundlesUpdate) {
      await onBundlesUpdate()
    }
  }
  if (parts.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">暂无分床数据</p>
        {editable && (
          <ConfigurePartsDialog 
            orderLines={orderLines}
            existingParts={parts}
            orderNo={orderNo}
            skcNo={skcNo}
            onSave={onUpdate}
            trigger={
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                配置分床
              </Button>
            }
          />
        )}
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-end mb-4 gap-2">
        {editable && (
          <ConfigurePartsDialog 
            orderLines={orderLines}
            existingParts={parts}
            orderNo={orderNo}
            skcNo={skcNo}
            onSave={onUpdate}
            trigger={
              <Button size="sm" variant="outline">
                <Edit2 className="w-4 h-4 mr-2" />
                编辑分床
              </Button>
            }
          />
        )}
      </div>
      
      <div className="grid gap-6">
        {parts.map((part) => (
          <Card key={part.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Package2 className="w-5 h-5" />
                  <div>
                    <CardTitle className="text-lg">{part.part_name}</CardTitle>
                    <div className="text-sm text-muted-foreground">
                      {part.order_part_no} • {part.part_type} • {part.color}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {editable && (
                    <NewBundleConfigDialog
                      orderPart={part}
                      orderLines={orderLines}
                      existingBundles={allBundles.filter(b => b.order_part_no === part.order_part_no)}
                      orderNo={orderNo}
                      skcNo={skcNo}
                      onSave={handleBundlesUpdate}
                      trigger={
                        <Button size="sm" variant="outline">
                          <Package2 className="w-4 h-4 mr-2" />
                          配置分扎
                        </Button>
                      }
                    />
                  )}
                  <Badge>{part.status}</Badge>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {part.completed_quantity}/{part.total_quantity} 件
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {part.progress_percentage}% 完成
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="sizes" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="sizes">尺码分配</TabsTrigger>
                  <TabsTrigger value="bundles">分扎详情</TabsTrigger>
                </TabsList>
                
                <TabsContent value="sizes" className="space-y-4">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>负责人: {part.supervisor_name || '-'}</span>
                    <span>总量: {part.total_quantity} 件</span>
                  </div>
                  
                  {/* Size breakdown - shows original order line requirements */}
                  <div>
                    <div className="text-sm font-medium mb-3">尺码分配情况</div>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                      {orderLines.map((line) => {
                        // TODO: Get actual part size allocation from API
                        // For now showing total requirement per size
                        const sizeAmount = Math.round((line.amount * part.total_quantity) / orderLines.reduce((sum, l) => sum + l.amount, 0));
                        
                        // Count bundles for this size and part
                        const bundlesForSize = allBundles.filter(b => 
                          b.order_part_no === part.order_part_no && b.size === line.size
                        ).length;
                        
                        return (
                          <div key={`${part.id}-${line.size}`} className="text-center p-4 border rounded-lg">
                            <div className="text-xl font-bold">{line.size}</div>
                            <div className="text-sm text-muted-foreground">{sizeAmount} 件</div>
                            <div className="text-xs text-blue-600">
                              {bundlesForSize} 扎
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="bundles" className="space-y-4">
                  {(() => {
                    const partBundles = allBundles.filter(b => b.order_part_no === part.order_part_no)
                    
                    if (partBundles.length === 0) {
                      return (
                        <div className="text-center py-8">
                          <p className="text-muted-foreground mb-4">暂无分扎数据</p>
                          {editable && (
                            <NewBundleConfigDialog
                              orderPart={part}
                              orderLines={orderLines}
                              existingBundles={partBundles}
                              orderNo={orderNo}
                              skcNo={skcNo}
                              onSave={handleBundlesUpdate}
                              trigger={
                                <Button size="sm">
                                  <Plus className="w-4 h-4 mr-2" />
                                  创建分扎
                                </Button>
                              }
                            />
                          )}
                        </div>
                      )
                    }
                    
                    return (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                              共 {partBundles.length} 个分扎
                            </span>
                            <Badge variant="outline">{partBundles.length} 扎</Badge>
                          </div>
                        </div>
                        
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>扎编号</TableHead>
                              <TableHead>尺码</TableHead>
                              <TableHead>数量</TableHead>
                              <TableHead>颜色</TableHead>
                              <TableHead>状态</TableHead>
                              <TableHead>完成进度</TableHead>
                              <TableHead>负责人</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {partBundles.map((bundle) => (
                              <TableRow key={bundle.id}>
                                <TableCell className="font-mono">{bundle.order_bundle_no}</TableCell>
                                <TableCell>
                                  <Badge variant="outline">{bundle.size}</Badge>
                                </TableCell>
                                <TableCell>{bundle.quantity} 件</TableCell>
                                <TableCell>{bundle.color}</TableCell>
                                <TableCell>
                                  <Badge>{bundle.status}</Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <div className="text-sm">
                                      {bundle.completed_quantity}/{bundle.quantity}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      ({bundle.progress_percentage}%)
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {bundle.sewer_name || bundle.cutter_name || '-'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )
                  })()}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
