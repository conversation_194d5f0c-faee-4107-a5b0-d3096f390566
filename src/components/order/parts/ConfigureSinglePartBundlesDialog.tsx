import React, { useState, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Package2, Plus, Minus } from 'lucide-react'
import { toast } from 'sonner'
import type { 
  OrderBundleResponseDto, 
  OrderPartResponseDto, 
  OrderLineResponseDto,
  OrderBundleCreateDto,
  OrderBundleUpdateDto
} from '@/services/types.gen'
import { 
  createOrderBundleApiV1OrderBundlesPostMutation, 
  updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation,
  searchOrderBundlesApiV1OrderBundlesGetQueryKey
} from '@/services/@tanstack/react-query.gen'

interface ConfigureSinglePartBundlesDialogProps {
  orderPart: OrderPartResponseDto  // 单个 part
  orderLines: OrderLineResponseDto[]  // 订单行（尺码需求）
  existingBundles?: OrderBundleResponseDto[]  // 该 part 现有的分扎
  orderNo: string
  skcNo?: string  // 款号，可选
  onSave?: () => Promise<void>
  trigger?: React.ReactNode
}

interface SizeBundleConfig {
  size: string
  requiredQuantity: number
  bundleCount: number
  quantityPerBundle: number
  totalAllocated: number
}

export const ConfigureSinglePartBundlesDialog: React.FC<ConfigureSinglePartBundlesDialogProps> = ({ 
  orderPart,
  orderLines,
  existingBundles = [],
  orderNo,
  skcNo,
  onSave,
  trigger
}) => {
  const [open, setOpen] = useState(false)
  const [sizeConfigs, setSizeConfigs] = useState<SizeBundleConfig[]>([])
  const [isCreating, setIsCreating] = useState(false)
  
  const queryClient = useQueryClient()

  // Mutations
  const createBundleMutation = useMutation(createOrderBundleApiV1OrderBundlesPostMutation())
  const updateBundleMutation = useMutation(updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation())

  // Initialize size configs
  useEffect(() => {
    if (open) {
      const configs: SizeBundleConfig[] = orderLines.map(line => {
        // 找到该尺码现有的分扎
        const existingBundlesForSize = existingBundles.filter(
          bundle => bundle.size === line.size && bundle.order_part_no === orderPart.order_part_no
        )
        
        // 计算现有分扎的总数量
        const totalExistingQuantity = existingBundlesForSize.reduce((sum, bundle) => sum + bundle.quantity, 0)
        
        // 如果有现有分扎，使用第一个的数量作为默认值，否则使用合理的默认值
        const defaultQuantityPerBundle = existingBundlesForSize.length > 0 
          ? existingBundlesForSize[0].quantity 
          : Math.min(line.amount, 10) // 默认每扎10件，但不超过总需求
          
        return {
          size: line.size,
          requiredQuantity: line.amount,
          bundleCount: existingBundlesForSize.length,
          quantityPerBundle: defaultQuantityPerBundle,
          totalAllocated: totalExistingQuantity
        }
      })
      
      setSizeConfigs(configs)
    }
  }, [open, orderLines, existingBundles, orderPart.order_part_no])

  // 更新单个尺码的配置
  const updateSizeConfig = (size: string, field: 'bundleCount' | 'quantityPerBundle', value: number) => {
    setSizeConfigs(prev => prev.map(config => {
      if (config.size === size) {
        const updated = { ...config, [field]: Math.max(0, value) }
        updated.totalAllocated = updated.bundleCount * updated.quantityPerBundle
        return updated
      }
      return config
    }))
  }

  // 智能分配：根据需求自动计算分扎
  const autoAllocateForSize = (size: string) => {
    setSizeConfigs(prev => prev.map(config => {
      if (config.size === size) {
        const { requiredQuantity } = config
        // 智能分配策略：尽量每扎10-20件
        const idealQuantityPerBundle = Math.min(Math.max(Math.ceil(requiredQuantity / 10), 5), 20)
        const bundleCount = Math.ceil(requiredQuantity / idealQuantityPerBundle)
        const actualQuantityPerBundle = Math.ceil(requiredQuantity / bundleCount)
        
        return {
          ...config,
          bundleCount,
          quantityPerBundle: actualQuantityPerBundle,
          totalAllocated: bundleCount * actualQuantityPerBundle
        }
      }
      return config
    }))
  }

  // 保存分扎配置
  const handleSave = async () => {
    try {
      setIsCreating(true)
      
      // 为每个尺码处理分扎
      for (const config of sizeConfigs) {
        const existingBundlesForSize = existingBundles.filter(
          bundle => bundle.size === config.size && bundle.order_part_no === orderPart.order_part_no
        )
        
        // 删除多余的分扎（如果新配置的分扎数量少于现有数量）
        if (existingBundlesForSize.length > config.bundleCount) {
          // TODO: 实现删除多余分扎的逻辑
          console.log(`需要删除 ${existingBundlesForSize.length - config.bundleCount} 个分扎`)
        }
        
        // 更新现有分扎的数量
        for (let i = 0; i < Math.min(existingBundlesForSize.length, config.bundleCount); i++) {
          const bundle = existingBundlesForSize[i]
          if (bundle.quantity !== config.quantityPerBundle) {
            await updateBundleMutation.mutateAsync({
              path: { order_bundle_id: bundle.id },
              body: {
                quantity: config.quantityPerBundle
              }
            })
          }
        }
        
        // 创建新的分扎（如果需要）
        const bundlesToCreate = Math.max(0, config.bundleCount - existingBundlesForSize.length)
        for (let i = 0; i < bundlesToCreate; i++) {
          const newBundle: OrderBundleCreateDto = {
            size: config.size,
            quantity: config.quantityPerBundle,
            color: orderPart.color,
            order_no: orderNo,
            order_part_no: orderPart.order_part_no,
            skc_no: skcNo || orderNo, // 使用提供的skcNo，如果没有则使用orderNo
            bundle_sequence: existingBundlesForSize.length + i + 1, // 计算扎序号
            notes: null,
            planned_start_date: null,
            planned_end_date: null,
            cutting_machine: null,
            sewing_machine: null,
            cutter_user_id: null,
            sewer_user_id: null,
            qc_user_id: null
          }
          
          await createBundleMutation.mutateAsync({
            body: newBundle
          })
        }
      }
      
      // 刷新数据
      await queryClient.invalidateQueries({
        queryKey: searchOrderBundlesApiV1OrderBundlesGetQueryKey()
      })
      
      if (onSave) {
        await onSave()
      }
      
      toast.success(`${orderPart.part_name} 分扎配置已保存`)
      setOpen(false)
      
    } catch (error) {
      console.error('保存分扎配置失败:', error)
      toast.error('保存失败，请稍后重试')
    } finally {
      setIsCreating(false)
    }
  }

  const totalRequired = sizeConfigs.reduce((sum, config) => sum + config.requiredQuantity, 0)
  const totalAllocated = sizeConfigs.reduce((sum, config) => sum + config.totalAllocated, 0)
  const isFullyAllocated = totalAllocated >= totalRequired

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm">
            <Package2 className="w-4 h-4 mr-2" />
            配置分扎
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package2 className="w-5 h-5" />
            配置分扎 - {orderPart.part_name}
          </DialogTitle>
          <div className="text-sm text-muted-foreground">
            {orderPart.order_part_no} • {orderPart.part_type} • {orderPart.color}
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* 总体统计 */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">{totalRequired}</div>
                  <div className="text-sm text-muted-foreground">总需求</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">{totalAllocated}</div>
                  <div className="text-sm text-muted-foreground">已分配</div>
                </div>
                <div>
                  <div className={`text-2xl font-bold ${isFullyAllocated ? 'text-green-600' : 'text-red-600'}`}>
                    {totalAllocated - totalRequired}
                  </div>
                  <div className="text-sm text-muted-foreground">差额</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 各尺码分扎配置 */}
          <div className="space-y-4">
            <div className="text-sm font-medium">尺码分扎配置</div>
            {sizeConfigs.map((config) => {
              const isOverAllocated = config.totalAllocated > config.requiredQuantity
              const isUnderAllocated = config.totalAllocated < config.requiredQuantity
              
              return (
                <Card key={config.size} className={`p-4 ${
                  isOverAllocated ? 'border-red-200 bg-red-50' : 
                  isUnderAllocated ? 'border-yellow-200 bg-yellow-50' : 
                  'border-green-200 bg-green-50'
                }`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="text-lg px-3 py-1">
                        {config.size}
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        需求: {config.requiredQuantity} 件
                      </div>
                      <div className={`text-sm font-medium ${
                        isOverAllocated ? 'text-red-600' : 
                        isUnderAllocated ? 'text-yellow-600' : 
                        'text-green-600'
                      }`}>
                        已分配: {config.totalAllocated} 件
                      </div>
                      {(isOverAllocated || isUnderAllocated) && (
                        <Badge variant={isOverAllocated ? 'destructive' : 'secondary'}>
                          {isOverAllocated ? '超额' : '不足'} {Math.abs(config.totalAllocated - config.requiredQuantity)}
                        </Badge>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => autoAllocateForSize(config.size)}
                    >
                      智能分配
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>分扎数量</Label>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateSizeConfig(config.size, 'bundleCount', config.bundleCount - 1)}
                          disabled={config.bundleCount <= 0}
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                        <Input
                          type="number"
                          value={config.bundleCount}
                          onChange={(e) => updateSizeConfig(config.size, 'bundleCount', parseInt(e.target.value) || 0)}
                          className="w-20 text-center"
                          min="0"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateSizeConfig(config.size, 'bundleCount', config.bundleCount + 1)}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                        <span className="text-sm text-muted-foreground">扎</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>每扎数量</Label>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateSizeConfig(config.size, 'quantityPerBundle', config.quantityPerBundle - 1)}
                          disabled={config.quantityPerBundle <= 1}
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                        <Input
                          type="number"
                          value={config.quantityPerBundle}
                          onChange={(e) => updateSizeConfig(config.size, 'quantityPerBundle', parseInt(e.target.value) || 1)}
                          className="w-20 text-center"
                          min="1"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateSizeConfig(config.size, 'quantityPerBundle', config.quantityPerBundle + 1)}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                        <span className="text-sm text-muted-foreground">件/扎</span>
                      </div>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>

          <Separator />

          {/* 操作按钮 */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isCreating}
            >
              取消
            </Button>
            <Button
              onClick={handleSave}
              disabled={isCreating}
              className={isFullyAllocated ? '' : 'bg-yellow-600 hover:bg-yellow-700'}
            >
              {isCreating ? '保存中...' : '保存配置'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
