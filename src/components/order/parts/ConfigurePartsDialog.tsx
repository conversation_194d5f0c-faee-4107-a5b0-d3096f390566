import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>alog, DialogContent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Settings } from 'lucide-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { 
  createOrderPartApiV1OrderPartsPostMutation,
  updateOrderPartApiV1OrderPartsOrderPartIdPutMutation
} from '@/services/@tanstack/react-query.gen'
import type { 
  OrderPartResponseDto, 
  OrderLineResponseDto,
  OrderPartCreateDto,
  OrderPartUpdateDto,
  PartTypeEnum,
  PartStatusEnum 
} from '@/services/types.gen'

interface PartSizeConfig {
  size: string
  amount: number
}

interface PartWithSizes extends Partial<OrderPartResponseDto> {
  sizeConfigs: PartSizeConfig[]
}

interface ConfigurePartsDialogProps {
  orderLines: OrderLineResponseDto[]
  existingParts: OrderPartResponseDto[]
  orderNo: string  // Add order number for creating parts
  skcNo: string    // Add SKC number for creating parts
  onSave?: (parts: OrderPartResponseDto[]) => Promise<void>
  trigger?: React.ReactNode
}

export const ConfigurePartsDialog: React.FC<ConfigurePartsDialogProps> = ({ 
  orderLines, 
  existingParts, 
  orderNo,
  skcNo,
  onSave, 
  trigger 
}) => {
  const [open, setOpen] = useState(false)
  const queryClient = useQueryClient()
  
  // Initialize mutations
  const createPartMutation = useMutation(createOrderPartApiV1OrderPartsPostMutation())
  const updatePartMutation = useMutation(updateOrderPartApiV1OrderPartsOrderPartIdPutMutation())
  
  // Convert existing parts to parts with size configs
  const convertToPartsWithSizes = (parts: OrderPartResponseDto[]): PartWithSizes[] => {
    return parts.map(part => ({
      ...part,
      sizeConfigs: orderLines.map(line => ({
        size: line.size,
        amount: Math.floor(part.total_quantity / orderLines.length) // 平均分配，实际应根据业务逻辑
      }))
    }))
  }

  const [parts, setParts] = useState<PartWithSizes[]>(
    existingParts.length > 0 
      ? convertToPartsWithSizes(existingParts)
      : []
  )

  const addPart = () => {
    const newPart: PartWithSizes = {
      part_name: '',
      part_type: 'front_body' as PartTypeEnum,
      color: '',
      total_quantity: 0,
      description: '',
      notes: '',
      part_sequence: parts.length + 1,
      status: 'planned' as PartStatusEnum,
      completed_quantity: 0,
      progress_percentage: 0,
      sizeConfigs: orderLines.map(line => ({
        size: line.size,
        amount: line.amount // 默认每个床位需要所有尺码的全部数量
      }))
    }
    setParts([...parts, newPart])
  }

  const updatePart = (index: number, field: keyof OrderPartResponseDto, value: string | number) => {
    const updated = [...parts]
    updated[index] = { ...updated[index], [field]: value }
    
    // 如果更新的是总数量，需要重新计算各尺码的分配
    if (field === 'total_quantity') {
      const totalAmount = Number(value)
      const totalOrderAmount = orderLines.reduce((sum, line) => sum + line.amount, 0)
      
      updated[index].sizeConfigs = orderLines.map(line => ({
        size: line.size,
        amount: Math.round((line.amount / totalOrderAmount) * totalAmount)
      }))
    }
    
    setParts(updated)
  }

  const updatePartSizeConfig = (partIndex: number, sizeIndex: number, amount: number) => {
    const updated = [...parts]
    updated[partIndex].sizeConfigs[sizeIndex].amount = amount
    
    // 重新计算总数量
    updated[partIndex].total_quantity = updated[partIndex].sizeConfigs.reduce((sum, config) => sum + config.amount, 0)
    
    setParts(updated)
  }

  const removePart = (index: number) => {
    setParts(parts.filter((_, i) => i !== index))
  }

  const handleSave = async () => {
    try {
      const savedParts: OrderPartResponseDto[] = []
      
      for (const part of parts) {
        const totalQuantity = part.sizeConfigs?.reduce((sum, config) => sum + config.amount, 0) || 0
        
        if (part.id) {
          // Update existing part
          const updateData: OrderPartUpdateDto = {
            part_name: part.part_name,
            color: part.color,
            total_quantity: totalQuantity,
            notes: part.notes,
            description: part.description
          }
          
          const result = await updatePartMutation.mutateAsync({
            path: { order_part_id: part.id },
            body: updateData
          })
          
          savedParts.push(result)
        } else {
          // Create new part
          const createData: OrderPartCreateDto = {
            part_name: part.part_name || '',
            part_type: part.part_type || 'front_body',
            color: part.color || '',
            total_quantity: totalQuantity,
            description: part.description,
            notes: part.notes,
            order_no: orderNo,
            skc_no: skcNo,
            part_sequence: part.part_sequence || savedParts.length + 1
          }
          
          const result = await createPartMutation.mutateAsync({
            body: createData
          })
          
          savedParts.push(result)
        }
      }
      
      // Invalidate queries to refresh data
      await queryClient.invalidateQueries({ 
        queryKey: ['getOrderPartsByOrder', orderNo] 
      })
      
      toast.success('床位配置保存成功！')
      
      if (onSave) {
        await onSave(savedParts)
      }
      
      setOpen(false)
    } catch (error) {
      console.error('Error saving parts:', error)
      toast.error('保存失败，请重试')
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm">
            <Settings className="w-4 h-4 mr-2" />
            配置分床
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>配置分床</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              订单尺码需求: {orderLines.map(line => `${line.size}:${line.amount}件`).join(', ')}
            </div>
            <Button size="sm" onClick={addPart}>
              <Plus className="w-4 h-4 mr-2" />
              添加床位
            </Button>
          </div>
          
          <div className="space-y-6">
            {parts.map((part, partIndex) => (
              <Card key={partIndex}>
                <CardContent className="p-4">
                  {/* 基本信息 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div>
                      <label className="text-sm font-medium">部位名称</label>
                      <Input
                        value={part.part_name || ''}
                        onChange={(e) => updatePart(partIndex, 'part_name', e.target.value)}
                        placeholder="例如：上衣、裤子"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">部位类型</label>
                      <Select
                        value={part.part_type || 'front_body'}
                        onValueChange={(value) => updatePart(partIndex, 'part_type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="front_body">前身</SelectItem>
                          <SelectItem value="back_body">后身</SelectItem>
                          <SelectItem value="sleeve">袖子</SelectItem>
                          <SelectItem value="collar">领子</SelectItem>
                          <SelectItem value="pocket">口袋</SelectItem>
                          <SelectItem value="other">其他</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">颜色</label>
                      <Input
                        value={part.color || ''}
                        onChange={(e) => updatePart(partIndex, 'color', e.target.value)}
                        placeholder="例如：蓝色、红色"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">总数量</label>
                      <Input
                        type="number"
                        value={part.total_quantity || 0}
                        onChange={(e) => updatePart(partIndex, 'total_quantity', parseInt(e.target.value) || 0)}
                        className="bg-muted"
                        readOnly
                      />
                    </div>
                  </div>

                  {/* 各尺码配置 */}
                  <div>
                    <div className="text-sm font-medium mb-3">各尺码数量配置</div>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                      {part.sizeConfigs?.map((sizeConfig, sizeIndex) => (
                        <div key={sizeConfig.size} className="space-y-2">
                          <div className="text-center">
                            <div className="w-12 h-8 bg-primary/10 rounded flex items-center justify-center text-sm font-medium mx-auto">
                              {sizeConfig.size}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              需求: {orderLines.find(line => line.size === sizeConfig.size)?.amount || 0}
                            </div>
                          </div>
                          <Input
                            type="number"
                            min="0"
                            value={sizeConfig.amount}
                            onChange={(e) => updatePartSizeConfig(partIndex, sizeIndex, parseInt(e.target.value) || 0)}
                            className="text-center"
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 备注和操作 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="text-sm font-medium">备注</label>
                      <Input
                        value={part.notes || ''}
                        onChange={(e) => updatePart(partIndex, 'notes', e.target.value)}
                        placeholder="可选"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removePart(partIndex)}
                      >
                        删除床位
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSave}>
              保存配置
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
