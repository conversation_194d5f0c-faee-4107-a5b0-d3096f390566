import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Edit2, Package2 } from 'lucide-react'
import { searchOrderBundlesApiV1OrderBundlesGetOptions } from '@/services/@tanstack/react-query.gen'
import { ConfigureBundlesDialog } from './ConfigureBundlesDialog'
import { NewBundleConfigDialog } from './NewBundleConfigDialog'
import type { OrderPartResponseDto, OrderLineResponseDto } from '@/services/types.gen'

interface OrderBundlesSectionProps {
  orderParts: OrderPartResponseDto[]
  orderLines: OrderLineResponseDto[]
  orderNo: string
  editable: boolean
}

export const OrderBundlesSection: React.FC<OrderBundlesSectionProps> = ({ 
  orderParts,
  orderLines, 
  orderNo,
  editable 
}) => {
  const [selectedPartNo, setSelectedPartNo] = useState<string>('all')
  
  // Fetch bundles using the API
  const { data: bundlesResponse, isLoading, error, refetch } = useQuery(
    searchOrderBundlesApiV1OrderBundlesGetOptions({
      query: {
        order_no: orderNo,
        order_part_no: selectedPartNo === 'all' ? undefined : selectedPartNo,
      }
    })
  )

  const handleBundlesUpdate = async () => {
    await refetch()
  }
  
  const bundles = bundlesResponse?.order_bundles || []

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">加载分扎数据中...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">加载分扎数据失败</p>
      </div>
    )
  }

  if (bundles.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">暂无分扎数据</p>
        {editable && orderParts.length > 0 && (
          <div className="flex flex-wrap justify-center gap-2">
            {orderParts.map(part => (
              <NewBundleConfigDialog
                key={part.id}
                orderPart={part}
                orderLines={orderLines}
                existingBundles={bundles.filter(b => b.order_part_no === part.order_part_no)}
                orderNo={orderNo}
                skcNo={orderNo}
                onSave={handleBundlesUpdate}
                trigger={
                  <Button variant="outline" size="sm">
                    <Package2 className="w-4 h-4 mr-2" />
                    配置 {part.part_name}
                  </Button>
                }
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  // Group bundles by part
  const bundlesByPart = bundles.reduce((acc, bundle) => {
    const partNo = bundle.order_part_no
    if (!acc[partNo]) {
      acc[partNo] = []
    }
    acc[partNo].push(bundle)
    return acc
  }, {} as Record<string, typeof bundles>)

  // Filter by selected part if not 'all'
  const filteredParts = selectedPartNo === 'all' 
    ? orderParts.filter(part => bundlesByPart[part.order_part_no]?.length > 0)
    : orderParts.filter(part => part.order_part_no === selectedPartNo)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Select value={selectedPartNo} onValueChange={setSelectedPartNo}>
          <SelectTrigger className="w-[200px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部床位</SelectItem>
            {orderParts.map((part) => (
              <SelectItem key={part.order_part_no} value={part.order_part_no}>
                {part.part_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {editable && (
          <ConfigureBundlesDialog
            orderLines={orderLines}
            orderParts={selectedPartNo === 'all' ? orderParts : orderParts.filter(p => p.order_part_no === selectedPartNo)}
            orderNo={orderNo}
            skcNo={orderNo} // Using orderNo as skcNo for now
            existingBundles={bundles}
            onSave={handleBundlesUpdate}
            trigger={
              <Button size="sm" variant="outline">
                <Edit2 className="w-4 h-4 mr-2" />
                批量编辑
              </Button>
            }
          />
        )}
      </div>

      {/* Display bundles grouped by part */}
      {filteredParts.map(part => {
        const partBundles = bundlesByPart[part.order_part_no] || []
        
        return (
          <Card key={part.order_part_no}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Package2 className="w-5 h-5" />
                  {part.part_name}
                  <Badge variant="outline">{partBundles.length} 扎</Badge>
                </CardTitle>
                {editable && (
                  <NewBundleConfigDialog
                    orderPart={part}
                    orderLines={orderLines}
                    existingBundles={partBundles}
                    orderNo={orderNo}
                    skcNo={orderNo}
                    onSave={handleBundlesUpdate}
                    trigger={
                      <Button size="sm" variant="outline">
                        <Edit2 className="w-4 h-4 mr-2" />
                        编辑分扎
                      </Button>
                    }
                  />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>扎编号</TableHead>
                    <TableHead>尺码</TableHead>
                    <TableHead>数量</TableHead>
                    <TableHead>颜色</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>完成进度</TableHead>
                    <TableHead>负责人</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {partBundles.map((bundle) => (
                    <TableRow key={bundle.id}>
                      <TableCell className="font-mono">{bundle.order_bundle_no}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{bundle.size}</Badge>
                      </TableCell>
                      <TableCell>{bundle.quantity} 件</TableCell>
                      <TableCell>{bundle.color}</TableCell>
                      <TableCell>
                        <Badge>{bundle.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="text-sm">
                            {bundle.completed_quantity}/{bundle.quantity}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ({bundle.progress_percentage}%)
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {bundle.sewer_name || bundle.cutter_name || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
