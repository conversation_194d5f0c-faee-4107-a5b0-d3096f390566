import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Plus, Minus, Edit } from 'lucide-react'
import type { 
  OrderBundleResponseDto, 
  OrderLineResponseDto
} from '@/services/types.gen'

interface OrderConfigurationSectionProps {
  orderLines: OrderLineResponseDto[]
  orderBundles: OrderBundleResponseDto[]
  onOrderLinesChange?: (orderLines: OrderLineResponseDto[]) => void
}

export const OrderConfigurationSection: React.FC<OrderConfigurationSectionProps> = ({ 
  orderLines, 
  orderBundles,
  onOrderLinesChange
}) => {
  const [isEditDialogO<PERSON>, setIsEditDialogOpen] = useState(false)
  const [editingOrderLines, setEditingOrderLines] = useState<OrderLineResponseDto[]>([])
  
  const handleEditClick = () => {
    setEditingOrderLines([...orderLines])
    setIsEditDialogOpen(true)
  }
  
  const handleSaveChanges = () => {
    if (onOrderLinesChange) {
      onOrderLinesChange(editingOrderLines)
    }
    setIsEditDialogOpen(false)
  }
  
  const handleAddOrderLine = () => {
    const newOrderLine: OrderLineResponseDto = {
      size: '',
      amount: 1
    }
    setEditingOrderLines([...editingOrderLines, newOrderLine])
  }
  
  const handleRemoveOrderLine = (index: number) => {
    const newOrderLines = editingOrderLines.filter((_, i) => i !== index)
    setEditingOrderLines(newOrderLines)
  }
  
  const handleUpdateOrderLine = (index: number, field: 'size' | 'amount', value: string | number) => {
    const newOrderLines = [...editingOrderLines]
    newOrderLines[index] = {
      ...newOrderLines[index],
      [field]: value
    }
    setEditingOrderLines(newOrderLines)
  }
  // 计算每个尺码已配置的数量
  const getSizeConfiguredQuantity = (size: string) => {
    return orderBundles
      .filter(bundle => bundle.size === size)
      .reduce((sum, bundle) => sum + bundle.quantity, 0)
  }

  if (orderLines.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">暂无订单行数据</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 尺码需求总览 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg">尺码需求总览</CardTitle>
            <div className="flex items-center gap-4">
              <div className="flex gap-4 text-sm text-muted-foreground">
                <span>尺码规格: {orderLines.length}</span>
                <span>分扎数量: {orderBundles.length}</span>
              </div>
              <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handleEditClick}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑订单数量
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>编辑订单数量</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {editingOrderLines.map((line, index) => (
                      <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                        <div className="flex-1">
                          <Label htmlFor={`size-${index}`}>尺码</Label>
                          <Input
                            id={`size-${index}`}
                            value={line.size}
                            onChange={(e) => handleUpdateOrderLine(index, 'size', e.target.value)}
                            placeholder="输入尺码"
                          />
                        </div>
                        <div className="flex-1">
                          <Label htmlFor={`amount-${index}`}>数量</Label>
                          <Input
                            id={`amount-${index}`}
                            type="number"
                            value={line.amount}
                            onChange={(e) => handleUpdateOrderLine(index, 'amount', parseInt(e.target.value) || 0)}
                            min="0"
                          />
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveOrderLine(index)}
                          disabled={editingOrderLines.length <= 1}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleAddOrderLine}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      添加尺码
                    </Button>
                  </div>
                  <div className="flex justify-end gap-2 pt-4 border-t">
                    <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                      取消
                    </Button>
                    <Button onClick={handleSaveChanges}>
                      保存更改
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {orderLines.map((line) => (
              <div key={line.size} className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold">{line.size}</div>
                <div className="text-sm text-muted-foreground">
                  需求: {line.amount} 件
                </div>
                <div className="text-sm text-blue-600">
                  已配置: {getSizeConfiguredQuantity(line.size)} 件
                </div>
                {getSizeConfiguredQuantity(line.size) !== line.amount && (
                  <div className="text-xs text-red-600">
                    差额: {line.amount - getSizeConfiguredQuantity(line.size)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
