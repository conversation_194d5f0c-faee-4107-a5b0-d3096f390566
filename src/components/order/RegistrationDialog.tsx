import React, { useCallback } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { createCraftInstanceApiV1CraftInstancesPostMutation } from '@/services/@tanstack/react-query.gen'
import type { OrderCraftRouteResponseDto, CraftInstanceCreateDto, CompletionGranularityDto } from '@/services/types.gen'
import { toast } from 'sonner'
import { RegistrationFormFields } from './RegistrationFormFields'
import { RegistrationTypeSelector } from './RegistrationTypeSelector'
import { OrderPartSelector } from './OrderPartSelector'
import { OrderBundleSelector } from './OrderBundleSelector'

// 登记表单类型
export interface RegistrationFormData {
  registrationType: string // 登记类型: ALL, PART, BUNDLER
  unitIdentifier: string // 根据登记类型不同：整单号、床号、扎号
  selectedParts: string[] // 选中的分床号
  selectedBundles: string[] // 选中的分扎号
  completionPerson: string // 完成人ID
  startTime: string // 开始时间
  completionTime: string // 完成时间
  completedQuantity: number // 完成数量
  qualityLevel: string // 质量等级
  notes: string // 备注
}

interface RegistrationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedRoute?: OrderCraftRouteResponseDto
  orderNo?: string // 订单号，用于获取分床和分扎数据
  onSubmit: () => Promise<void>
  onCancel: () => void
}

export const RegistrationDialog: React.FC<RegistrationDialogProps> = ({
  open,
  onOpenChange,
  selectedRoute,
  orderNo,
  onSubmit,
  onCancel
}) => {
  const queryClient = useQueryClient()

  // React Hook Form 设置
  const form = useForm<RegistrationFormData>({
    defaultValues: {
      registrationType: '',
      unitIdentifier: '',
      selectedParts: [],
      selectedBundles: [],
      completionPerson: '',
      startTime: '',
      completionTime: '',
      completedQuantity: 1,
      qualityLevel: 'A',
      notes: '',
    }
  })

  const { handleSubmit } = form

  // 创建工艺实例的 mutation
  const createCraftInstanceMutation = useMutation({
    mutationFn: createCraftInstanceApiV1CraftInstancesPostMutation().mutationFn,
    onSuccess: () => {
      toast.success('工序登记成功')
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.length > 0 &&
                 typeof queryKey[0] === 'object' &&
                 queryKey[0] !== null &&
                 JSON.stringify(queryKey).includes('CraftInstance') ||
                 JSON.stringify(queryKey).includes('OrderCraft')
        }
      })
      onSubmit()
      onOpenChange(false)
    },
    onError: (error: Error) => {
      toast.error('登记失败: ' + (error.message || '未知错误'))
    }
  })



  // 表单提交处理
  const onFormSubmit = useCallback((data: RegistrationFormData) => {
    if (!selectedRoute || !orderNo) {
      toast.error('缺少必要的工序或订单信息')
      return
    }

    // 验证选择的内容
    if (data.registrationType === 'PART' && data.selectedParts.length === 0) {
      toast.error('请至少选择一个分床')
      return
    }

    if (data.registrationType === 'BUNDLER' && data.selectedBundles.length === 0) {
      toast.error('请至少选择一个分扎')
      return
    }

    // 确定完成粒度
    let completionGranularity: CompletionGranularityDto
    if (data.registrationType === 'ALL') {
      completionGranularity = 'order'
    } else if (data.registrationType === 'PART') {
      completionGranularity = 'bed'
    } else {
      completionGranularity = 'bundle'
    }

    // 根据登记类型创建相应的工艺实例
    const promises: Promise<unknown>[] = []

    if (data.registrationType === 'ALL') {
      // 整单登记 - 创建一个工艺实例
      const craftInstanceData: CraftInstanceCreateDto = {
        order_craft_route_id: selectedRoute.id,
        completion_granularity: completionGranularity,
        order_no: orderNo,
        worker_user_id: parseInt(data.completionPerson),
        completed_quantity: data.completedQuantity,
        quality_level: data.qualityLevel,
        started_at: data.startTime,
        notes: data.notes,
      }
      
      promises.push(
        createCraftInstanceMutation.mutateAsync({
          body: craftInstanceData
        })
      )
    } else if (data.registrationType === 'PART') {
      // 分床登记 - 创建一个工艺实例，包含所有选中的床号
      const craftInstanceData: CraftInstanceCreateDto = {
        order_craft_route_id: selectedRoute.id,
        completion_granularity: completionGranularity,
        order_no: orderNo,
        order_part_nos: data.selectedParts,
        worker_user_id: parseInt(data.completionPerson),
        completed_quantity: data.completedQuantity,
        quality_level: data.qualityLevel,
        started_at: data.startTime,
        notes: data.notes,
      }
      
      promises.push(
        createCraftInstanceMutation.mutateAsync({
          body: craftInstanceData
        })
      )
    } else if (data.registrationType === 'BUNDLER') {
      // 分扎登记 - 创建一个工艺实例，包含所有选中的扎号
      const craftInstanceData: CraftInstanceCreateDto = {
        order_craft_route_id: selectedRoute.id,
        completion_granularity: completionGranularity,
        order_no: orderNo,
        order_bundle_nos: data.selectedBundles,
        worker_user_id: parseInt(data.completionPerson),
        completed_quantity: data.completedQuantity,
        quality_level: data.qualityLevel,
        started_at: data.startTime,
        notes: data.notes,
      }
      
      promises.push(
        createCraftInstanceMutation.mutateAsync({
          body: craftInstanceData
        })
      )
    }

    // 执行所有请求
    Promise.all(promises)
      .then(() => {
        // 成功处理在 mutation 的 onSuccess 中
      })
      .catch(() => {
        // 错误处理在 mutation 的 onError 中
      })
  }, [selectedRoute, orderNo, createCraftInstanceMutation])


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>工序登记</DialogTitle>
          <DialogDescription>
            请填写工序完成信息
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-y-auto py-4 px-2">
          <FormProvider {...form}>
            <form id="registration-form" onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
              {/* 显示选中的工序信息 */}
              {selectedRoute && (
                <div className="p-3 bg-muted/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-sm">选中工序:</span>
                    <span className="text-sm">{selectedRoute.skill_name || selectedRoute.skill_code}</span>
                    <Badge variant="outline" className="text-xs">{selectedRoute.skill_code}</Badge>
                  </div>
                </div>
              )}

              {/* 表单字段组件 */}
              <RegistrationFormFields />

              {/* 登记类型选择器 */}
              <RegistrationTypeSelector
                availableTypes={selectedRoute?.registration_types || []}
                orderNo={orderNo}
                selectedRoute={selectedRoute}
              />

              {/* 分床选择器 */}
              <OrderPartSelector
                orderNo={orderNo}
                selectedRoute={selectedRoute}
              />

              {/* 分扎选择器 */}
              <OrderBundleSelector
                orderNo={orderNo}
                selectedRoute={selectedRoute}
              />

            </form>
          </FormProvider>
        </div>

        <DialogFooter className="flex-shrink-0 border-t bg-background p-6">
          <Button type="button" variant="outline" onClick={onCancel} disabled={createCraftInstanceMutation.isPending}>
            取消
          </Button>
          <Button type="submit" form="registration-form" disabled={createCraftInstanceMutation.isPending}>
            {createCraftInstanceMutation.isPending ? '提交中...' : '提交登记'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
