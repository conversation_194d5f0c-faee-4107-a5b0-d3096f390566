import React, { useState } from 'react'
import { useForm<PERSON>ontext, useFieldArray } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, Trash2, Package2 } from 'lucide-react'
import PartTypeCraftRouteCards from './PartTypeCraftRouteCards'
import CraftsImportDialog from '@/components/CraftsImportDialog'
import type { CraftRouteDetailDto } from '@/services/types.gen'

export interface OrderCraftRoute {
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  assigned_user_id?: number
  price: number
}

export interface OrderCraft {
  craft_code: string
  craft_name: string
  order: number
  is_required: boolean
  estimated_duration_hours?: number
  order_craft_routes: OrderCraftRoute[]
}

export interface OrderPartType {
  part_type: string
  part_name: string
  order_crafts: OrderCraft[]
}

// 预定义的部件类型
const PART_TYPES = [
  { value: 'front', label: '前片' },
  { value: 'back', label: '后片' },
  { value: 'sleeve', label: '袖子' },
  { value: 'collar', label: '领子' },
  { value: 'pocket', label: '口袋' },
  { value: 'cuff', label: '袖口' },
  { value: 'hem', label: '下摆' },
  { value: 'other', label: '其他' }
]

interface OrderPartTypesConfigProps {
  className?: string
}

const OrderPartTypesConfig: React.FC<OrderPartTypesConfigProps> = ({ className }) => {
  const { control } = useFormContext()
  const { fields: partTypeFields, append: appendPartType, remove: removePartType } = useFieldArray({
    control,
    name: "part_types"
  })

  const [selectedPartType, setSelectedPartType] = useState<string>('')

  // 添加部件类型
  const addPartType = () => {
    if (!selectedPartType) return

    const partTypeData = PART_TYPES.find(pt => pt.value === selectedPartType)
    if (!partTypeData) return

    // 检查是否已存在
    const exists = partTypeFields.some((field) => 
      (field as unknown as OrderPartType).part_type === selectedPartType
    )
    
    if (exists) return

    const newPartType: OrderPartType = {
      part_type: selectedPartType,
      part_name: partTypeData.label,
      order_crafts: []
    }

    appendPartType(newPartType)
    setSelectedPartType('')
  }

  // 删除部件类型
  const removePartTypeItem = (partTypeIndex: number) => {
    removePartType(partTypeIndex)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Package2 className="w-5 h-5" />
            部件工艺配置
          </CardTitle>
          <div className="flex items-center gap-3">
            <Select value={selectedPartType} onValueChange={setSelectedPartType}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择部件类型" />
              </SelectTrigger>
              <SelectContent>
                {PART_TYPES.map((partType) => (
                  <SelectItem key={partType.value} value={partType.value}>
                    {partType.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={addPartType} 
              size="sm"
              disabled={!selectedPartType}
            >
              <Plus className="w-4 h-4 mr-2" />
              添加部件
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {partTypeFields.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              暂无部件配置，请选择部件类型并添加
            </p>
          </div>
        ) : (
          <Tabs defaultValue={partTypeFields[0]?.id} className="w-full">
            <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
              {partTypeFields.map((partType) => {
                const partTypeData = partType as unknown as OrderPartType & { id: string }
                return (
                  <TabsTrigger 
                    key={partTypeData.id} 
                    value={partTypeData.id}
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm gap-2"
                  >
                    {partTypeData.part_name}
                    <Badge variant="secondary" className="ml-1">
                      {partTypeData.order_crafts?.length || 0}
                    </Badge>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {partTypeFields.map((partType, partTypeIndex) => {
              const partTypeData = partType as unknown as OrderPartType & { id: string }
              return (
                <TabsContent key={partTypeData.id} value={partTypeData.id} className="space-y-4">
                  {/* 该部件类型的工艺配置 */}
                  <PartTypeCraftsConfig 
                    partTypeIndex={partTypeIndex} 
                    partTypeName={partTypeData.part_name}
                    onRemovePartType={() => removePartTypeItem(partTypeIndex)}
                  />
                </TabsContent>
              )
            })}
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

// 单个部件类型的工艺配置组件
interface PartTypeCraftsConfigProps {
  partTypeIndex: number
  partTypeName: string
  onRemovePartType: () => void
}

const PartTypeCraftsConfig: React.FC<PartTypeCraftsConfigProps> = ({ partTypeIndex, partTypeName, onRemovePartType }) => {
  const { control } = useFormContext()
  const { fields: craftFields, append: appendCraft, remove: removeCraft } = useFieldArray({
    control,
    name: `part_types.${partTypeIndex}.order_crafts`
  })

  const [showImportDialog, setShowImportDialog] = useState(false)

  const addOrderCraft = () => {
    setShowImportDialog(true)
  }

  const handleImportCrafts = (importedData: { code: string; name: string; routes: CraftRouteDetailDto[] }[]) => {
    importedData.forEach(({ code, name, routes }) => {
      console.log(`Importing craft: ${code} - ${name}`, routes)
      
      // 检查是否已存在
      const exists = craftFields.some((field) => (field as unknown as OrderCraft).craft_code === code)
      if (!exists) {
        const newCraft: OrderCraft = {
          craft_code: code,
          craft_name: name,
          order: craftFields.length + 1,
          is_required: true,
          estimated_duration_hours: 0,
          order_craft_routes: routes.map((route): OrderCraftRoute => ({
            code: route.code,
            name: route.name || route.code,
            skill_code: route.skill_code,
            skill_name: route.skill_name || route.skill_code,
            order: route.order,
            measurement_types: route.measurement_types || [],
            registration_types: route.registration_types || [],
            assigned_user_id: undefined,
            price: 0, // 默认价格为0，后续可编辑
          }))
        }
        appendCraft(newCraft)
      }
    })
    setShowImportDialog(false)
  }

  const removeCraftItem = (craftIndex: number) => {
    removeCraft(craftIndex)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">{partTypeName} - 工艺配置</h3>
        <div className="flex items-center gap-2">
          <Button onClick={addOrderCraft} size="sm" variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            添加工艺
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onRemovePartType}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            删除部件
          </Button>
        </div>
      </div>

      {craftFields.length === 0 ? (
        <div className="text-center py-6 border border-dashed rounded-lg">
          <p className="text-muted-foreground">
            暂无工艺配置，点击"添加工艺"开始配置
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-4">
          {craftFields.map((craft, craftIndex) => {
            const craftData = craft as unknown as OrderCraft & { id: string }
            return (
              <Card key={craftData.id} className="border-l-4 border-l-blue-500">
                <CardHeader className="px-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-sm">{craftData.craft_name}</h5>
                      <p className="text-xs text-muted-foreground">
                        {craftData.craft_code}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={craftData.is_required ? "default" : "secondary"} className="text-xs">
                        {craftData.is_required ? "必需" : "可选"}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCraftItem(craftIndex)}
                        className="h-6 w-6 p-0"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="px-3 pb-3 pt-0">
                  <PartTypeCraftRouteCards 
                    partTypeIndex={partTypeIndex}
                    craftIndex={craftIndex}
                  />
                </CardContent>
              </Card>
            )
          }          )}
        </div>
      )}

      <CraftsImportDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
        onImport={handleImportCrafts}
      />
    </div>
  )
}

export default OrderPartTypesConfig
