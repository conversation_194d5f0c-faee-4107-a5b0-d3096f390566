import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useState, useRef, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/simple-sidebar"
import { Home, Users, Building2, LogOut, ChevronDown, Factory, Award, Route, UserCog, Plus, Receipt, FileText, ClipboardList, User, Settings, KeyRound, FileCheck } from "lucide-react"
import ModifyPasswordDialog from './ModifyPasswordDialog'
import EditProfileDialog from './EditProfileDialog'
import { getMyAvailableFactoriesApiV1SessionMyFactoriesGetOptions, switchFactoryContextApiV1SessionSwitchFactoryPostMutation } from '@/services/@tanstack/react-query.gen'
import type { AvailableFactoryDto } from '@/services/types.gen'
import { useAuth } from '@/hooks/useAuth'

function SidebarFooterContent({ 
  onLogout,
  onEditProfile,
  onChangePassword 
}: {
  onLogout: () => void
  onEditProfile: () => void
  onChangePassword: () => void
}) {
  const { open } = useSidebar()
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const { user, isLoading } = useAuth()

  // Handle click outside to close user menu
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleEditProfile = () => {
    setIsUserMenuOpen(false)
    onEditProfile()
  }

  const handleChangePassword = () => {
    setIsUserMenuOpen(false)
    onChangePassword()
  }

  const handleLogout = () => {
    setIsUserMenuOpen(false)
    onLogout()
  }

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-2 py-2">
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <User className="size-4" />
        </div>
        {open && (
          <div className="flex flex-col gap-0.5 leading-none">
            <span className="font-semibold text-sm">加载中...</span>
          </div>
        )}
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center gap-2 px-2 py-2">
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <User className="size-4" />
        </div>
        {open && (
          <div className="flex flex-col gap-0.5 leading-none">
            <span className="font-semibold text-sm">未登录</span>
          </div>
        )}
      </div>
    )
  }
  
  return (
    <div className="relative" ref={userMenuRef}>
      <div 
        className="flex items-center gap-2 px-2 py-2 cursor-pointer hover:bg-accent rounded-md"
        onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
      >
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <User className="size-4" />
        </div>
        {open && (
          <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col gap-0.5 leading-none">
              <span className="font-semibold text-sm">{user.full_name || user.username}</span>
              <span className="text-xs text-muted-foreground">{user.role?.name}</span>
            </div>
            <ChevronDown className="size-4 text-muted-foreground" />
          </div>
        )}
      </div>
      
      {/* User Menu Dropdown */}
      {isUserMenuOpen && open && (
        <div className="absolute bottom-full left-0 right-0 mb-1 bg-background border rounded-md shadow-lg z-50">
          <div className="px-3 py-2 border-b">
            <div className="flex flex-col">
              <span className="text-sm font-medium">{user.full_name || user.username}</span>
              <span className="text-xs text-muted-foreground">{user.email}</span>
            </div>
          </div>
          
          <div className="py-1">
            <button
              className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-accent text-left"
              onClick={handleEditProfile}
            >
              <Settings className="size-4" />
              <span>修改信息</span>
            </button>
            
            <button
              className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-accent text-left"
              onClick={handleChangePassword}
            >
              <KeyRound className="size-4" />
              <span>修改密码</span>
            </button>
            
            <div className="border-t my-1"></div>
            
            <button
              className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-accent text-left text-red-600"
              onClick={handleLogout}
            >
              <LogOut className="size-4" />
              <span>登出</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

function SidebarHeaderContent() {
  const { open } = useSidebar()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const queryClient = useQueryClient()
  const { user, refreshUser } = useAuth()
  
  // Fetch available factories
  const { data: factoriesData, isLoading, error } = useQuery(
    getMyAvailableFactoriesApiV1SessionMyFactoriesGetOptions()
  )
  
  // Switch factory mutation
  const switchFactoryMutation = useMutation(switchFactoryContextApiV1SessionSwitchFactoryPostMutation())

  const factories = factoriesData?.factories || []

  const handleFactoryChange = async (factory: AvailableFactoryDto) => {
    try {
      setIsDropdownOpen(false)
      await switchFactoryMutation.mutateAsync({
        body: { factory_id: factory.factory_id }
      })
      // Invalidate and refetch factory-related queries
      refreshUser()
      queryClient.invalidateQueries()
    } catch (error) {
      console.error('Failed to switch factory:', error)
      // TODO: Show error toast
    }
  }

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])
  
  return (
    <div className="relative" ref={dropdownRef}>
      <div 
        className="flex items-center gap-2 px-2 py-2 cursor-pointer hover:bg-accent rounded-md"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <Factory className="size-4" />
        </div>
        {open && (
          <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col gap-0.5 leading-none">
              <span className="font-semibold">
                {switchFactoryMutation.isPending ? "切换中..." : user?.current_factory_name}
              </span>
              <span className="text-xs text-muted-foreground">点击切换工厂</span>
            </div>
            <ChevronDown className="size-4 text-muted-foreground" />
          </div>
        )}
      </div>
      
      {/* Dropdown Menu */}
      {isDropdownOpen && open && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50">
          {isLoading ? (
            <div className="px-3 py-2 text-sm text-muted-foreground">
              加载中...
            </div>
          ) : error ? (
            <div className="px-3 py-2 text-sm text-red-600">
              加载工厂列表失败
            </div>
          ) : factories.length === 0 ? (
            <div className="px-3 py-2 text-sm text-muted-foreground">
              暂无可用工厂
            </div>
          ) : (
            factories.map((factory) => (
              user?.current_factory_id === factory.factory_id ? (
                <div
                key={factory.factory_id}
                className="flex items-center gap-2 px-3 py-2 hover:bg-accent cursor-pointer bg-primary text-primary-foreground"
                onClick={() => handleFactoryChange(factory)}
              >
                <Factory className="size-4" />
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{factory.factory_name}</span>
                  <span className="text-xs">{factory.factory_code}</span>
                </div>
              </div>
            ) : (
              <div
                key={factory.factory_id}
                className="flex items-center gap-2 px-3 py-2 hover:bg-accent cursor-pointer"
                onClick={() => handleFactoryChange(factory)}
              >
                <Factory className="size-4 text-muted-foreground" />
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{factory.factory_name}</span>
                  <span className="text-xs text-muted-foreground">{factory.factory_code}</span>
                </div>
              </div>
              )
            ))
          )}
        </div>
      )}
    </div>
  )
}

interface AppLayoutProps {
  children: React.ReactNode
}

export default function AppLayout({ children }: AppLayoutProps) {
  const navigate = useNavigate()
  const location = useLocation()
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false)
  const [isPasswordLoading, setIsPasswordLoading] = useState(false)
  const [isProfileLoading, setIsProfileLoading] = useState(false)
  const { user, logout: authLogout } = useAuth()

  const handleLogout = () => {
    authLogout()
    navigate('/login')
  }

  const handleEditProfile = () => {
    setIsProfileDialogOpen(true)
  }

  const handleChangePassword = () => {
    setIsPasswordDialogOpen(true)
  }

  const handlePasswordSubmit = async (data: { oldPassword: string; newPassword: string }) => {
    setIsPasswordLoading(true)
    try {
      // TODO: Replace with actual API call
      console.log('修改密码:', data)
      // await updatePassword(data)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setIsPasswordDialogOpen(false)
      // TODO: Show success toast
      console.log('密码修改成功')
    } catch (error) {
      // TODO: Show error toast
      console.error('密码修改失败:', error)
    } finally {
      setIsPasswordLoading(false)
    }
  }

  const handleProfileSubmit = async (data: { name: string; email?: string; avatar?: string; bio?: string }) => {
    setIsProfileLoading(true)
    try {
      // TODO: Replace with actual API call
      console.log('更新用户信息:', data)
      // await updateUserProfile(data)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setIsProfileDialogOpen(false)
      // TODO: Show success toast and update user context
      console.log('用户信息更新成功')
    } catch (error) {
      // TODO: Show error toast
      console.error('用户信息更新失败:', error)
    } finally {
      setIsProfileLoading(false)
    }
  }

  const menuGroups = [
    {
      label: "总览",
      items: [
        {
          title: "仪表板",
          url: "/dashboard",
          icon: Home,
        },
      ]
    },
    {
      label: "订单管理",
      items: [
        {
          title: "新建订单",
          url: "/orders/new",
          icon: Plus,
        },
        {
          title: '订单列表',
          url: '/orders',
          icon: ClipboardList,
        },
        {
          title: "账单管理",
          url: "/bills",
          icon: Receipt,
        },
        {
          title: "价格审核",
          url: "/price-audit",
          icon: FileCheck,
        },
      ]
    },
    {
      label: "组织管理",
      items: [
        {
          title: "部门管理",
          url: "/departments",
          icon: Building2,
        },
        {
          title: "员工管理",
          url: "/employees",
          icon: Users,
        },
        {
          title: "角色管理",
          url: "/roles",
          icon: UserCog,
        },
      ]
    },
    {
      label: "工艺管理",
      items: [
        {
          title: "技能管理",
          url: "/skills",
          icon: Award,
        },
        {
          title: "工艺路线",
          url: "/crafts",
          icon: Route,
        },
        {
          title: "工艺模板",
          url: "/craft-templates",
          icon: FileText,
        },
      ]
    }
  ]

  return (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <SidebarHeaderContent />
        </SidebarHeader>
        
        <SidebarContent>
          {menuGroups.map((group) => (
            <SidebarGroup key={group.label}>
              <SidebarGroupLabel>{group.label}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {group.items.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <Link to={item.url}>
                        <SidebarMenuButton 
                          isActive={location.pathname === item.url}
                        >
                          <item.icon className="w-4 h-4" />
                          <span>{item.title}</span>
                        </SidebarMenuButton>
                      </Link>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </SidebarContent>
        
        <SidebarFooter>
          <SidebarFooterContent 
            onLogout={handleLogout}
            onEditProfile={handleEditProfile}
            onChangePassword={handleChangePassword}
          />
        </SidebarFooter>
      </Sidebar>
      
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="flex flex-1 items-center gap-2 px-2">
            <h1 className="text-lg font-semibold">当前工厂: {user?.current_factory_name}</h1>
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4">
          {children}
        </main>
      </SidebarInset>

      {/* Dialogs */}
      <ModifyPasswordDialog
        open={isPasswordDialogOpen}
        onOpenChange={setIsPasswordDialogOpen}
        onSubmit={handlePasswordSubmit}
        isLoading={isPasswordLoading}
      />

      <EditProfileDialog
        open={isProfileDialogOpen}
        onOpenChange={setIsProfileDialogOpen}
        onSubmit={handleProfileSubmit}
        isLoading={isProfileLoading}
      />
    </SidebarProvider>
  )
}
