import { useState } from 'react'
import { Save, X, Edit2, Trash2, Plus, <PERSON>U<PERSON>, ArrowDown } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import type { CraftRouteDetailDto, CraftRouteCreateDto, CraftRouteUpdateDto, SkillResponseDto } from '@/services/types.gen'

// 计价方式
export const MEASUREMENT_TYPES = [
  { value: 'ALL', label: '整单' },
  { value: 'COUNT', label: '件数' },
  { value: 'TIME', label: '工时' },
]

//登记方式
export const REGISTRATION_TYPES = [
  { value: 'ALL', label: '整单录入'},
  { value: 'PART', label: '分床录入'},
  { value: 'BUNDLER', label: '分扎录入'}
]

interface EditableCraftRouteTableProps {
  routes: CraftRouteDetailDto[]
  skills: SkillResponseDto[]
  craftCode: string
  onRouteUpdate: (routeId: number, data: CraftRouteUpdateDto) => Promise<void>
  onRouteCreate: (data: CraftRouteCreateDto) => Promise<void>
  onRouteDelete: (routeId: number) => Promise<void>
  onRouteReorder: (routeId: number, direction: 'up' | 'down') => Promise<void>
  isLoading?: boolean
}

interface EditingRoute {
  id?: number
  code: string
  name: string
  skill_code: string
  measurement_types: string[]
  registration_types: string[]
  is_required: boolean
  notes: string
}

export default function EditableCraftRouteTable({
  routes,
  skills,
  craftCode,
  onRouteUpdate,
  onRouteCreate,
  onRouteDelete,
  onRouteReorder,
  isLoading = false
}: EditableCraftRouteTableProps) {
  const [editingRowId, setEditingRowId] = useState<number | 'new' | null>(null)
  const [editingRoute, setEditingRoute] = useState<EditingRoute | null>(null)
  const [reorderMode, setReorderMode] = useState(false)

  // 获取技能名称
  const getSkillName = (skillCode: string) => {
    const skill = skills.find(s => s.code === skillCode)
    return skill ? skill.name : skillCode
  }

  // 开始编辑现有路线
  const startEdit = (route: CraftRouteDetailDto) => {
    setEditingRowId(route.id)
    setEditingRoute({
      id: route.id,
      code: `${route.id}`, // 使用路线ID作为默认编码，您可以根据需要修改
      name: route.skill_name || `路线${route.order}`, // 使用技能名称或默认名称
      skill_code: route.skill_code,
      measurement_types: [...route.measurement_types],
      registration_types: [...route.registration_types],
      is_required: route.is_required,
      notes: route.notes || ''
    })
  }

  // 开始添加新路线
  const startAddNew = () => {
    setEditingRowId('new')
    setEditingRoute({
      code: '',
      name: '',
      skill_code: '',
      measurement_types: [],
      registration_types: [],
      is_required: true,
      notes: ''
    })
  }

  // 取消编辑
  const cancelEdit = () => {
    setEditingRowId(null)
    setEditingRoute(null)
  }

  // 保存编辑
  const saveEdit = async () => {
    if (!editingRoute) return

    try {
      if (editingRowId === 'new') {
        // 创建新路线
        await onRouteCreate({
          craft_code: craftCode,
          code: editingRoute.code,
          name: editingRoute.name,
          skill_code: editingRoute.skill_code,
          measurement_types: editingRoute.measurement_types,
          registration_types: editingRoute.registration_types,
          is_required: editingRoute.is_required,
          notes: editingRoute.notes || undefined
        })
      } else if (typeof editingRowId === 'number') {
        // 更新现有路线
        // 注意：根据API限制，skill_code 可能无法更新，如果需要修改技能，请删除后重新创建
        await onRouteUpdate(editingRowId, {
          measurement_types: editingRoute.measurement_types,
          registration_types: editingRoute.registration_types,
          is_required: editingRoute.is_required,
          notes: editingRoute.notes || undefined
        })
      }
      cancelEdit()
    } catch {
      // 错误处理在父组件中完成
    }
  }

  // 更新编辑中的路线数据
  const updateEditingRoute = (updates: Partial<EditingRoute>) => {
    if (editingRoute) {
      setEditingRoute({ ...editingRoute, ...updates })
    }
  }

  // 切换多选项（测量类型或注册类型）
  const toggleArrayValue = (array: string[], value: string) => {
    return array.includes(value)
      ? array.filter(item => item !== value)
      : [...array, value]
  }

  // 渲染编辑模式的单元格
  const renderEditCell = (field: keyof EditingRoute, route: EditingRoute) => {
    switch (field) {
      case 'code':
        return (
          <Input
            value={route.code}
            onChange={(e) => updateEditingRoute({ code: e.target.value })}
            placeholder="路线编码"
            className="min-w-[120px]"
          />
        )

      case 'name':
        return (
          <Input
            value={route.name}
            onChange={(e) => updateEditingRoute({ name: e.target.value })}
            placeholder="路线名称"
            className="min-w-[150px]"
          />
        )

      case 'skill_code':
        // 技能代码现在在所有模式下都可以编辑
        return (
          <Select
            value={route.skill_code}
            onValueChange={(value) => updateEditingRoute({ skill_code: value })}
          >
            <SelectTrigger className="min-w-[150px]">
              <SelectValue placeholder="选择技能" />
            </SelectTrigger>
            <SelectContent>
              {skills.map((skill) => (
                <SelectItem key={skill.code} value={skill.code}>
                  {skill.name} ({skill.code})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'measurement_types':
        return (
          <div className="space-y-2 min-w-[200px]">
            {MEASUREMENT_TYPES.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`measurement-${type.value}`}
                  checked={route.measurement_types.includes(type.value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateEditingRoute({
                        measurement_types: toggleArrayValue(route.measurement_types, type.value)
                      })
                    } else {
                      updateEditingRoute({
                        measurement_types: route.measurement_types.filter(t => t !== type.value)
                      })
                    }
                  }}
                />
                <label htmlFor={`measurement-${type.value}`} className="text-sm">
                  {type.label}
                </label>
              </div>
            ))}
          </div>
        )

      case 'registration_types':
        return (
          <div className="space-y-2 min-w-[200px]">
            {REGISTRATION_TYPES.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`registration-${type.value}`}
                  checked={route.registration_types.includes(type.value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateEditingRoute({
                        registration_types: toggleArrayValue(route.registration_types, type.value)
                      })
                    } else {
                      updateEditingRoute({
                        registration_types: route.registration_types.filter(t => t !== type.value)
                      })
                    }
                  }}
                />
                <label htmlFor={`registration-${type.value}`} className="text-sm">
                  {type.label}
                </label>
              </div>
            ))}
          </div>
        )

      case 'is_required':
        return (
          <Checkbox
            checked={route.is_required}
            onCheckedChange={(checked) => updateEditingRoute({ is_required: !!checked })}
          />
        )

      case 'notes':
        return (
          <Textarea
            value={route.notes}
            onChange={(e) => updateEditingRoute({ notes: e.target.value })}
            placeholder="备注信息..."
            className="min-w-[200px]"
            rows={2}
          />
        )

      default:
        return null
    }
  }

  // 渲染显示模式的单元格
  const renderDisplayCell = (field: string, route: CraftRouteDetailDto) => {
    switch (field) {
      case 'code':
        return (
          <Badge variant="outline" className="text-xs">
            {route.id} {/* 使用路线ID作为编码显示 */}
          </Badge>
        )

      case 'name':
        return (
          <span className="font-medium">
            {route.skill_name || `路线${route.order}`} {/* 使用技能名称或默认名称 */}
          </span>
        )

      case 'skill':
        return (
          <div>
            <div className="font-medium">{getSkillName(route.skill_code)}</div>
            <div className="text-sm text-gray-500">{route.skill_code}</div>
          </div>
        )

      case 'measurement_types':
        return (
          <div className="flex flex-wrap gap-1">
            {route.measurement_types.length > 0 ? (
              route.measurement_types.map((type) => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {MEASUREMENT_TYPES.find(m => m.value === type)?.label || type}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-gray-400">无</span>
            )}
          </div>
        )

      case 'registration_types':
        return (
          <div className="flex flex-wrap gap-1">
            {route.registration_types.length > 0 ? (
              route.registration_types.map((type) => (
                <Badge key={type} variant="outline" className="text-xs">
                  {REGISTRATION_TYPES.find(r => r.value === type)?.label || type}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-gray-400">无</span>
            )}
          </div>
        )

      case 'is_required':
        return route.is_required ? (
          <Badge variant="destructive" className="text-xs">必需</Badge>
        ) : (
          <Badge variant="secondary" className="text-xs">可选</Badge>
        )

      case 'notes':
        return route.notes ? (
          <div className="text-sm text-gray-600 max-w-xs truncate" title={route.notes}>
            {route.notes}
          </div>
        ) : (
          <span className="text-sm text-gray-400">无</span>
        )

      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">加载路线数据...</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">工艺路线</h3>
        <div className="flex space-x-2">
          <Button 
            onClick={startAddNew}
            size="sm"
            className="bg-green-600 hover:bg-green-700"
            disabled={editingRowId !== null}
          >
            <Plus className="h-4 w-4 mr-2" />
            添加路线
          </Button>
          {routes.length > 1 && (
            <Button 
              onClick={() => setReorderMode(!reorderMode)}
              size="sm"
              variant={reorderMode ? "default" : "outline"}
              disabled={editingRowId !== null}
            >
              {reorderMode ? '完成排序' : '调整顺序'}
            </Button>
          )}
        </div>
      </div>

      {/* 表格 */}
      {routes.length === 0 && editingRowId !== 'new' ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Plus className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无工艺路线</h3>
          <p className="text-gray-600 mb-4">为此工艺添加第一个工艺路线步骤</p>
          <Button onClick={startAddNew} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            添加路线
          </Button>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">步骤</TableHead>
              <TableHead className="w-32">路线编码</TableHead>
              <TableHead className="w-32">路线名称</TableHead>
              <TableHead>技能</TableHead>
              <TableHead>测量类型</TableHead>
              <TableHead>录入方式</TableHead>
              <TableHead className="w-20">是否必需</TableHead>
              <TableHead>备注</TableHead>
              <TableHead className="w-32">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {routes.map((route, index) => (
              <TableRow key={route.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="px-2 py-1">
                      {route.order}
                    </Badge>
                    {reorderMode && (
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onRouteReorder(route.id, 'up')}
                          disabled={index === 0}
                          className="h-6 w-6 p-0"
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onRouteReorder(route.id, 'down')}
                          disabled={index === routes.length - 1}
                          className="h-6 w-6 p-0"
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('code', editingRoute) : 
                    renderDisplayCell('code', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('name', editingRoute) : 
                    renderDisplayCell('name', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('skill_code', editingRoute) : 
                    renderDisplayCell('skill', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('measurement_types', editingRoute) : 
                    renderDisplayCell('measurement_types', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('registration_types', editingRoute) : 
                    renderDisplayCell('registration_types', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('is_required', editingRoute) : 
                    renderDisplayCell('is_required', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id && editingRoute ? 
                    renderEditCell('notes', editingRoute) : 
                    renderDisplayCell('notes', route)
                  }
                </TableCell>
                <TableCell>
                  {editingRowId === route.id ? (
                    <div className="flex space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={saveEdit}
                        className="h-8 w-8 p-0 text-green-600"
                      >
                        <Save className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={cancelEdit}
                        className="h-8 w-8 p-0 text-gray-600"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => startEdit(route)}
                        disabled={editingRowId !== null || reorderMode}
                        className="h-8 w-8 p-0"
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="text-red-600 h-8 w-8 p-0"
                            disabled={editingRowId !== null || reorderMode}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>确认删除</AlertDialogTitle>
                            <AlertDialogDescription>
                              确定要删除这个工艺路线步骤吗？此操作无法撤销。
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction 
                              onClick={() => onRouteDelete(route.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              删除
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
            
            {/* 新增行 */}
            {editingRowId === 'new' && editingRoute && (
              <TableRow className="bg-green-50">
                <TableCell>
                  <Badge variant="outline" className="px-2 py-1">
                    {routes.length + 1}
                  </Badge>
                </TableCell>
                <TableCell>{renderEditCell('code', editingRoute)}</TableCell>
                <TableCell>{renderEditCell('name', editingRoute)}</TableCell>
                <TableCell>{renderEditCell('skill_code', editingRoute)}</TableCell>
                <TableCell>{renderEditCell('measurement_types', editingRoute)}</TableCell>
                <TableCell>{renderEditCell('registration_types', editingRoute)}</TableCell>
                <TableCell>{renderEditCell('is_required', editingRoute)}</TableCell>
                <TableCell>{renderEditCell('notes', editingRoute)}</TableCell>
                <TableCell>
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={saveEdit}
                      className="h-8 w-8 p-0 text-green-600"
                    >
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={cancelEdit}
                      className="h-8 w-8 p-0 text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}
    </div>
  )
}
