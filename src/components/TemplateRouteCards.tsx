import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Edit2, Trash2, Plus } from 'lucide-react'
import { MEASUREMENT_TYPES, REGISTRATION_TYPES } from '@/components/EditableCraftRouteTable'

interface CraftRoute {
  id: string
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  price: number
}

interface TemplateRouteCardProps {
  route: CraftRoute
  onEdit?: (route: CraftRoute) => void
  onRemove?: (routeId: string) => void
}

interface TemplateRouteCardsProps {
  routes: CraftRoute[]
  onAddRoute?: () => void
  onEditRoute?: (route: CraftRoute) => void
  onRemoveRoute?: (routeId: string) => void
}

const TemplateRouteCard: React.FC<TemplateRouteCardProps> = ({
  route,
  onEdit,
  onRemove
}) => {
  const handleEdit = () => {
    if (onEdit) {
      onEdit(route)
    }
  }

  const handleRemove = () => {
    if (onRemove) {
      onRemove(route.id)
    }
  }

  return (
    <Card className="min-w-[240px] max-w-[240px] flex-shrink-0 shadow-sm border-l-4 border-l-blue-500">
      <CardContent className="p-3 space-y-2">
        {/* First Row: Sequence, Name, Edit/Delete */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-5 flex-shrink-0">
              #{route.order}
            </Badge>
            <span className="text-sm font-medium truncate">
              {route.name || route.code || '未命名'}
            </span>
          </div>
          <div className="flex gap-1 flex-shrink-0">
            {onEdit && (
              <Button size="sm" variant="ghost" onClick={handleEdit} className="h-6 w-6 p-0">
                <Edit2 className="w-3 h-3" />
              </Button>
            )}
            {onRemove && (
              <Button size="sm" variant="ghost" onClick={handleRemove} className="h-6 w-6 p-0">
                <Trash2 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
        
        {/* Second Row: Badges for Measurement and Registration Types */}
        <div className="flex flex-wrap gap-1">
          {route.measurement_types && route.measurement_types.length > 0 && (
            <>
              {route.measurement_types.slice(0, 2).map((type: string) => {
                const typeInfo = MEASUREMENT_TYPES.find(t => t.value === type)
                return (
                  <Badge key={type} variant="secondary" className="text-xs px-1.5 py-0 h-4">
                    {typeInfo?.label || type}
                  </Badge>
                )
              })}
              {route.measurement_types.length > 2 && (
                <Badge variant="outline" className="text-xs px-1.5 py-0 h-4">
                  +{route.measurement_types.length - 2}
                </Badge>
              )}
            </>
          )}
          {route.registration_types && route.registration_types.length > 0 && (
            <>
              {route.registration_types.slice(0, 2).map((type: string) => {
                const typeInfo = REGISTRATION_TYPES.find(t => t.value === type)
                return (
                  <Badge key={type} variant="default" className="text-xs px-1.5 py-0 h-4">
                    {typeInfo?.label || type}
                  </Badge>
                )
              })}
              {route.registration_types.length > 2 && (
                <Badge variant="outline" className="text-xs px-1.5 py-0 h-4">
                  +{route.registration_types.length - 2}
                </Badge>
              )}
            </>
          )}
        </div>
        
        {/* Third Row: Skill Name (left) and Price (right) */}
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground truncate flex-1 min-w-0">
            {route.skill_name || route.skill_code || '未设置技能'}
          </span>
          <span className="font-medium text-green-600 flex-shrink-0 ml-2">
            ¥{route.price?.toFixed(2) || '0.00'}
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

const TemplateRouteCards: React.FC<TemplateRouteCardsProps> = ({
  routes,
  onAddRoute,
  onEditRoute,
  onRemoveRoute
}) => {
  if (routes.length === 0) {
    return (
      <div className="text-center py-6 border border-dashed rounded-lg">
        <p className="text-sm text-muted-foreground mb-4">
          该工艺暂无路线配置
        </p>
        {onAddRoute && (
          <Button onClick={onAddRoute} size="sm" variant="outline">
            添加第一个工艺路线
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className="flex gap-2 overflow-x-auto pb-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      {/* Existing Route Cards */}
      {routes.map((route) => (
        <TemplateRouteCard
          key={route.id}
          route={route}
          onEdit={onEditRoute}
          onRemove={onRemoveRoute}
        />
      ))}
      
      {/* Add New Route Card */}
      {onAddRoute && (
        <Card className="min-w-[240px] max-w-[240px] flex-shrink-0 border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors cursor-pointer"
              onClick={onAddRoute}>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="w-10 h-10 mx-auto mb-2 rounded-full bg-gray-100 flex items-center justify-center">
                <Plus className="w-5 h-5 text-gray-400" />
              </div>
              <p className="text-xs text-muted-foreground">添加工艺路线</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default TemplateRouteCards
export { TemplateRouteCard }
