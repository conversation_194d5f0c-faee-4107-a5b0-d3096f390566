import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import type { CraftResponseDto, CraftCreateDto, CraftUpdateDto } from '@/services/types.gen'

// Zod 验证模式
const craftSchema = z.object({
  code: z.string().min(1, '工艺编码不能为空').max(50, '工艺编码不能超过50个字符'),
  name: z.string().min(1, '工艺名称不能为空').max(100, '工艺名称不能超过100个字符'),
  priority: z.number().min(1, '优先级必须大于0').max(10, '优先级不能超过10'),
  enabled: z.boolean(),
  description: z.string().optional()
})

type CraftFormData = z.infer<typeof craftSchema>

interface CraftDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingCraft: CraftResponseDto | null
  onSubmit: (data: CraftCreateDto | { id: number; data: CraftUpdateDto }) => void
  isLoading?: boolean
}

export default function CraftDialog({ 
  open, 
  onOpenChange, 
  editingCraft, 
  onSubmit, 
  isLoading = false 
}: CraftDialogProps) {
  const form = useForm<CraftFormData>({
    resolver: zodResolver(craftSchema),
    defaultValues: {
      code: '',
      name: '',
      priority: 1,
      enabled: true,
      description: ''
    }
  })

  // 当对话框打开时重置表单
  React.useEffect(() => {
    if (open) {
      if (editingCraft) {
        form.reset({
          code: editingCraft.code,
          name: editingCraft.name,
          priority: editingCraft.priority,
          enabled: editingCraft.enabled,
          description: editingCraft.description || ''
        })
      } else {
        form.reset({
          code: '',
          name: '',
          priority: 1,
          enabled: true,
          description: ''
        })
      }
    }
  }, [open, editingCraft, form])

  const onFormSubmit = (data: CraftFormData) => {
    if (editingCraft) {
      // 编辑模式：只发送可更新的字段
      const updateData: CraftUpdateDto = {
        name: data.name,
        priority: data.priority,
        enabled: data.enabled,
        description: data.description
      }
      onSubmit({ id: editingCraft.id, data: updateData })
    } else {
      // 创建模式：发送完整数据
      const createData: CraftCreateDto = {
        code: data.code,
        name: data.name,
        priority: data.priority,
        enabled: data.enabled,
        description: data.description
      }
      onSubmit(createData)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{editingCraft ? '编辑工艺' : '新建工艺'}</DialogTitle>
          <DialogDescription>
            {editingCraft ? '修改工艺信息' : '创建新的工艺配置'}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>工艺编码 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入工艺编码"
                      disabled={!!editingCraft}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>工艺名称 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入工艺名称"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>优先级</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <p className="text-xs text-gray-500 mt-1">优先级范围：1-10（数值越高优先级越高）</p>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel>启用工艺</FormLabel>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入工艺描述"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button 
                type="submit"
                disabled={!form.formState.isValid || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {editingCraft ? '更新中...' : '创建中...'}
                  </div>
                ) : (
                  editingCraft ? '更新' : '创建'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
