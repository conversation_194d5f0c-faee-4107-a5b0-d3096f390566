import React, { useMemo } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useQuery } from '@tanstack/react-query'
import { getAvailableUsersApiV1UserManagementAvailableUsersGetOptions } from '@/services/api'

interface UserSelectorProps {
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
}

export const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onValueChange,
  placeholder = "选择用户",
  disabled = false
}) => {
      // 获取用户列表
  // Fetch factory users
  const { data: usersData } = useQuery({
    ...getAvailableUsersApiV1UserManagementAvailableUsersGetOptions(),
    staleTime: 30000,
  })

  const users = useMemo(() => usersData?.users?.map((user) => ({
    id: user.id,
    name: user.full_name || user.username,
    code: user.username
  })) || [], [usersData])


  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {users.map((user) => (
          <SelectItem key={user.id} value={user.id.toString()}>
            {user.name} ({user.code})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

// Alias for EmployeeSelector
export const EmployeeSelector = UserSelector
