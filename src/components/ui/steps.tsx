import React from 'react'
import { cn } from '@/lib/utils'
import { Check, Circle, Clock } from 'lucide-react'

interface StepsProps {
  className?: string
  children: React.ReactNode
}

interface StepProps {
  className?: string
  status?: 'pending' | 'current' | 'completed'
  children: React.ReactNode
}

interface StepIndicatorProps {
  status?: 'pending' | 'current' | 'completed'
  className?: string
}

interface StepContentProps {
  className?: string
  children: React.ReactNode
}

interface StepTitleProps {
  className?: string
  children: React.ReactNode
}

interface StepDescriptionProps {
  className?: string
  children: React.ReactNode
}

const Steps = React.forwardRef<HTMLOListElement, StepsProps>(
  ({ className, children, ...props }, ref) => (
    <ol
      ref={ref}
      className={cn('space-y-6', className)}
      {...props}
    >
      {children}
    </ol>
  )
)
Steps.displayName = 'Steps'

const Step = React.forwardRef<HTMLLIElement, StepProps>(
  ({ className, status = 'pending', children, ...props }, ref) => (
    <li
      ref={ref}
      className={cn('relative flex gap-4', className)}
      {...props}
    >
      {children}
    </li>
  )
)
Step.displayName = 'Step'

const StepIndicator = React.forwardRef<HTMLDivElement, StepIndicatorProps>(
  ({ className, status = 'pending', ...props }, ref) => {
    const Icon = status === 'completed' ? Check : status === 'current' ? Clock : Circle
    
    return (
      <div
        ref={ref}
        className={cn(
          'flex h-8 w-8 items-center justify-center rounded-full border-2 shrink-0',
          {
            'border-primary bg-primary text-primary-foreground': status === 'completed',
            'border-primary bg-background text-primary': status === 'current',
            'border-muted-foreground text-muted-foreground': status === 'pending',
          },
          className
        )}
        {...props}
      >
        <Icon className="h-4 w-4" />
      </div>
    )
  }
)
StepIndicator.displayName = 'StepIndicator'

const StepContent = React.forwardRef<HTMLDivElement, StepContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex-1 space-y-2', className)}
      {...props}
    >
      {children}
    </div>
  )
)
StepContent.displayName = 'StepContent'

const StepTitle = React.forwardRef<HTMLParagraphElement, StepTitleProps>(
  ({ className, children, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('font-medium leading-none', className)}
      {...props}
    >
      {children}
    </p>
  )
)
StepTitle.displayName = 'StepTitle'

const StepDescription = React.forwardRef<HTMLParagraphElement, StepDescriptionProps>(
  ({ className, children, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-muted-foreground', className)}
      {...props}
    >
      {children}
    </p>
  )
)
StepDescription.displayName = 'StepDescription'

export {
  Steps,
  Step,
  StepIndicator,
  StepContent,
  StepTitle,
  StepDescription,
}
