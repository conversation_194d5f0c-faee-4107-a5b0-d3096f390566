import * as React from "react"
import { cn } from "../../lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { PanelLeft } from "lucide-react"

// Simple sidebar context
const SidebarContext = React.createContext<{
  open: boolean
  setOpen: (open: boolean) => void
  toggle: () => void
} | null>(null)

export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }
  return context
}

export function SidebarProvider({ 
  children, 
  defaultOpen = true 
}: { 
  children: React.ReactNode
  defaultOpen?: boolean 
}) {
  const [open, setOpen] = React.useState(defaultOpen)
  
  const toggle = React.useCallback(() => {
    setOpen(prev => !prev)
  }, [])

  return (
    <SidebarContext.Provider value={{ open, setOpen, toggle }}>
      <div className="flex min-h-screen">
        {children}
      </div>
    </SidebarContext.Provider>
  )
}

export function Sidebar({ 
  children, 
  className,
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  const { open } = useSidebar()
  
  return (
    <div
      className={cn(
        "fixed inset-y-0 left-0 z-50 bg-background border-r transition-all duration-300",
        open ? "w-64" : "w-16",
        className
      )}
      {...props}
    >
      <div className="flex h-full w-full flex-col">
        {children}
      </div>
    </div>
  )
}

export function SidebarHeader({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn("flex flex-col gap-2 p-4 border-b", className)} 
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarContent({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn("flex-1 overflow-auto p-2", className)} 
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarFooter({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn("p-4 border-t", className)} 
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarGroup({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn("flex flex-col gap-2", className)} 
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarGroupLabel({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  const { open } = useSidebar()
  
  if (!open) return null
  
  return (
    <div 
      className={cn(
        "px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wider",
        className
      )} 
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarGroupContent({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn("space-y-1", className)} 
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarMenu({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLUListElement>) {
  return (
    <ul 
      className={cn("space-y-1", className)} 
      {...props}
    >
      {children}
    </ul>
  )
}

export function SidebarMenuItem({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLLIElement>) {
  return (
    <li 
      className={cn("", className)} 
      {...props}
    >
      {children}
    </li>
  )
}

export function SidebarMenuButton({ 
  children, 
  className,
  isActive = false,
  onClick,
  ...props 
}: React.HTMLAttributes<HTMLDivElement> & {
  isActive?: boolean
}) {
  const { open } = useSidebar()
  
  const buttonClass = cn(
    "flex items-center w-full px-3 py-2 text-sm rounded-md transition-colors cursor-pointer",
    "hover:bg-accent hover:text-accent-foreground",
    isActive && "bg-accent text-accent-foreground",
    !open && "justify-center px-2",
    className
  )

  return (
    <div className={buttonClass} onClick={onClick} {...props}>
      <div className="flex items-center gap-2">
        {/* 图标容器 */}
        <div className="flex-shrink-0">
          {React.Children.toArray(children).find((child) => 
            React.isValidElement(child) && 
            typeof child.type === 'function'
          )}
        </div>
        {/* 文本容器 - 仅在展开状态显示 */}
        {open && (
          <span className="truncate">
            {React.Children.toArray(children).find((child) => 
              typeof child === 'string' || 
              (React.isValidElement(child) && child.type === 'span')
            )}
          </span>
        )}
      </div>
    </div>
  )
}

export function SidebarInset({ 
  children, 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  const { open } = useSidebar()
  
  return (
    <div
      className={cn(
        "flex-1 transition-all duration-300",
        open ? "ml-64 w-[calc(100%-16rem)]" : "ml-16 w-[calc(100%-4rem)]",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarTrigger({ 
  className, 
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { toggle } = useSidebar()
  
  return (
    <Button
      variant="ghost"
      size="icon"
      className={cn("h-8 w-8", className)}
      onClick={toggle}
      {...props}
    >
      <PanelLeft className="h-4 w-4" />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  )
}
