import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface AlertDialogProps {
  children: React.ReactNode
}

interface AlertDialogTriggerProps {
  children: React.ReactNode
  asChild?: boolean
}

interface AlertDialogContentProps {
  children: React.ReactNode
  className?: string
}

const AlertDialogContext = React.createContext<{
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}>({
  isOpen: false,
  setIsOpen: () => {}
})

const AlertDialog: React.FC<AlertDialogProps> = ({ children }) => {
  const [isOpen, setIsOpen] = React.useState(false)
  
  return (
    <AlertDialogContext.Provider value={{ isOpen, setIsOpen }}>
      {children}
    </AlertDialogContext.Provider>
  )
}

const AlertDialogTrigger: React.FC<AlertDialogTriggerProps> = ({ children, asChild }) => {
  const { setIsOpen } = React.useContext(AlertDialogContext)
  
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      onClick: () => setIsOpen(true)
    } as React.HTMLAttributes<HTMLElement>)
  }
  
  return (
    <div onClick={() => setIsOpen(true)}>
      {children}
    </div>
  )
}

const AlertDialogContent: React.FC<AlertDialogContentProps> = ({ children, className }) => {
  const { isOpen } = React.useContext(AlertDialogContext)
  
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg sm:rounded-lg">
        <div
          className={cn("grid gap-4", className)}
        >
          {children}
        </div>
      </div>
    </div>
  )
}

const AlertDialogHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => (
  <div className={cn("flex flex-col space-y-2 text-center sm:text-left", className)}>
    {children}
  </div>
)

const AlertDialogFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => (
  <div className={cn("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2", className)}>
    {children}
  </div>
)

const AlertDialogTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => (
  <h2 className={cn("text-lg font-semibold", className)}>
    {children}
  </h2>
)

const AlertDialogDescription: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => (
  <p className={cn("text-sm text-muted-foreground", className)}>
    {children}
  </p>
)

const AlertDialogAction: React.FC<{ 
  children: React.ReactNode 
  onClick?: () => void
  className?: string
}> = ({ children, onClick, className }) => {
  const { setIsOpen } = React.useContext(AlertDialogContext)
  
  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    setIsOpen(false)
  }
  
  return (
    <Button onClick={handleClick} className={className}>
      {children}
    </Button>
  )
}

const AlertDialogCancel: React.FC<{ 
  children: React.ReactNode
  className?: string
}> = ({ children, className }) => {
  const { setIsOpen } = React.useContext(AlertDialogContext)
  
  return (
    <Button 
      variant="outline" 
      onClick={() => setIsOpen(false)} 
      className={cn("mt-2 sm:mt-0", className)}
    >
      {children}
    </Button>
  )
}

export {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
}
