import { client } from './client.gen';

// Configure the API client with base URL and authentication
export const configureApiClient = () => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  
  client.setConfig({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
    // Add authentication token from localStorage if available
    // auth: () => {
    //   const token = localStorage.getItem('access_token');
    //   return token ? `Bearer ${token || undefined}` : undefined;
    auth: (security) => {
      const token = localStorage.getItem('access_token');
      console.log('Auth callback called:', { security, token: token ? `${token.substring(0, 20)}...` : 'null' });
      return token || undefined;
    },
  });
};

// Initialize the client configuration
configureApiClient();

export { client as apiClient };

// Re-export types and SDK functions for convenience
export * from './types.gen';
export * from './sdk.gen';
export * from './@tanstack/react-query.gen';
