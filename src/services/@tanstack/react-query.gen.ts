// This file is auto-generated by @hey-api/openapi-ts

import { type Options, loginApiV1AuthTokenPost, readUsersMeApiV1AuthMeGet, changePasswordApiV1AuthChangePasswordPost, updateUserProfileApiV1AuthProfilePut, generateImageCodeApiV1AuthGenerateImageCodePost, sendSmsCodeApiV1AuthSendSmsCodePost, loginWithPhonePasswordApiV1AuthLoginPhonePasswordPost, loginWithPhoneSmsApiV1AuthLoginPhoneSmsPost, cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPost, requestToJoinFactoryApiV1FactoryManagementJoinRequestPost, approveOrRejectRequestApiV1FactoryManagementApproveRequestPost, getPendingRequestsApiV1FactoryManagementPendingRequestsGet, getFactoryMembersApiV1FactoryManagementMembersGet, updateUserRoleApiV1FactoryManagementUserUserIdRolePut, suspendUserApiV1FactoryManagementUserUserIdSuspendPost, resignFromFactoryApiV1FactoryManagementResignPost, getSessionStatusApiV1SessionStatusGet, getMyAvailableFactoriesApiV1SessionMyFactoriesGet, switchFactoryContextApiV1SessionSwitchFactoryPost, refreshFactoryContextApiV1SessionRefreshContextPost, extendSessionApiV1SessionExtendPost, logoutApiV1SessionLogoutDelete, getPermissionTreeApiV1PermissionsTreeGet, getAllPermissionsApiV1PermissionsListGet, getAllRolesApiV1RolesGet, createRoleApiV1RolesPost, getActiveRolesApiV1RolesActiveGet, getRolesSummaryApiV1RolesSummaryGet, deleteRoleApiV1RolesRoleIdDelete, getRoleByIdApiV1RolesRoleIdGet, updateRoleApiV1RolesRoleIdPut, removePermissionsFromRoleApiV1RolesRoleIdPermissionsDelete, getRolePermissionsApiV1RolesRoleIdPermissionsGet, assignPermissionsToRoleApiV1RolesRoleIdPermissionsPost, getAvailableUsersApiV1UserManagementAvailableUsersGet, addUsersToFactoryApiV1UserManagementAddUsersPost, bindUserRoleApiV1UserManagementBindRolesPost, suspendUserInFactoryApiV1UserManagementSuspendPost, removeUserFromFactoryApiV1UserManagementRemoveUserDelete, createUserWithFactoryApiV1UserManagementCreateUserPost, getAllSkillsApiV1SkillsGet, createSkillApiV1SkillsPost, getActiveSkillsApiV1SkillsActiveGet, deleteSkillApiV1SkillsSkillIdDelete, getSkillByIdApiV1SkillsSkillIdGet, updateSkillApiV1SkillsSkillIdPut, getUserSkillsApiV1SkillsUserUserIdSkillsGet, assignSkillsToUserApiV1SkillsAssignPost, modifySkillProficiencyApiV1SkillsModifyProficiencyPut, certifyUserSkillApiV1SkillsCertifyPost, removeUserSkillApiV1SkillsRemoveDelete, getAllCraftsApiV1CraftsGet, createCraftApiV1CraftsPost, getEnabledCraftsApiV1CraftsEnabledGet, deleteCraftApiV1CraftsCraftIdDelete, getCraftByIdApiV1CraftsCraftIdGet, updateCraftApiV1CraftsCraftIdPut, getCraftByCodeApiV1CraftsCodeCraftCodeGet, createCraftRouteApiV1CraftsRoutesPost, createBulkCraftRoutesApiV1CraftsRoutesBulkPost, getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet, getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGet, deleteCraftRouteApiV1CraftsRoutesRouteIdDelete, updateCraftRouteApiV1CraftsRoutesRouteIdPut, reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePost, getDepartmentsApiV1DepartmentsGet, createDepartmentApiV1DepartmentsPost, searchDepartmentsApiV1DepartmentsSearchGet, deleteDepartmentApiV1DepartmentsDepartmentIdDelete, getDepartmentByIdApiV1DepartmentsDepartmentIdGet, updateDepartmentApiV1DepartmentsDepartmentIdPut, getAllOrdersApiV1OrdersGet, createOrderApiV1OrdersPost, getOrderByOrderNoMainApiV1OrdersOrderNoGet, deleteOrderApiV1OrdersOrderIdDelete, updateOrderApiV1OrdersOrderIdPut, updateOrderStatusApiV1OrdersOrderIdStatusPut, startOrderApiV1OrdersOrderNoStartPost, updateCraftProgressApiV1OrdersOrderIdCraftProgressPut, updateOrderAmountApiV1OrdersOrderIdAmountPut, addOrderLinesApiV1OrdersOrderLinesBulkPost, updateProductionApiV1OrdersProductionPut, getOrderStatisticsApiV1OrdersStatisticsSummaryGet, getDashboardDataApiV1OrdersDashboardDataGet, searchOrderPartsApiV1OrderPartsGet, createOrderPartApiV1OrderPartsPost, getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGet, deleteOrderPartApiV1OrderPartsOrderPartIdDelete, getOrderPartByIdApiV1OrderPartsOrderPartIdGet, updateOrderPartApiV1OrderPartsOrderPartIdPut, getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGet, updateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPut, bulkCreateOrderPartsApiV1OrderPartsBulkPost, getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGet, searchOrderBundlesApiV1OrderBundlesGet, createOrderBundleApiV1OrderBundlesPost, getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGet, getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGet, deleteOrderBundleApiV1OrderBundlesOrderBundleIdDelete, getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGet, updateOrderBundleApiV1OrderBundlesOrderBundleIdPut, updateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPut, updateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPut, bulkCreateOrderBundlesApiV1OrderBundlesBulkPost, getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGet, getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet, createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPost, getOrderCraftByIdApiV1CraftsOrderCraftIdGet, updateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPut, updateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPut, getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGet, getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGet, getOrderCraftStatisticsApiV1StatisticsGet, createCraftInstanceApiV1CraftInstancesPost, qrScanRegisterApiV1CraftInstancesQrScanPost, searchCraftInstancesApiV1CraftInstancesSearchGet, getCraftInstanceApiV1CraftInstancesInstanceIdGet, verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPost, rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPost, getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGet, getAvailableRegistrationData, getRegistrationSummary, getBillsApiV1BillsGet, createBillApiV1BillsPost, getBillApiV1BillsBillIdGet, updateBillApiV1BillsBillIdPut, submitBillForReviewApiV1BillsBillIdSubmitPost, approveBillApiV1BillsBillIdApprovePost, rejectBillApiV1BillsBillIdRejectPost, markBillAsPaidApiV1BillsBillIdPayPost, cancelBillApiV1BillsBillIdCancelPost, getSettlementSummaryApiV1BillsSettlementSummaryGet, disputeInstanceApiV1BillsInstancesInstanceIdDisputePost, resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePost, getPendingBillsApiV1BillsPendingGet, getDashboardStatisticsApiV1StatisticsDashboardGet, getScanSummaryApiV1StatisticsScanSummaryGet, getOnlineEmployeesApiV1StatisticsOnlineEmployeesGet, createTemplateApiV1CraftTemplatesPost, deleteTemplateApiV1CraftTemplatesTemplateIdDelete, getTemplateApiV1CraftTemplatesTemplateIdGet, updateTemplateApiV1CraftTemplatesTemplateIdPut, searchTemplatesApiV1CraftTemplatesSearchPost, duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePost, getTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGet, getTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGet, addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPost, removeCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDelete, getCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGet, updateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePut, createAuditRecordApiV1PriceAuditPost, deleteAuditRecordApiV1PriceAuditRecordIdDelete, getAuditRecordApiV1PriceAuditRecordIdGet, updateAuditRecordApiV1PriceAuditRecordIdPut, searchAuditRecordsApiV1PriceAuditSearchPost, getAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGet, getPendingReviewsApiV1PriceAuditPendingReviewsGet, getUnderReviewApiV1PriceAuditUnderReviewGet, getOverdueRecordsApiV1PriceAuditOverdueGet, getHighPriorityRecordsApiV1PriceAuditHighPriorityGet, getEscalatedRecordsApiV1PriceAuditEscalatedGet, performAuditActionApiV1PriceAuditActionPost, bulkAuditActionApiV1PriceAuditBulkActionPost, assignReviewerApiV1PriceAuditAssignReviewerPost, setPriorityApiV1PriceAuditSetPriorityPost, addEvidenceApiV1PriceAuditAddEvidencePost, escalateAuditApiV1PriceAuditEscalatePost, setDeadlineApiV1PriceAuditSetDeadlinePost, getAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGet, getDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGet, getUserStatisticsApiV1PriceAuditStatisticsUserUserIdGet, getFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGet, getTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGet, getTopReportersApiV1PriceAuditStatisticsReportersGet, getReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGet, getEscalationStatisticsApiV1PriceAuditStatisticsEscalationGet, getDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGet, getAuditDashboardApiV1PriceAuditDashboardGet, getAuditReportApiV1PriceAuditReportGet, getAuditSummaryApiV1PriceAuditRecordIdSummaryGet, rootGet, healthCheckHealthGet } from '../sdk.gen';
import { queryOptions, type UseMutationOptions, type DefaultError, infiniteQueryOptions, type InfiniteData } from '@tanstack/react-query';
import type { LoginApiV1AuthTokenPostData, LoginApiV1AuthTokenPostError, LoginApiV1AuthTokenPostResponse, ReadUsersMeApiV1AuthMeGetData, ChangePasswordApiV1AuthChangePasswordPostData, ChangePasswordApiV1AuthChangePasswordPostError, UpdateUserProfileApiV1AuthProfilePutData, UpdateUserProfileApiV1AuthProfilePutError, UpdateUserProfileApiV1AuthProfilePutResponse, GenerateImageCodeApiV1AuthGenerateImageCodePostData, GenerateImageCodeApiV1AuthGenerateImageCodePostError, GenerateImageCodeApiV1AuthGenerateImageCodePostResponse, SendSmsCodeApiV1AuthSendSmsCodePostData, SendSmsCodeApiV1AuthSendSmsCodePostError, SendSmsCodeApiV1AuthSendSmsCodePostResponse, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostError, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponse, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostError, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponse, CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostError, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponse, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostError, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponse, GetPendingRequestsApiV1FactoryManagementPendingRequestsGetData, GetFactoryMembersApiV1FactoryManagementMembersGetData, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutError, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponse, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostError, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponse, ResignFromFactoryApiV1FactoryManagementResignPostData, ResignFromFactoryApiV1FactoryManagementResignPostResponse, GetSessionStatusApiV1SessionStatusGetData, GetMyAvailableFactoriesApiV1SessionMyFactoriesGetData, SwitchFactoryContextApiV1SessionSwitchFactoryPostData, SwitchFactoryContextApiV1SessionSwitchFactoryPostError, SwitchFactoryContextApiV1SessionSwitchFactoryPostResponse, RefreshFactoryContextApiV1SessionRefreshContextPostData, RefreshFactoryContextApiV1SessionRefreshContextPostResponse, ExtendSessionApiV1SessionExtendPostData, LogoutApiV1SessionLogoutDeleteData, GetPermissionTreeApiV1PermissionsTreeGetData, GetAllPermissionsApiV1PermissionsListGetData, GetAllRolesApiV1RolesGetData, CreateRoleApiV1RolesPostData, CreateRoleApiV1RolesPostError, CreateRoleApiV1RolesPostResponse, GetActiveRolesApiV1RolesActiveGetData, GetRolesSummaryApiV1RolesSummaryGetData, DeleteRoleApiV1RolesRoleIdDeleteData, DeleteRoleApiV1RolesRoleIdDeleteError, DeleteRoleApiV1RolesRoleIdDeleteResponse, GetRoleByIdApiV1RolesRoleIdGetData, UpdateRoleApiV1RolesRoleIdPutData, UpdateRoleApiV1RolesRoleIdPutError, UpdateRoleApiV1RolesRoleIdPutResponse, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteError, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponse, GetRolePermissionsApiV1RolesRoleIdPermissionsGetData, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostError, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponse, GetAvailableUsersApiV1UserManagementAvailableUsersGetData, AddUsersToFactoryApiV1UserManagementAddUsersPostData, AddUsersToFactoryApiV1UserManagementAddUsersPostError, AddUsersToFactoryApiV1UserManagementAddUsersPostResponse, BindUserRoleApiV1UserManagementBindRolesPostData, BindUserRoleApiV1UserManagementBindRolesPostError, BindUserRoleApiV1UserManagementBindRolesPostResponse, SuspendUserInFactoryApiV1UserManagementSuspendPostData, SuspendUserInFactoryApiV1UserManagementSuspendPostError, SuspendUserInFactoryApiV1UserManagementSuspendPostResponse, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteError, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponse, CreateUserWithFactoryApiV1UserManagementCreateUserPostData, CreateUserWithFactoryApiV1UserManagementCreateUserPostError, CreateUserWithFactoryApiV1UserManagementCreateUserPostResponse, GetAllSkillsApiV1SkillsGetData, CreateSkillApiV1SkillsPostData, CreateSkillApiV1SkillsPostError, CreateSkillApiV1SkillsPostResponse, GetActiveSkillsApiV1SkillsActiveGetData, DeleteSkillApiV1SkillsSkillIdDeleteData, DeleteSkillApiV1SkillsSkillIdDeleteError, DeleteSkillApiV1SkillsSkillIdDeleteResponse, GetSkillByIdApiV1SkillsSkillIdGetData, UpdateSkillApiV1SkillsSkillIdPutData, UpdateSkillApiV1SkillsSkillIdPutError, UpdateSkillApiV1SkillsSkillIdPutResponse, GetUserSkillsApiV1SkillsUserUserIdSkillsGetData, AssignSkillsToUserApiV1SkillsAssignPostData, AssignSkillsToUserApiV1SkillsAssignPostError, AssignSkillsToUserApiV1SkillsAssignPostResponse, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutError, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponse, CertifyUserSkillApiV1SkillsCertifyPostData, CertifyUserSkillApiV1SkillsCertifyPostError, CertifyUserSkillApiV1SkillsCertifyPostResponse, RemoveUserSkillApiV1SkillsRemoveDeleteData, RemoveUserSkillApiV1SkillsRemoveDeleteError, RemoveUserSkillApiV1SkillsRemoveDeleteResponse, GetAllCraftsApiV1CraftsGetData, CreateCraftApiV1CraftsPostData, CreateCraftApiV1CraftsPostError, CreateCraftApiV1CraftsPostResponse, GetEnabledCraftsApiV1CraftsEnabledGetData, DeleteCraftApiV1CraftsCraftIdDeleteData, DeleteCraftApiV1CraftsCraftIdDeleteError, GetCraftByIdApiV1CraftsCraftIdGetData, UpdateCraftApiV1CraftsCraftIdPutData, UpdateCraftApiV1CraftsCraftIdPutError, UpdateCraftApiV1CraftsCraftIdPutResponse, GetCraftByCodeApiV1CraftsCodeCraftCodeGetData, CreateCraftRouteApiV1CraftsRoutesPostData, CreateCraftRouteApiV1CraftsRoutesPostError, CreateCraftRouteApiV1CraftsRoutesPostResponse, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostError, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponse, GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetData, GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetData, DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData, DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteError, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutError, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponse, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostError, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponse, GetDepartmentsApiV1DepartmentsGetData, CreateDepartmentApiV1DepartmentsPostData, CreateDepartmentApiV1DepartmentsPostError, CreateDepartmentApiV1DepartmentsPostResponse, SearchDepartmentsApiV1DepartmentsSearchGetData, DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteData, DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteError, DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponse, GetDepartmentByIdApiV1DepartmentsDepartmentIdGetData, UpdateDepartmentApiV1DepartmentsDepartmentIdPutData, UpdateDepartmentApiV1DepartmentsDepartmentIdPutError, UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponse, GetAllOrdersApiV1OrdersGetData, CreateOrderApiV1OrdersPostData, CreateOrderApiV1OrdersPostError, CreateOrderApiV1OrdersPostResponse, GetOrderByOrderNoMainApiV1OrdersOrderNoGetData, DeleteOrderApiV1OrdersOrderIdDeleteData, DeleteOrderApiV1OrdersOrderIdDeleteError, UpdateOrderApiV1OrdersOrderIdPutData, UpdateOrderApiV1OrdersOrderIdPutError, UpdateOrderApiV1OrdersOrderIdPutResponse, UpdateOrderStatusApiV1OrdersOrderIdStatusPutData, UpdateOrderStatusApiV1OrdersOrderIdStatusPutError, UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponse, StartOrderApiV1OrdersOrderNoStartPostData, StartOrderApiV1OrdersOrderNoStartPostError, StartOrderApiV1OrdersOrderNoStartPostResponse, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutError, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponse, UpdateOrderAmountApiV1OrdersOrderIdAmountPutData, UpdateOrderAmountApiV1OrdersOrderIdAmountPutError, UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponse, AddOrderLinesApiV1OrdersOrderLinesBulkPostData, AddOrderLinesApiV1OrdersOrderLinesBulkPostError, AddOrderLinesApiV1OrdersOrderLinesBulkPostResponse, UpdateProductionApiV1OrdersProductionPutData, UpdateProductionApiV1OrdersProductionPutError, UpdateProductionApiV1OrdersProductionPutResponse, GetOrderStatisticsApiV1OrdersStatisticsSummaryGetData, GetDashboardDataApiV1OrdersDashboardDataGetData, SearchOrderPartsApiV1OrderPartsGetData, CreateOrderPartApiV1OrderPartsPostData, CreateOrderPartApiV1OrderPartsPostError, CreateOrderPartApiV1OrderPartsPostResponse, GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetData, DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData, DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteError, GetOrderPartByIdApiV1OrderPartsOrderPartIdGetData, UpdateOrderPartApiV1OrderPartsOrderPartIdPutData, UpdateOrderPartApiV1OrderPartsOrderPartIdPutError, UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponse, GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetData, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutError, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponse, BulkCreateOrderPartsApiV1OrderPartsBulkPostData, BulkCreateOrderPartsApiV1OrderPartsBulkPostError, BulkCreateOrderPartsApiV1OrderPartsBulkPostResponse, GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetData, SearchOrderBundlesApiV1OrderBundlesGetData, CreateOrderBundleApiV1OrderBundlesPostData, CreateOrderBundleApiV1OrderBundlesPostError, CreateOrderBundleApiV1OrderBundlesPostResponse, GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetData, GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetData, DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData, DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteError, GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetData, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutError, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponse, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutError, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponse, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutError, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponse, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostError, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponse, GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetData, GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetData, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostError, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponse, GetOrderCraftByIdApiV1CraftsOrderCraftIdGetData, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutError, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponse, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutError, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponse, GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetData, GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetData, GetOrderCraftStatisticsApiV1StatisticsGetData, CreateCraftInstanceApiV1CraftInstancesPostData, CreateCraftInstanceApiV1CraftInstancesPostError, CreateCraftInstanceApiV1CraftInstancesPostResponse, QrScanRegisterApiV1CraftInstancesQrScanPostData, QrScanRegisterApiV1CraftInstancesQrScanPostError, QrScanRegisterApiV1CraftInstancesQrScanPostResponse, SearchCraftInstancesApiV1CraftInstancesSearchGetData, SearchCraftInstancesApiV1CraftInstancesSearchGetError, SearchCraftInstancesApiV1CraftInstancesSearchGetResponse, GetCraftInstanceApiV1CraftInstancesInstanceIdGetData, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostError, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponse, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostError, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponse, GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetData, GetAvailableRegistrationDataData, GetRegistrationSummaryData, GetBillsApiV1BillsGetData, CreateBillApiV1BillsPostData, CreateBillApiV1BillsPostError, CreateBillApiV1BillsPostResponse, GetBillApiV1BillsBillIdGetData, UpdateBillApiV1BillsBillIdPutData, UpdateBillApiV1BillsBillIdPutError, UpdateBillApiV1BillsBillIdPutResponse, SubmitBillForReviewApiV1BillsBillIdSubmitPostData, SubmitBillForReviewApiV1BillsBillIdSubmitPostError, SubmitBillForReviewApiV1BillsBillIdSubmitPostResponse, ApproveBillApiV1BillsBillIdApprovePostData, ApproveBillApiV1BillsBillIdApprovePostError, ApproveBillApiV1BillsBillIdApprovePostResponse, RejectBillApiV1BillsBillIdRejectPostData, RejectBillApiV1BillsBillIdRejectPostError, RejectBillApiV1BillsBillIdRejectPostResponse, MarkBillAsPaidApiV1BillsBillIdPayPostData, MarkBillAsPaidApiV1BillsBillIdPayPostError, MarkBillAsPaidApiV1BillsBillIdPayPostResponse, CancelBillApiV1BillsBillIdCancelPostData, CancelBillApiV1BillsBillIdCancelPostError, CancelBillApiV1BillsBillIdCancelPostResponse, GetSettlementSummaryApiV1BillsSettlementSummaryGetData, DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData, DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostError, ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData, ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostError, GetPendingBillsApiV1BillsPendingGetData, GetDashboardStatisticsApiV1StatisticsDashboardGetData, GetScanSummaryApiV1StatisticsScanSummaryGetData, GetOnlineEmployeesApiV1StatisticsOnlineEmployeesGetData, CreateTemplateApiV1CraftTemplatesPostData, CreateTemplateApiV1CraftTemplatesPostError, CreateTemplateApiV1CraftTemplatesPostResponse, DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteData, DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteError, GetTemplateApiV1CraftTemplatesTemplateIdGetData, UpdateTemplateApiV1CraftTemplatesTemplateIdPutData, UpdateTemplateApiV1CraftTemplatesTemplateIdPutError, UpdateTemplateApiV1CraftTemplatesTemplateIdPutResponse, SearchTemplatesApiV1CraftTemplatesSearchPostData, SearchTemplatesApiV1CraftTemplatesSearchPostError, SearchTemplatesApiV1CraftTemplatesSearchPostResponse, DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostData, DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostError, DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostResponse, GetTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGetData, GetTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGetData, AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostData, AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostError, AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostResponse, RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteData, RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteError, RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteResponse, GetCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGetData, UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutData, UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutError, UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutResponse, CreateAuditRecordApiV1PriceAuditPostData, CreateAuditRecordApiV1PriceAuditPostError, CreateAuditRecordApiV1PriceAuditPostResponse, DeleteAuditRecordApiV1PriceAuditRecordIdDeleteData, DeleteAuditRecordApiV1PriceAuditRecordIdDeleteError, GetAuditRecordApiV1PriceAuditRecordIdGetData, UpdateAuditRecordApiV1PriceAuditRecordIdPutData, UpdateAuditRecordApiV1PriceAuditRecordIdPutError, UpdateAuditRecordApiV1PriceAuditRecordIdPutResponse, SearchAuditRecordsApiV1PriceAuditSearchPostData, SearchAuditRecordsApiV1PriceAuditSearchPostError, SearchAuditRecordsApiV1PriceAuditSearchPostResponse, GetAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGetData, GetPendingReviewsApiV1PriceAuditPendingReviewsGetData, GetUnderReviewApiV1PriceAuditUnderReviewGetData, GetOverdueRecordsApiV1PriceAuditOverdueGetData, GetHighPriorityRecordsApiV1PriceAuditHighPriorityGetData, GetEscalatedRecordsApiV1PriceAuditEscalatedGetData, PerformAuditActionApiV1PriceAuditActionPostData, PerformAuditActionApiV1PriceAuditActionPostError, PerformAuditActionApiV1PriceAuditActionPostResponse, BulkAuditActionApiV1PriceAuditBulkActionPostData, BulkAuditActionApiV1PriceAuditBulkActionPostError, BulkAuditActionApiV1PriceAuditBulkActionPostResponse, AssignReviewerApiV1PriceAuditAssignReviewerPostData, AssignReviewerApiV1PriceAuditAssignReviewerPostError, AssignReviewerApiV1PriceAuditAssignReviewerPostResponse, SetPriorityApiV1PriceAuditSetPriorityPostData, SetPriorityApiV1PriceAuditSetPriorityPostError, SetPriorityApiV1PriceAuditSetPriorityPostResponse, AddEvidenceApiV1PriceAuditAddEvidencePostData, AddEvidenceApiV1PriceAuditAddEvidencePostError, AddEvidenceApiV1PriceAuditAddEvidencePostResponse, EscalateAuditApiV1PriceAuditEscalatePostData, EscalateAuditApiV1PriceAuditEscalatePostError, EscalateAuditApiV1PriceAuditEscalatePostResponse, SetDeadlineApiV1PriceAuditSetDeadlinePostData, SetDeadlineApiV1PriceAuditSetDeadlinePostError, SetDeadlineApiV1PriceAuditSetDeadlinePostResponse, GetAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGetData, GetDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGetData, GetUserStatisticsApiV1PriceAuditStatisticsUserUserIdGetData, GetFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGetData, GetTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGetData, GetTopReportersApiV1PriceAuditStatisticsReportersGetData, GetReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGetData, GetEscalationStatisticsApiV1PriceAuditStatisticsEscalationGetData, GetDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGetData, GetAuditDashboardApiV1PriceAuditDashboardGetData, GetAuditReportApiV1PriceAuditReportGetData, GetAuditSummaryApiV1PriceAuditRecordIdSummaryGetData, RootGetData, HealthCheckHealthGetData } from '../types.gen';
import type { AxiosError } from 'axios';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseURL' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseURL: (options?.client ?? _heyApiClient).getConfig().baseURL } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const loginApiV1AuthTokenPostQueryKey = (options: Options<LoginApiV1AuthTokenPostData>) => createQueryKey('loginApiV1AuthTokenPost', options);

/**
 * Login
 * Login and get access token with session context.
 */
export const loginApiV1AuthTokenPostOptions = (options: Options<LoginApiV1AuthTokenPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await loginApiV1AuthTokenPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginApiV1AuthTokenPostQueryKey(options)
    });
};

/**
 * Login
 * Login and get access token with session context.
 */
export const loginApiV1AuthTokenPostMutation = (options?: Partial<Options<LoginApiV1AuthTokenPostData>>): UseMutationOptions<LoginApiV1AuthTokenPostResponse, AxiosError<LoginApiV1AuthTokenPostError>, Options<LoginApiV1AuthTokenPostData>> => {
    const mutationOptions: UseMutationOptions<LoginApiV1AuthTokenPostResponse, AxiosError<LoginApiV1AuthTokenPostError>, Options<LoginApiV1AuthTokenPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await loginApiV1AuthTokenPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const readUsersMeApiV1AuthMeGetQueryKey = (options?: Options<ReadUsersMeApiV1AuthMeGetData>) => createQueryKey('readUsersMeApiV1AuthMeGet', options);

/**
 * Read Users Me
 * Get current user information with factory context, roles and permissions from session.
 */
export const readUsersMeApiV1AuthMeGetOptions = (options?: Options<ReadUsersMeApiV1AuthMeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await readUsersMeApiV1AuthMeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: readUsersMeApiV1AuthMeGetQueryKey(options)
    });
};

export const changePasswordApiV1AuthChangePasswordPostQueryKey = (options: Options<ChangePasswordApiV1AuthChangePasswordPostData>) => createQueryKey('changePasswordApiV1AuthChangePasswordPost', options);

/**
 * Change Password
 * Change user password.
 */
export const changePasswordApiV1AuthChangePasswordPostOptions = (options: Options<ChangePasswordApiV1AuthChangePasswordPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await changePasswordApiV1AuthChangePasswordPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: changePasswordApiV1AuthChangePasswordPostQueryKey(options)
    });
};

/**
 * Change Password
 * Change user password.
 */
export const changePasswordApiV1AuthChangePasswordPostMutation = (options?: Partial<Options<ChangePasswordApiV1AuthChangePasswordPostData>>): UseMutationOptions<unknown, AxiosError<ChangePasswordApiV1AuthChangePasswordPostError>, Options<ChangePasswordApiV1AuthChangePasswordPostData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<ChangePasswordApiV1AuthChangePasswordPostError>, Options<ChangePasswordApiV1AuthChangePasswordPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await changePasswordApiV1AuthChangePasswordPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update User Profile
 * Update user profile information (name, email).
 */
export const updateUserProfileApiV1AuthProfilePutMutation = (options?: Partial<Options<UpdateUserProfileApiV1AuthProfilePutData>>): UseMutationOptions<UpdateUserProfileApiV1AuthProfilePutResponse, AxiosError<UpdateUserProfileApiV1AuthProfilePutError>, Options<UpdateUserProfileApiV1AuthProfilePutData>> => {
    const mutationOptions: UseMutationOptions<UpdateUserProfileApiV1AuthProfilePutResponse, AxiosError<UpdateUserProfileApiV1AuthProfilePutError>, Options<UpdateUserProfileApiV1AuthProfilePutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUserProfileApiV1AuthProfilePut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const generateImageCodeApiV1AuthGenerateImageCodePostQueryKey = (options: Options<GenerateImageCodeApiV1AuthGenerateImageCodePostData>) => createQueryKey('generateImageCodeApiV1AuthGenerateImageCodePost', options);

/**
 * Generate Image Code
 * Generate image validation code.
 */
export const generateImageCodeApiV1AuthGenerateImageCodePostOptions = (options: Options<GenerateImageCodeApiV1AuthGenerateImageCodePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await generateImageCodeApiV1AuthGenerateImageCodePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: generateImageCodeApiV1AuthGenerateImageCodePostQueryKey(options)
    });
};

/**
 * Generate Image Code
 * Generate image validation code.
 */
export const generateImageCodeApiV1AuthGenerateImageCodePostMutation = (options?: Partial<Options<GenerateImageCodeApiV1AuthGenerateImageCodePostData>>): UseMutationOptions<GenerateImageCodeApiV1AuthGenerateImageCodePostResponse, AxiosError<GenerateImageCodeApiV1AuthGenerateImageCodePostError>, Options<GenerateImageCodeApiV1AuthGenerateImageCodePostData>> => {
    const mutationOptions: UseMutationOptions<GenerateImageCodeApiV1AuthGenerateImageCodePostResponse, AxiosError<GenerateImageCodeApiV1AuthGenerateImageCodePostError>, Options<GenerateImageCodeApiV1AuthGenerateImageCodePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await generateImageCodeApiV1AuthGenerateImageCodePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const sendSmsCodeApiV1AuthSendSmsCodePostQueryKey = (options: Options<SendSmsCodeApiV1AuthSendSmsCodePostData>) => createQueryKey('sendSmsCodeApiV1AuthSendSmsCodePost', options);

/**
 * Send Sms Code
 * Send SMS validation code.
 */
export const sendSmsCodeApiV1AuthSendSmsCodePostOptions = (options: Options<SendSmsCodeApiV1AuthSendSmsCodePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await sendSmsCodeApiV1AuthSendSmsCodePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: sendSmsCodeApiV1AuthSendSmsCodePostQueryKey(options)
    });
};

/**
 * Send Sms Code
 * Send SMS validation code.
 */
export const sendSmsCodeApiV1AuthSendSmsCodePostMutation = (options?: Partial<Options<SendSmsCodeApiV1AuthSendSmsCodePostData>>): UseMutationOptions<SendSmsCodeApiV1AuthSendSmsCodePostResponse, AxiosError<SendSmsCodeApiV1AuthSendSmsCodePostError>, Options<SendSmsCodeApiV1AuthSendSmsCodePostData>> => {
    const mutationOptions: UseMutationOptions<SendSmsCodeApiV1AuthSendSmsCodePostResponse, AxiosError<SendSmsCodeApiV1AuthSendSmsCodePostError>, Options<SendSmsCodeApiV1AuthSendSmsCodePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await sendSmsCodeApiV1AuthSendSmsCodePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostQueryKey = (options: Options<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData>) => createQueryKey('loginWithPhonePasswordApiV1AuthLoginPhonePasswordPost', options);

/**
 * Login With Phone Password
 * Login with phone + password + image validation code.
 */
export const loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostOptions = (options: Options<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await loginWithPhonePasswordApiV1AuthLoginPhonePasswordPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostQueryKey(options)
    });
};

/**
 * Login With Phone Password
 * Login with phone + password + image validation code.
 */
export const loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostMutation = (options?: Partial<Options<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData>>): UseMutationOptions<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponse, AxiosError<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostError>, Options<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData>> => {
    const mutationOptions: UseMutationOptions<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponse, AxiosError<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostError>, Options<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await loginWithPhonePasswordApiV1AuthLoginPhonePasswordPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const loginWithPhoneSmsApiV1AuthLoginPhoneSmsPostQueryKey = (options: Options<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData>) => createQueryKey('loginWithPhoneSmsApiV1AuthLoginPhoneSmsPost', options);

/**
 * Login With Phone Sms
 * Login with phone + SMS code + image validation code.
 */
export const loginWithPhoneSmsApiV1AuthLoginPhoneSmsPostOptions = (options: Options<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await loginWithPhoneSmsApiV1AuthLoginPhoneSmsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginWithPhoneSmsApiV1AuthLoginPhoneSmsPostQueryKey(options)
    });
};

/**
 * Login With Phone Sms
 * Login with phone + SMS code + image validation code.
 */
export const loginWithPhoneSmsApiV1AuthLoginPhoneSmsPostMutation = (options?: Partial<Options<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData>>): UseMutationOptions<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponse, AxiosError<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostError>, Options<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData>> => {
    const mutationOptions: UseMutationOptions<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponse, AxiosError<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostError>, Options<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await loginWithPhoneSmsApiV1AuthLoginPhoneSmsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostQueryKey = (options?: Options<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData>) => createQueryKey('cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPost', options);

/**
 * Cleanup Expired Codes
 * Clean up expired validation codes (admin endpoint).
 */
export const cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostOptions = (options?: Options<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostQueryKey(options)
    });
};

/**
 * Cleanup Expired Codes
 * Clean up expired validation codes (admin endpoint).
 */
export const cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostMutation = (options?: Partial<Options<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData>>): UseMutationOptions<unknown, AxiosError<DefaultError>, Options<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const requestToJoinFactoryApiV1FactoryManagementJoinRequestPostQueryKey = (options: Options<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData>) => createQueryKey('requestToJoinFactoryApiV1FactoryManagementJoinRequestPost', options);

/**
 * Request To Join Factory
 * Request to join a factory.
 */
export const requestToJoinFactoryApiV1FactoryManagementJoinRequestPostOptions = (options: Options<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await requestToJoinFactoryApiV1FactoryManagementJoinRequestPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: requestToJoinFactoryApiV1FactoryManagementJoinRequestPostQueryKey(options)
    });
};

/**
 * Request To Join Factory
 * Request to join a factory.
 */
export const requestToJoinFactoryApiV1FactoryManagementJoinRequestPostMutation = (options?: Partial<Options<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData>>): UseMutationOptions<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponse, AxiosError<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostError>, Options<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData>> => {
    const mutationOptions: UseMutationOptions<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponse, AxiosError<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostError>, Options<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await requestToJoinFactoryApiV1FactoryManagementJoinRequestPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const approveOrRejectRequestApiV1FactoryManagementApproveRequestPostQueryKey = (options: Options<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData>) => createQueryKey('approveOrRejectRequestApiV1FactoryManagementApproveRequestPost', options);

/**
 * Approve Or Reject Request
 * Approve or reject a factory join request (managers only).
 */
export const approveOrRejectRequestApiV1FactoryManagementApproveRequestPostOptions = (options: Options<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await approveOrRejectRequestApiV1FactoryManagementApproveRequestPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: approveOrRejectRequestApiV1FactoryManagementApproveRequestPostQueryKey(options)
    });
};

/**
 * Approve Or Reject Request
 * Approve or reject a factory join request (managers only).
 */
export const approveOrRejectRequestApiV1FactoryManagementApproveRequestPostMutation = (options?: Partial<Options<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData>>): UseMutationOptions<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponse, AxiosError<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostError>, Options<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData>> => {
    const mutationOptions: UseMutationOptions<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponse, AxiosError<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostError>, Options<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await approveOrRejectRequestApiV1FactoryManagementApproveRequestPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getPendingRequestsApiV1FactoryManagementPendingRequestsGetQueryKey = (options?: Options<GetPendingRequestsApiV1FactoryManagementPendingRequestsGetData>) => createQueryKey('getPendingRequestsApiV1FactoryManagementPendingRequestsGet', options);

/**
 * Get Pending Requests
 * Get pending join requests for current factory (managers only).
 */
export const getPendingRequestsApiV1FactoryManagementPendingRequestsGetOptions = (options?: Options<GetPendingRequestsApiV1FactoryManagementPendingRequestsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getPendingRequestsApiV1FactoryManagementPendingRequestsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getPendingRequestsApiV1FactoryManagementPendingRequestsGetQueryKey(options)
    });
};

export const getFactoryMembersApiV1FactoryManagementMembersGetQueryKey = (options?: Options<GetFactoryMembersApiV1FactoryManagementMembersGetData>) => createQueryKey('getFactoryMembersApiV1FactoryManagementMembersGet', options);

/**
 * Get Factory Members
 * Get all members of current factory.
 */
export const getFactoryMembersApiV1FactoryManagementMembersGetOptions = (options?: Options<GetFactoryMembersApiV1FactoryManagementMembersGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getFactoryMembersApiV1FactoryManagementMembersGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getFactoryMembersApiV1FactoryManagementMembersGetQueryKey(options)
    });
};

/**
 * Update User Role
 * Update user's role in current factory (managers only).
 */
export const updateUserRoleApiV1FactoryManagementUserUserIdRolePutMutation = (options?: Partial<Options<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData>>): UseMutationOptions<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponse, AxiosError<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutError>, Options<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData>> => {
    const mutationOptions: UseMutationOptions<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponse, AxiosError<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutError>, Options<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUserRoleApiV1FactoryManagementUserUserIdRolePut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const suspendUserApiV1FactoryManagementUserUserIdSuspendPostQueryKey = (options: Options<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData>) => createQueryKey('suspendUserApiV1FactoryManagementUserUserIdSuspendPost', options);

/**
 * Suspend User
 * Suspend user from current factory (managers only).
 */
export const suspendUserApiV1FactoryManagementUserUserIdSuspendPostOptions = (options: Options<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await suspendUserApiV1FactoryManagementUserUserIdSuspendPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: suspendUserApiV1FactoryManagementUserUserIdSuspendPostQueryKey(options)
    });
};

/**
 * Suspend User
 * Suspend user from current factory (managers only).
 */
export const suspendUserApiV1FactoryManagementUserUserIdSuspendPostMutation = (options?: Partial<Options<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData>>): UseMutationOptions<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponse, AxiosError<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostError>, Options<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData>> => {
    const mutationOptions: UseMutationOptions<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponse, AxiosError<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostError>, Options<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await suspendUserApiV1FactoryManagementUserUserIdSuspendPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const resignFromFactoryApiV1FactoryManagementResignPostQueryKey = (options?: Options<ResignFromFactoryApiV1FactoryManagementResignPostData>) => createQueryKey('resignFromFactoryApiV1FactoryManagementResignPost', options);

/**
 * Resign From Factory
 * Resign from current factory.
 */
export const resignFromFactoryApiV1FactoryManagementResignPostOptions = (options?: Options<ResignFromFactoryApiV1FactoryManagementResignPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await resignFromFactoryApiV1FactoryManagementResignPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: resignFromFactoryApiV1FactoryManagementResignPostQueryKey(options)
    });
};

/**
 * Resign From Factory
 * Resign from current factory.
 */
export const resignFromFactoryApiV1FactoryManagementResignPostMutation = (options?: Partial<Options<ResignFromFactoryApiV1FactoryManagementResignPostData>>): UseMutationOptions<ResignFromFactoryApiV1FactoryManagementResignPostResponse, AxiosError<DefaultError>, Options<ResignFromFactoryApiV1FactoryManagementResignPostData>> => {
    const mutationOptions: UseMutationOptions<ResignFromFactoryApiV1FactoryManagementResignPostResponse, AxiosError<DefaultError>, Options<ResignFromFactoryApiV1FactoryManagementResignPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await resignFromFactoryApiV1FactoryManagementResignPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getSessionStatusApiV1SessionStatusGetQueryKey = (options?: Options<GetSessionStatusApiV1SessionStatusGetData>) => createQueryKey('getSessionStatusApiV1SessionStatusGet', options);

/**
 * Get Session Status
 * Get current session status and factory context.
 */
export const getSessionStatusApiV1SessionStatusGetOptions = (options?: Options<GetSessionStatusApiV1SessionStatusGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getSessionStatusApiV1SessionStatusGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getSessionStatusApiV1SessionStatusGetQueryKey(options)
    });
};

export const getMyAvailableFactoriesApiV1SessionMyFactoriesGetQueryKey = (options?: Options<GetMyAvailableFactoriesApiV1SessionMyFactoriesGetData>) => createQueryKey('getMyAvailableFactoriesApiV1SessionMyFactoriesGet', options);

/**
 * Get My Available Factories
 * Get list of factories current user has access to (using JWT auth).
 */
export const getMyAvailableFactoriesApiV1SessionMyFactoriesGetOptions = (options?: Options<GetMyAvailableFactoriesApiV1SessionMyFactoriesGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getMyAvailableFactoriesApiV1SessionMyFactoriesGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getMyAvailableFactoriesApiV1SessionMyFactoriesGetQueryKey(options)
    });
};

export const switchFactoryContextApiV1SessionSwitchFactoryPostQueryKey = (options: Options<SwitchFactoryContextApiV1SessionSwitchFactoryPostData>) => createQueryKey('switchFactoryContextApiV1SessionSwitchFactoryPost', options);

/**
 * Switch Factory Context
 * Switch user's factory context.
 */
export const switchFactoryContextApiV1SessionSwitchFactoryPostOptions = (options: Options<SwitchFactoryContextApiV1SessionSwitchFactoryPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await switchFactoryContextApiV1SessionSwitchFactoryPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: switchFactoryContextApiV1SessionSwitchFactoryPostQueryKey(options)
    });
};

/**
 * Switch Factory Context
 * Switch user's factory context.
 */
export const switchFactoryContextApiV1SessionSwitchFactoryPostMutation = (options?: Partial<Options<SwitchFactoryContextApiV1SessionSwitchFactoryPostData>>): UseMutationOptions<SwitchFactoryContextApiV1SessionSwitchFactoryPostResponse, AxiosError<SwitchFactoryContextApiV1SessionSwitchFactoryPostError>, Options<SwitchFactoryContextApiV1SessionSwitchFactoryPostData>> => {
    const mutationOptions: UseMutationOptions<SwitchFactoryContextApiV1SessionSwitchFactoryPostResponse, AxiosError<SwitchFactoryContextApiV1SessionSwitchFactoryPostError>, Options<SwitchFactoryContextApiV1SessionSwitchFactoryPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await switchFactoryContextApiV1SessionSwitchFactoryPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const refreshFactoryContextApiV1SessionRefreshContextPostQueryKey = (options?: Options<RefreshFactoryContextApiV1SessionRefreshContextPostData>) => createQueryKey('refreshFactoryContextApiV1SessionRefreshContextPost', options);

/**
 * Refresh Factory Context
 * Refresh factory context with latest data.
 */
export const refreshFactoryContextApiV1SessionRefreshContextPostOptions = (options?: Options<RefreshFactoryContextApiV1SessionRefreshContextPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await refreshFactoryContextApiV1SessionRefreshContextPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: refreshFactoryContextApiV1SessionRefreshContextPostQueryKey(options)
    });
};

/**
 * Refresh Factory Context
 * Refresh factory context with latest data.
 */
export const refreshFactoryContextApiV1SessionRefreshContextPostMutation = (options?: Partial<Options<RefreshFactoryContextApiV1SessionRefreshContextPostData>>): UseMutationOptions<RefreshFactoryContextApiV1SessionRefreshContextPostResponse, AxiosError<DefaultError>, Options<RefreshFactoryContextApiV1SessionRefreshContextPostData>> => {
    const mutationOptions: UseMutationOptions<RefreshFactoryContextApiV1SessionRefreshContextPostResponse, AxiosError<DefaultError>, Options<RefreshFactoryContextApiV1SessionRefreshContextPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await refreshFactoryContextApiV1SessionRefreshContextPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const extendSessionApiV1SessionExtendPostQueryKey = (options?: Options<ExtendSessionApiV1SessionExtendPostData>) => createQueryKey('extendSessionApiV1SessionExtendPost', options);

/**
 * Extend Session
 * Extend session expiration.
 */
export const extendSessionApiV1SessionExtendPostOptions = (options?: Options<ExtendSessionApiV1SessionExtendPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await extendSessionApiV1SessionExtendPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: extendSessionApiV1SessionExtendPostQueryKey(options)
    });
};

/**
 * Extend Session
 * Extend session expiration.
 */
export const extendSessionApiV1SessionExtendPostMutation = (options?: Partial<Options<ExtendSessionApiV1SessionExtendPostData>>): UseMutationOptions<unknown, AxiosError<DefaultError>, Options<ExtendSessionApiV1SessionExtendPostData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<ExtendSessionApiV1SessionExtendPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await extendSessionApiV1SessionExtendPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Logout
 * Logout and destroy session.
 */
export const logoutApiV1SessionLogoutDeleteMutation = (options?: Partial<Options<LogoutApiV1SessionLogoutDeleteData>>): UseMutationOptions<unknown, AxiosError<DefaultError>, Options<LogoutApiV1SessionLogoutDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<LogoutApiV1SessionLogoutDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await logoutApiV1SessionLogoutDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getPermissionTreeApiV1PermissionsTreeGetQueryKey = (options?: Options<GetPermissionTreeApiV1PermissionsTreeGetData>) => createQueryKey('getPermissionTreeApiV1PermissionsTreeGet', options);

/**
 * Get Permission Tree
 * Get hierarchical permission tree.
 */
export const getPermissionTreeApiV1PermissionsTreeGetOptions = (options?: Options<GetPermissionTreeApiV1PermissionsTreeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getPermissionTreeApiV1PermissionsTreeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getPermissionTreeApiV1PermissionsTreeGetQueryKey(options)
    });
};

export const getAllPermissionsApiV1PermissionsListGetQueryKey = (options?: Options<GetAllPermissionsApiV1PermissionsListGetData>) => createQueryKey('getAllPermissionsApiV1PermissionsListGet', options);

/**
 * Get All Permissions
 * Get all permissions as a flat list.
 */
export const getAllPermissionsApiV1PermissionsListGetOptions = (options?: Options<GetAllPermissionsApiV1PermissionsListGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAllPermissionsApiV1PermissionsListGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAllPermissionsApiV1PermissionsListGetQueryKey(options)
    });
};

export const getAllRolesApiV1RolesGetQueryKey = (options?: Options<GetAllRolesApiV1RolesGetData>) => createQueryKey('getAllRolesApiV1RolesGet', options);

/**
 * Get All Roles
 * Get all roles for the current factory.
 */
export const getAllRolesApiV1RolesGetOptions = (options?: Options<GetAllRolesApiV1RolesGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAllRolesApiV1RolesGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAllRolesApiV1RolesGetQueryKey(options)
    });
};

export const createRoleApiV1RolesPostQueryKey = (options: Options<CreateRoleApiV1RolesPostData>) => createQueryKey('createRoleApiV1RolesPost', options);

/**
 * Create Role
 * Create a new role for the current factory.
 */
export const createRoleApiV1RolesPostOptions = (options: Options<CreateRoleApiV1RolesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createRoleApiV1RolesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createRoleApiV1RolesPostQueryKey(options)
    });
};

/**
 * Create Role
 * Create a new role for the current factory.
 */
export const createRoleApiV1RolesPostMutation = (options?: Partial<Options<CreateRoleApiV1RolesPostData>>): UseMutationOptions<CreateRoleApiV1RolesPostResponse, AxiosError<CreateRoleApiV1RolesPostError>, Options<CreateRoleApiV1RolesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateRoleApiV1RolesPostResponse, AxiosError<CreateRoleApiV1RolesPostError>, Options<CreateRoleApiV1RolesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createRoleApiV1RolesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getActiveRolesApiV1RolesActiveGetQueryKey = (options?: Options<GetActiveRolesApiV1RolesActiveGetData>) => createQueryKey('getActiveRolesApiV1RolesActiveGet', options);

/**
 * Get Active Roles
 * Get all active roles for the current factory.
 */
export const getActiveRolesApiV1RolesActiveGetOptions = (options?: Options<GetActiveRolesApiV1RolesActiveGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getActiveRolesApiV1RolesActiveGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getActiveRolesApiV1RolesActiveGetQueryKey(options)
    });
};

export const getRolesSummaryApiV1RolesSummaryGetQueryKey = (options?: Options<GetRolesSummaryApiV1RolesSummaryGetData>) => createQueryKey('getRolesSummaryApiV1RolesSummaryGet', options);

/**
 * Get Roles Summary
 * Get summary of all roles for the current factory without full permission details.
 */
export const getRolesSummaryApiV1RolesSummaryGetOptions = (options?: Options<GetRolesSummaryApiV1RolesSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getRolesSummaryApiV1RolesSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getRolesSummaryApiV1RolesSummaryGetQueryKey(options)
    });
};

/**
 * Delete Role
 * Delete role.
 */
export const deleteRoleApiV1RolesRoleIdDeleteMutation = (options?: Partial<Options<DeleteRoleApiV1RolesRoleIdDeleteData>>): UseMutationOptions<DeleteRoleApiV1RolesRoleIdDeleteResponse, AxiosError<DeleteRoleApiV1RolesRoleIdDeleteError>, Options<DeleteRoleApiV1RolesRoleIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<DeleteRoleApiV1RolesRoleIdDeleteResponse, AxiosError<DeleteRoleApiV1RolesRoleIdDeleteError>, Options<DeleteRoleApiV1RolesRoleIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteRoleApiV1RolesRoleIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getRoleByIdApiV1RolesRoleIdGetQueryKey = (options: Options<GetRoleByIdApiV1RolesRoleIdGetData>) => createQueryKey('getRoleByIdApiV1RolesRoleIdGet', options);

/**
 * Get Role By Id
 * Get role by ID (factory-scoped).
 */
export const getRoleByIdApiV1RolesRoleIdGetOptions = (options: Options<GetRoleByIdApiV1RolesRoleIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getRoleByIdApiV1RolesRoleIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getRoleByIdApiV1RolesRoleIdGetQueryKey(options)
    });
};

/**
 * Update Role
 * Update role.
 */
export const updateRoleApiV1RolesRoleIdPutMutation = (options?: Partial<Options<UpdateRoleApiV1RolesRoleIdPutData>>): UseMutationOptions<UpdateRoleApiV1RolesRoleIdPutResponse, AxiosError<UpdateRoleApiV1RolesRoleIdPutError>, Options<UpdateRoleApiV1RolesRoleIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateRoleApiV1RolesRoleIdPutResponse, AxiosError<UpdateRoleApiV1RolesRoleIdPutError>, Options<UpdateRoleApiV1RolesRoleIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateRoleApiV1RolesRoleIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Remove Permissions From Role
 * Remove permissions from role.
 */
export const removePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteMutation = (options?: Partial<Options<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData>>): UseMutationOptions<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponse, AxiosError<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteError>, Options<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData>> => {
    const mutationOptions: UseMutationOptions<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponse, AxiosError<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteError>, Options<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await removePermissionsFromRoleApiV1RolesRoleIdPermissionsDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getRolePermissionsApiV1RolesRoleIdPermissionsGetQueryKey = (options: Options<GetRolePermissionsApiV1RolesRoleIdPermissionsGetData>) => createQueryKey('getRolePermissionsApiV1RolesRoleIdPermissionsGet', options);

/**
 * Get Role Permissions
 * Get permissions assigned to a role.
 */
export const getRolePermissionsApiV1RolesRoleIdPermissionsGetOptions = (options: Options<GetRolePermissionsApiV1RolesRoleIdPermissionsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getRolePermissionsApiV1RolesRoleIdPermissionsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getRolePermissionsApiV1RolesRoleIdPermissionsGetQueryKey(options)
    });
};

export const assignPermissionsToRoleApiV1RolesRoleIdPermissionsPostQueryKey = (options: Options<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData>) => createQueryKey('assignPermissionsToRoleApiV1RolesRoleIdPermissionsPost', options);

/**
 * Assign Permissions To Role
 * Assign permissions to role.
 */
export const assignPermissionsToRoleApiV1RolesRoleIdPermissionsPostOptions = (options: Options<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await assignPermissionsToRoleApiV1RolesRoleIdPermissionsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: assignPermissionsToRoleApiV1RolesRoleIdPermissionsPostQueryKey(options)
    });
};

/**
 * Assign Permissions To Role
 * Assign permissions to role.
 */
export const assignPermissionsToRoleApiV1RolesRoleIdPermissionsPostMutation = (options?: Partial<Options<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData>>): UseMutationOptions<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponse, AxiosError<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostError>, Options<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData>> => {
    const mutationOptions: UseMutationOptions<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponse, AxiosError<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostError>, Options<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await assignPermissionsToRoleApiV1RolesRoleIdPermissionsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAvailableUsersApiV1UserManagementAvailableUsersGetQueryKey = (options?: Options<GetAvailableUsersApiV1UserManagementAvailableUsersGetData>) => createQueryKey('getAvailableUsersApiV1UserManagementAvailableUsersGet', options);

/**
 * Get Available Users
 * Get list of users available to add to the factory.
 */
export const getAvailableUsersApiV1UserManagementAvailableUsersGetOptions = (options?: Options<GetAvailableUsersApiV1UserManagementAvailableUsersGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAvailableUsersApiV1UserManagementAvailableUsersGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAvailableUsersApiV1UserManagementAvailableUsersGetQueryKey(options)
    });
};

export const addUsersToFactoryApiV1UserManagementAddUsersPostQueryKey = (options: Options<AddUsersToFactoryApiV1UserManagementAddUsersPostData>) => createQueryKey('addUsersToFactoryApiV1UserManagementAddUsersPost', options);

/**
 * Add Users To Factory
 * Add multiple users to the current factory.
 */
export const addUsersToFactoryApiV1UserManagementAddUsersPostOptions = (options: Options<AddUsersToFactoryApiV1UserManagementAddUsersPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await addUsersToFactoryApiV1UserManagementAddUsersPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: addUsersToFactoryApiV1UserManagementAddUsersPostQueryKey(options)
    });
};

/**
 * Add Users To Factory
 * Add multiple users to the current factory.
 */
export const addUsersToFactoryApiV1UserManagementAddUsersPostMutation = (options?: Partial<Options<AddUsersToFactoryApiV1UserManagementAddUsersPostData>>): UseMutationOptions<AddUsersToFactoryApiV1UserManagementAddUsersPostResponse, AxiosError<AddUsersToFactoryApiV1UserManagementAddUsersPostError>, Options<AddUsersToFactoryApiV1UserManagementAddUsersPostData>> => {
    const mutationOptions: UseMutationOptions<AddUsersToFactoryApiV1UserManagementAddUsersPostResponse, AxiosError<AddUsersToFactoryApiV1UserManagementAddUsersPostError>, Options<AddUsersToFactoryApiV1UserManagementAddUsersPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await addUsersToFactoryApiV1UserManagementAddUsersPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const bindUserRoleApiV1UserManagementBindRolesPostQueryKey = (options: Options<BindUserRoleApiV1UserManagementBindRolesPostData>) => createQueryKey('bindUserRoleApiV1UserManagementBindRolesPost', options);

/**
 * Bind User Role
 * Bind system role to a user.
 */
export const bindUserRoleApiV1UserManagementBindRolesPostOptions = (options: Options<BindUserRoleApiV1UserManagementBindRolesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await bindUserRoleApiV1UserManagementBindRolesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: bindUserRoleApiV1UserManagementBindRolesPostQueryKey(options)
    });
};

/**
 * Bind User Role
 * Bind system role to a user.
 */
export const bindUserRoleApiV1UserManagementBindRolesPostMutation = (options?: Partial<Options<BindUserRoleApiV1UserManagementBindRolesPostData>>): UseMutationOptions<BindUserRoleApiV1UserManagementBindRolesPostResponse, AxiosError<BindUserRoleApiV1UserManagementBindRolesPostError>, Options<BindUserRoleApiV1UserManagementBindRolesPostData>> => {
    const mutationOptions: UseMutationOptions<BindUserRoleApiV1UserManagementBindRolesPostResponse, AxiosError<BindUserRoleApiV1UserManagementBindRolesPostError>, Options<BindUserRoleApiV1UserManagementBindRolesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await bindUserRoleApiV1UserManagementBindRolesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const suspendUserInFactoryApiV1UserManagementSuspendPostQueryKey = (options: Options<SuspendUserInFactoryApiV1UserManagementSuspendPostData>) => createQueryKey('suspendUserInFactoryApiV1UserManagementSuspendPost', options);

/**
 * Suspend User In Factory
 * Suspend a user in the current factory.
 */
export const suspendUserInFactoryApiV1UserManagementSuspendPostOptions = (options: Options<SuspendUserInFactoryApiV1UserManagementSuspendPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await suspendUserInFactoryApiV1UserManagementSuspendPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: suspendUserInFactoryApiV1UserManagementSuspendPostQueryKey(options)
    });
};

/**
 * Suspend User In Factory
 * Suspend a user in the current factory.
 */
export const suspendUserInFactoryApiV1UserManagementSuspendPostMutation = (options?: Partial<Options<SuspendUserInFactoryApiV1UserManagementSuspendPostData>>): UseMutationOptions<SuspendUserInFactoryApiV1UserManagementSuspendPostResponse, AxiosError<SuspendUserInFactoryApiV1UserManagementSuspendPostError>, Options<SuspendUserInFactoryApiV1UserManagementSuspendPostData>> => {
    const mutationOptions: UseMutationOptions<SuspendUserInFactoryApiV1UserManagementSuspendPostResponse, AxiosError<SuspendUserInFactoryApiV1UserManagementSuspendPostError>, Options<SuspendUserInFactoryApiV1UserManagementSuspendPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await suspendUserInFactoryApiV1UserManagementSuspendPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Remove User From Factory
 * Remove a user from the current factory.
 */
export const removeUserFromFactoryApiV1UserManagementRemoveUserDeleteMutation = (options?: Partial<Options<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData>>): UseMutationOptions<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponse, AxiosError<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteError>, Options<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData>> => {
    const mutationOptions: UseMutationOptions<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponse, AxiosError<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteError>, Options<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await removeUserFromFactoryApiV1UserManagementRemoveUserDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createUserWithFactoryApiV1UserManagementCreateUserPostQueryKey = (options: Options<CreateUserWithFactoryApiV1UserManagementCreateUserPostData>) => createQueryKey('createUserWithFactoryApiV1UserManagementCreateUserPost', options);

/**
 * Create User With Factory
 * Create new user or add existing user to factory with skills and complete information.
 */
export const createUserWithFactoryApiV1UserManagementCreateUserPostOptions = (options: Options<CreateUserWithFactoryApiV1UserManagementCreateUserPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createUserWithFactoryApiV1UserManagementCreateUserPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createUserWithFactoryApiV1UserManagementCreateUserPostQueryKey(options)
    });
};

/**
 * Create User With Factory
 * Create new user or add existing user to factory with skills and complete information.
 */
export const createUserWithFactoryApiV1UserManagementCreateUserPostMutation = (options?: Partial<Options<CreateUserWithFactoryApiV1UserManagementCreateUserPostData>>): UseMutationOptions<CreateUserWithFactoryApiV1UserManagementCreateUserPostResponse, AxiosError<CreateUserWithFactoryApiV1UserManagementCreateUserPostError>, Options<CreateUserWithFactoryApiV1UserManagementCreateUserPostData>> => {
    const mutationOptions: UseMutationOptions<CreateUserWithFactoryApiV1UserManagementCreateUserPostResponse, AxiosError<CreateUserWithFactoryApiV1UserManagementCreateUserPostError>, Options<CreateUserWithFactoryApiV1UserManagementCreateUserPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createUserWithFactoryApiV1UserManagementCreateUserPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAllSkillsApiV1SkillsGetQueryKey = (options?: Options<GetAllSkillsApiV1SkillsGetData>) => createQueryKey('getAllSkillsApiV1SkillsGet', options);

/**
 * Get All Skills
 * Get all skills with optional filters.
 */
export const getAllSkillsApiV1SkillsGetOptions = (options?: Options<GetAllSkillsApiV1SkillsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAllSkillsApiV1SkillsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAllSkillsApiV1SkillsGetQueryKey(options)
    });
};

export const createSkillApiV1SkillsPostQueryKey = (options: Options<CreateSkillApiV1SkillsPostData>) => createQueryKey('createSkillApiV1SkillsPost', options);

/**
 * Create Skill
 * Create a new skill.
 */
export const createSkillApiV1SkillsPostOptions = (options: Options<CreateSkillApiV1SkillsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createSkillApiV1SkillsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createSkillApiV1SkillsPostQueryKey(options)
    });
};

/**
 * Create Skill
 * Create a new skill.
 */
export const createSkillApiV1SkillsPostMutation = (options?: Partial<Options<CreateSkillApiV1SkillsPostData>>): UseMutationOptions<CreateSkillApiV1SkillsPostResponse, AxiosError<CreateSkillApiV1SkillsPostError>, Options<CreateSkillApiV1SkillsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateSkillApiV1SkillsPostResponse, AxiosError<CreateSkillApiV1SkillsPostError>, Options<CreateSkillApiV1SkillsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createSkillApiV1SkillsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getActiveSkillsApiV1SkillsActiveGetQueryKey = (options?: Options<GetActiveSkillsApiV1SkillsActiveGetData>) => createQueryKey('getActiveSkillsApiV1SkillsActiveGet', options);

/**
 * Get Active Skills
 * Get all active skills.
 */
export const getActiveSkillsApiV1SkillsActiveGetOptions = (options?: Options<GetActiveSkillsApiV1SkillsActiveGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getActiveSkillsApiV1SkillsActiveGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getActiveSkillsApiV1SkillsActiveGetQueryKey(options)
    });
};

/**
 * Delete Skill
 * Delete skill.
 */
export const deleteSkillApiV1SkillsSkillIdDeleteMutation = (options?: Partial<Options<DeleteSkillApiV1SkillsSkillIdDeleteData>>): UseMutationOptions<DeleteSkillApiV1SkillsSkillIdDeleteResponse, AxiosError<DeleteSkillApiV1SkillsSkillIdDeleteError>, Options<DeleteSkillApiV1SkillsSkillIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<DeleteSkillApiV1SkillsSkillIdDeleteResponse, AxiosError<DeleteSkillApiV1SkillsSkillIdDeleteError>, Options<DeleteSkillApiV1SkillsSkillIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteSkillApiV1SkillsSkillIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getSkillByIdApiV1SkillsSkillIdGetQueryKey = (options: Options<GetSkillByIdApiV1SkillsSkillIdGetData>) => createQueryKey('getSkillByIdApiV1SkillsSkillIdGet', options);

/**
 * Get Skill By Id
 * Get skill by ID.
 */
export const getSkillByIdApiV1SkillsSkillIdGetOptions = (options: Options<GetSkillByIdApiV1SkillsSkillIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getSkillByIdApiV1SkillsSkillIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getSkillByIdApiV1SkillsSkillIdGetQueryKey(options)
    });
};

/**
 * Update Skill
 * Update skill.
 */
export const updateSkillApiV1SkillsSkillIdPutMutation = (options?: Partial<Options<UpdateSkillApiV1SkillsSkillIdPutData>>): UseMutationOptions<UpdateSkillApiV1SkillsSkillIdPutResponse, AxiosError<UpdateSkillApiV1SkillsSkillIdPutError>, Options<UpdateSkillApiV1SkillsSkillIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateSkillApiV1SkillsSkillIdPutResponse, AxiosError<UpdateSkillApiV1SkillsSkillIdPutError>, Options<UpdateSkillApiV1SkillsSkillIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateSkillApiV1SkillsSkillIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getUserSkillsApiV1SkillsUserUserIdSkillsGetQueryKey = (options: Options<GetUserSkillsApiV1SkillsUserUserIdSkillsGetData>) => createQueryKey('getUserSkillsApiV1SkillsUserUserIdSkillsGet', options);

/**
 * Get User Skills
 * Get user's skills in current factory.
 */
export const getUserSkillsApiV1SkillsUserUserIdSkillsGetOptions = (options: Options<GetUserSkillsApiV1SkillsUserUserIdSkillsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUserSkillsApiV1SkillsUserUserIdSkillsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUserSkillsApiV1SkillsUserUserIdSkillsGetQueryKey(options)
    });
};

export const assignSkillsToUserApiV1SkillsAssignPostQueryKey = (options: Options<AssignSkillsToUserApiV1SkillsAssignPostData>) => createQueryKey('assignSkillsToUserApiV1SkillsAssignPost', options);

/**
 * Assign Skills To User
 * Assign skills to user in current factory.
 */
export const assignSkillsToUserApiV1SkillsAssignPostOptions = (options: Options<AssignSkillsToUserApiV1SkillsAssignPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await assignSkillsToUserApiV1SkillsAssignPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: assignSkillsToUserApiV1SkillsAssignPostQueryKey(options)
    });
};

/**
 * Assign Skills To User
 * Assign skills to user in current factory.
 */
export const assignSkillsToUserApiV1SkillsAssignPostMutation = (options?: Partial<Options<AssignSkillsToUserApiV1SkillsAssignPostData>>): UseMutationOptions<AssignSkillsToUserApiV1SkillsAssignPostResponse, AxiosError<AssignSkillsToUserApiV1SkillsAssignPostError>, Options<AssignSkillsToUserApiV1SkillsAssignPostData>> => {
    const mutationOptions: UseMutationOptions<AssignSkillsToUserApiV1SkillsAssignPostResponse, AxiosError<AssignSkillsToUserApiV1SkillsAssignPostError>, Options<AssignSkillsToUserApiV1SkillsAssignPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await assignSkillsToUserApiV1SkillsAssignPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Modify Skill Proficiency
 * Modify user's skill proficiency level.
 */
export const modifySkillProficiencyApiV1SkillsModifyProficiencyPutMutation = (options?: Partial<Options<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData>>): UseMutationOptions<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponse, AxiosError<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutError>, Options<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData>> => {
    const mutationOptions: UseMutationOptions<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponse, AxiosError<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutError>, Options<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await modifySkillProficiencyApiV1SkillsModifyProficiencyPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const certifyUserSkillApiV1SkillsCertifyPostQueryKey = (options: Options<CertifyUserSkillApiV1SkillsCertifyPostData>) => createQueryKey('certifyUserSkillApiV1SkillsCertifyPost', options);

/**
 * Certify User Skill
 * Certify user in skill.
 */
export const certifyUserSkillApiV1SkillsCertifyPostOptions = (options: Options<CertifyUserSkillApiV1SkillsCertifyPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await certifyUserSkillApiV1SkillsCertifyPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: certifyUserSkillApiV1SkillsCertifyPostQueryKey(options)
    });
};

/**
 * Certify User Skill
 * Certify user in skill.
 */
export const certifyUserSkillApiV1SkillsCertifyPostMutation = (options?: Partial<Options<CertifyUserSkillApiV1SkillsCertifyPostData>>): UseMutationOptions<CertifyUserSkillApiV1SkillsCertifyPostResponse, AxiosError<CertifyUserSkillApiV1SkillsCertifyPostError>, Options<CertifyUserSkillApiV1SkillsCertifyPostData>> => {
    const mutationOptions: UseMutationOptions<CertifyUserSkillApiV1SkillsCertifyPostResponse, AxiosError<CertifyUserSkillApiV1SkillsCertifyPostError>, Options<CertifyUserSkillApiV1SkillsCertifyPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await certifyUserSkillApiV1SkillsCertifyPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Remove User Skill
 * Remove skill from user.
 */
export const removeUserSkillApiV1SkillsRemoveDeleteMutation = (options?: Partial<Options<RemoveUserSkillApiV1SkillsRemoveDeleteData>>): UseMutationOptions<RemoveUserSkillApiV1SkillsRemoveDeleteResponse, AxiosError<RemoveUserSkillApiV1SkillsRemoveDeleteError>, Options<RemoveUserSkillApiV1SkillsRemoveDeleteData>> => {
    const mutationOptions: UseMutationOptions<RemoveUserSkillApiV1SkillsRemoveDeleteResponse, AxiosError<RemoveUserSkillApiV1SkillsRemoveDeleteError>, Options<RemoveUserSkillApiV1SkillsRemoveDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await removeUserSkillApiV1SkillsRemoveDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAllCraftsApiV1CraftsGetQueryKey = (options?: Options<GetAllCraftsApiV1CraftsGetData>) => createQueryKey('getAllCraftsApiV1CraftsGet', options);

/**
 * Get All Crafts
 * Get all crafts with optional filtering.
 */
export const getAllCraftsApiV1CraftsGetOptions = (options?: Options<GetAllCraftsApiV1CraftsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAllCraftsApiV1CraftsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAllCraftsApiV1CraftsGetQueryKey(options)
    });
};

export const createCraftApiV1CraftsPostQueryKey = (options: Options<CreateCraftApiV1CraftsPostData>) => createQueryKey('createCraftApiV1CraftsPost', options);

/**
 * Create Craft
 * Create a new craft (admin only).
 */
export const createCraftApiV1CraftsPostOptions = (options: Options<CreateCraftApiV1CraftsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createCraftApiV1CraftsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createCraftApiV1CraftsPostQueryKey(options)
    });
};

/**
 * Create Craft
 * Create a new craft (admin only).
 */
export const createCraftApiV1CraftsPostMutation = (options?: Partial<Options<CreateCraftApiV1CraftsPostData>>): UseMutationOptions<CreateCraftApiV1CraftsPostResponse, AxiosError<CreateCraftApiV1CraftsPostError>, Options<CreateCraftApiV1CraftsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateCraftApiV1CraftsPostResponse, AxiosError<CreateCraftApiV1CraftsPostError>, Options<CreateCraftApiV1CraftsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createCraftApiV1CraftsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getEnabledCraftsApiV1CraftsEnabledGetQueryKey = (options?: Options<GetEnabledCraftsApiV1CraftsEnabledGetData>) => createQueryKey('getEnabledCraftsApiV1CraftsEnabledGet', options);

/**
 * Get Enabled Crafts
 * Get all enabled crafts.
 */
export const getEnabledCraftsApiV1CraftsEnabledGetOptions = (options?: Options<GetEnabledCraftsApiV1CraftsEnabledGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEnabledCraftsApiV1CraftsEnabledGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEnabledCraftsApiV1CraftsEnabledGetQueryKey(options)
    });
};

/**
 * Delete Craft
 * Delete craft (admin only).
 */
export const deleteCraftApiV1CraftsCraftIdDeleteMutation = (options?: Partial<Options<DeleteCraftApiV1CraftsCraftIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteCraftApiV1CraftsCraftIdDeleteError>, Options<DeleteCraftApiV1CraftsCraftIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteCraftApiV1CraftsCraftIdDeleteError>, Options<DeleteCraftApiV1CraftsCraftIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteCraftApiV1CraftsCraftIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getCraftByIdApiV1CraftsCraftIdGetQueryKey = (options: Options<GetCraftByIdApiV1CraftsCraftIdGetData>) => createQueryKey('getCraftByIdApiV1CraftsCraftIdGet', options);

/**
 * Get Craft By Id
 * Get craft by ID.
 */
export const getCraftByIdApiV1CraftsCraftIdGetOptions = (options: Options<GetCraftByIdApiV1CraftsCraftIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCraftByIdApiV1CraftsCraftIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCraftByIdApiV1CraftsCraftIdGetQueryKey(options)
    });
};

/**
 * Update Craft
 * Update craft (admin only).
 */
export const updateCraftApiV1CraftsCraftIdPutMutation = (options?: Partial<Options<UpdateCraftApiV1CraftsCraftIdPutData>>): UseMutationOptions<UpdateCraftApiV1CraftsCraftIdPutResponse, AxiosError<UpdateCraftApiV1CraftsCraftIdPutError>, Options<UpdateCraftApiV1CraftsCraftIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateCraftApiV1CraftsCraftIdPutResponse, AxiosError<UpdateCraftApiV1CraftsCraftIdPutError>, Options<UpdateCraftApiV1CraftsCraftIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCraftApiV1CraftsCraftIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getCraftByCodeApiV1CraftsCodeCraftCodeGetQueryKey = (options: Options<GetCraftByCodeApiV1CraftsCodeCraftCodeGetData>) => createQueryKey('getCraftByCodeApiV1CraftsCodeCraftCodeGet', options);

/**
 * Get Craft By Code
 * Get craft by code with its routes.
 */
export const getCraftByCodeApiV1CraftsCodeCraftCodeGetOptions = (options: Options<GetCraftByCodeApiV1CraftsCodeCraftCodeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCraftByCodeApiV1CraftsCodeCraftCodeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCraftByCodeApiV1CraftsCodeCraftCodeGetQueryKey(options)
    });
};

export const createCraftRouteApiV1CraftsRoutesPostQueryKey = (options: Options<CreateCraftRouteApiV1CraftsRoutesPostData>) => createQueryKey('createCraftRouteApiV1CraftsRoutesPost', options);

/**
 * Create Craft Route
 * Create a new craft route.
 */
export const createCraftRouteApiV1CraftsRoutesPostOptions = (options: Options<CreateCraftRouteApiV1CraftsRoutesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createCraftRouteApiV1CraftsRoutesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createCraftRouteApiV1CraftsRoutesPostQueryKey(options)
    });
};

/**
 * Create Craft Route
 * Create a new craft route.
 */
export const createCraftRouteApiV1CraftsRoutesPostMutation = (options?: Partial<Options<CreateCraftRouteApiV1CraftsRoutesPostData>>): UseMutationOptions<CreateCraftRouteApiV1CraftsRoutesPostResponse, AxiosError<CreateCraftRouteApiV1CraftsRoutesPostError>, Options<CreateCraftRouteApiV1CraftsRoutesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateCraftRouteApiV1CraftsRoutesPostResponse, AxiosError<CreateCraftRouteApiV1CraftsRoutesPostError>, Options<CreateCraftRouteApiV1CraftsRoutesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createCraftRouteApiV1CraftsRoutesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createBulkCraftRoutesApiV1CraftsRoutesBulkPostQueryKey = (options: Options<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData>) => createQueryKey('createBulkCraftRoutesApiV1CraftsRoutesBulkPost', options);

/**
 * Create Bulk Craft Routes
 * Create multiple craft routes at once.
 */
export const createBulkCraftRoutesApiV1CraftsRoutesBulkPostOptions = (options: Options<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createBulkCraftRoutesApiV1CraftsRoutesBulkPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createBulkCraftRoutesApiV1CraftsRoutesBulkPostQueryKey(options)
    });
};

/**
 * Create Bulk Craft Routes
 * Create multiple craft routes at once.
 */
export const createBulkCraftRoutesApiV1CraftsRoutesBulkPostMutation = (options?: Partial<Options<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData>>): UseMutationOptions<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponse, AxiosError<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostError>, Options<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData>> => {
    const mutationOptions: UseMutationOptions<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponse, AxiosError<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostError>, Options<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createBulkCraftRoutesApiV1CraftsRoutesBulkPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetQueryKey = (options: Options<GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetData>) => createQueryKey('getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet', options);

/**
 * Get Craft Routes
 * Get all routes for a specific craft.
 */
export const getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetOptions = (options: Options<GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetQueryKey(options)
    });
};

export const getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetQueryKey = (options: Options<GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetData>) => createQueryKey('getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGet', options);

/**
 * Get Skill Routes
 * Get all routes that use a specific skill.
 */
export const getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetOptions = (options: Options<GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetQueryKey(options)
    });
};

/**
 * Delete Craft Route
 * Delete craft route.
 */
export const deleteCraftRouteApiV1CraftsRoutesRouteIdDeleteMutation = (options?: Partial<Options<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteError>, Options<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteError>, Options<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteCraftRouteApiV1CraftsRoutesRouteIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Craft Route
 * Update craft route.
 */
export const updateCraftRouteApiV1CraftsRoutesRouteIdPutMutation = (options?: Partial<Options<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData>>): UseMutationOptions<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponse, AxiosError<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutError>, Options<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponse, AxiosError<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutError>, Options<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCraftRouteApiV1CraftsRoutesRouteIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostQueryKey = (options: Options<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData>) => createQueryKey('reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePost', options);

/**
 * Reorder Craft Routes
 * Reorder routes for a craft.
 */
export const reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostOptions = (options: Options<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostQueryKey(options)
    });
};

/**
 * Reorder Craft Routes
 * Reorder routes for a craft.
 */
export const reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostMutation = (options?: Partial<Options<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData>>): UseMutationOptions<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponse, AxiosError<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostError>, Options<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData>> => {
    const mutationOptions: UseMutationOptions<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponse, AxiosError<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostError>, Options<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDepartmentsApiV1DepartmentsGetQueryKey = (options?: Options<GetDepartmentsApiV1DepartmentsGetData>) => createQueryKey('getDepartmentsApiV1DepartmentsGet', options);

/**
 * Get Departments
 * Get departments for current factory.
 */
export const getDepartmentsApiV1DepartmentsGetOptions = (options?: Options<GetDepartmentsApiV1DepartmentsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDepartmentsApiV1DepartmentsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDepartmentsApiV1DepartmentsGetQueryKey(options)
    });
};

export const createDepartmentApiV1DepartmentsPostQueryKey = (options: Options<CreateDepartmentApiV1DepartmentsPostData>) => createQueryKey('createDepartmentApiV1DepartmentsPost', options);

/**
 * Create Department
 * Create a new department.
 */
export const createDepartmentApiV1DepartmentsPostOptions = (options: Options<CreateDepartmentApiV1DepartmentsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createDepartmentApiV1DepartmentsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createDepartmentApiV1DepartmentsPostQueryKey(options)
    });
};

/**
 * Create Department
 * Create a new department.
 */
export const createDepartmentApiV1DepartmentsPostMutation = (options?: Partial<Options<CreateDepartmentApiV1DepartmentsPostData>>): UseMutationOptions<CreateDepartmentApiV1DepartmentsPostResponse, AxiosError<CreateDepartmentApiV1DepartmentsPostError>, Options<CreateDepartmentApiV1DepartmentsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateDepartmentApiV1DepartmentsPostResponse, AxiosError<CreateDepartmentApiV1DepartmentsPostError>, Options<CreateDepartmentApiV1DepartmentsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createDepartmentApiV1DepartmentsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const searchDepartmentsApiV1DepartmentsSearchGetQueryKey = (options?: Options<SearchDepartmentsApiV1DepartmentsSearchGetData>) => createQueryKey('searchDepartmentsApiV1DepartmentsSearchGet', options);

/**
 * Search Departments
 * Search departments with optional filtering.
 */
export const searchDepartmentsApiV1DepartmentsSearchGetOptions = (options?: Options<SearchDepartmentsApiV1DepartmentsSearchGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await searchDepartmentsApiV1DepartmentsSearchGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchDepartmentsApiV1DepartmentsSearchGetQueryKey(options)
    });
};

/**
 * Delete Department
 * Delete a department.
 */
export const deleteDepartmentApiV1DepartmentsDepartmentIdDeleteMutation = (options?: Partial<Options<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteData>>): UseMutationOptions<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponse, AxiosError<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteError>, Options<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponse, AxiosError<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteError>, Options<DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteDepartmentApiV1DepartmentsDepartmentIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDepartmentByIdApiV1DepartmentsDepartmentIdGetQueryKey = (options: Options<GetDepartmentByIdApiV1DepartmentsDepartmentIdGetData>) => createQueryKey('getDepartmentByIdApiV1DepartmentsDepartmentIdGet', options);

/**
 * Get Department By Id
 * Get department by ID.
 */
export const getDepartmentByIdApiV1DepartmentsDepartmentIdGetOptions = (options: Options<GetDepartmentByIdApiV1DepartmentsDepartmentIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDepartmentByIdApiV1DepartmentsDepartmentIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDepartmentByIdApiV1DepartmentsDepartmentIdGetQueryKey(options)
    });
};

/**
 * Update Department
 * Update a department.
 */
export const updateDepartmentApiV1DepartmentsDepartmentIdPutMutation = (options?: Partial<Options<UpdateDepartmentApiV1DepartmentsDepartmentIdPutData>>): UseMutationOptions<UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponse, AxiosError<UpdateDepartmentApiV1DepartmentsDepartmentIdPutError>, Options<UpdateDepartmentApiV1DepartmentsDepartmentIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponse, AxiosError<UpdateDepartmentApiV1DepartmentsDepartmentIdPutError>, Options<UpdateDepartmentApiV1DepartmentsDepartmentIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateDepartmentApiV1DepartmentsDepartmentIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAllOrdersApiV1OrdersGetQueryKey = (options?: Options<GetAllOrdersApiV1OrdersGetData>) => createQueryKey('getAllOrdersApiV1OrdersGet', options);

/**
 * Get All Orders
 * Get all orders with optional filtering.
 */
export const getAllOrdersApiV1OrdersGetOptions = (options?: Options<GetAllOrdersApiV1OrdersGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAllOrdersApiV1OrdersGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAllOrdersApiV1OrdersGetQueryKey(options)
    });
};

export const createOrderApiV1OrdersPostQueryKey = (options: Options<CreateOrderApiV1OrdersPostData>) => createQueryKey('createOrderApiV1OrdersPost', options);

/**
 * Create Order
 * Create a new order with order lines.
 */
export const createOrderApiV1OrdersPostOptions = (options: Options<CreateOrderApiV1OrdersPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrderApiV1OrdersPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrderApiV1OrdersPostQueryKey(options)
    });
};

/**
 * Create Order
 * Create a new order with order lines.
 */
export const createOrderApiV1OrdersPostMutation = (options?: Partial<Options<CreateOrderApiV1OrdersPostData>>): UseMutationOptions<CreateOrderApiV1OrdersPostResponse, AxiosError<CreateOrderApiV1OrdersPostError>, Options<CreateOrderApiV1OrdersPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrderApiV1OrdersPostResponse, AxiosError<CreateOrderApiV1OrdersPostError>, Options<CreateOrderApiV1OrdersPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrderApiV1OrdersPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderByOrderNoMainApiV1OrdersOrderNoGetQueryKey = (options: Options<GetOrderByOrderNoMainApiV1OrdersOrderNoGetData>) => createQueryKey('getOrderByOrderNoMainApiV1OrdersOrderNoGet', options);

/**
 * Get Order By Order No Main
 * Get order by order number with order lines, crafts, and craft routes.
 */
export const getOrderByOrderNoMainApiV1OrdersOrderNoGetOptions = (options: Options<GetOrderByOrderNoMainApiV1OrdersOrderNoGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderByOrderNoMainApiV1OrdersOrderNoGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderByOrderNoMainApiV1OrdersOrderNoGetQueryKey(options)
    });
};

/**
 * Delete Order
 * Delete order (admin only).
 */
export const deleteOrderApiV1OrdersOrderIdDeleteMutation = (options?: Partial<Options<DeleteOrderApiV1OrdersOrderIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteOrderApiV1OrdersOrderIdDeleteError>, Options<DeleteOrderApiV1OrdersOrderIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteOrderApiV1OrdersOrderIdDeleteError>, Options<DeleteOrderApiV1OrdersOrderIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteOrderApiV1OrdersOrderIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Order
 * Update order (admin or owner only).
 */
export const updateOrderApiV1OrdersOrderIdPutMutation = (options?: Partial<Options<UpdateOrderApiV1OrdersOrderIdPutData>>): UseMutationOptions<UpdateOrderApiV1OrdersOrderIdPutResponse, AxiosError<UpdateOrderApiV1OrdersOrderIdPutError>, Options<UpdateOrderApiV1OrdersOrderIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderApiV1OrdersOrderIdPutResponse, AxiosError<UpdateOrderApiV1OrdersOrderIdPutError>, Options<UpdateOrderApiV1OrdersOrderIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderApiV1OrdersOrderIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Order Status
 * Update order status.
 */
export const updateOrderStatusApiV1OrdersOrderIdStatusPutMutation = (options?: Partial<Options<UpdateOrderStatusApiV1OrdersOrderIdStatusPutData>>): UseMutationOptions<UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponse, AxiosError<UpdateOrderStatusApiV1OrdersOrderIdStatusPutError>, Options<UpdateOrderStatusApiV1OrdersOrderIdStatusPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponse, AxiosError<UpdateOrderStatusApiV1OrdersOrderIdStatusPutError>, Options<UpdateOrderStatusApiV1OrdersOrderIdStatusPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderStatusApiV1OrdersOrderIdStatusPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const startOrderApiV1OrdersOrderNoStartPostQueryKey = (options: Options<StartOrderApiV1OrdersOrderNoStartPostData>) => createQueryKey('startOrderApiV1OrdersOrderNoStartPost', options);

/**
 * Start Order
 * Start an order - change status from PENDING to IN_PROGRESS.
 */
export const startOrderApiV1OrdersOrderNoStartPostOptions = (options: Options<StartOrderApiV1OrdersOrderNoStartPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await startOrderApiV1OrdersOrderNoStartPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: startOrderApiV1OrdersOrderNoStartPostQueryKey(options)
    });
};

/**
 * Start Order
 * Start an order - change status from PENDING to IN_PROGRESS.
 */
export const startOrderApiV1OrdersOrderNoStartPostMutation = (options?: Partial<Options<StartOrderApiV1OrdersOrderNoStartPostData>>): UseMutationOptions<StartOrderApiV1OrdersOrderNoStartPostResponse, AxiosError<StartOrderApiV1OrdersOrderNoStartPostError>, Options<StartOrderApiV1OrdersOrderNoStartPostData>> => {
    const mutationOptions: UseMutationOptions<StartOrderApiV1OrdersOrderNoStartPostResponse, AxiosError<StartOrderApiV1OrdersOrderNoStartPostError>, Options<StartOrderApiV1OrdersOrderNoStartPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await startOrderApiV1OrdersOrderNoStartPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Craft Progress
 * Update order craft progress.
 */
export const updateCraftProgressApiV1OrdersOrderIdCraftProgressPutMutation = (options?: Partial<Options<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData>>): UseMutationOptions<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponse, AxiosError<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutError>, Options<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponse, AxiosError<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutError>, Options<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCraftProgressApiV1OrdersOrderIdCraftProgressPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Order Amount
 * Update order total amount.
 */
export const updateOrderAmountApiV1OrdersOrderIdAmountPutMutation = (options?: Partial<Options<UpdateOrderAmountApiV1OrdersOrderIdAmountPutData>>): UseMutationOptions<UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponse, AxiosError<UpdateOrderAmountApiV1OrdersOrderIdAmountPutError>, Options<UpdateOrderAmountApiV1OrdersOrderIdAmountPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponse, AxiosError<UpdateOrderAmountApiV1OrdersOrderIdAmountPutError>, Options<UpdateOrderAmountApiV1OrdersOrderIdAmountPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderAmountApiV1OrdersOrderIdAmountPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const addOrderLinesApiV1OrdersOrderLinesBulkPostQueryKey = (options: Options<AddOrderLinesApiV1OrdersOrderLinesBulkPostData>) => createQueryKey('addOrderLinesApiV1OrdersOrderLinesBulkPost', options);

/**
 * Add Order Lines
 * Add multiple order lines to an existing order.
 */
export const addOrderLinesApiV1OrdersOrderLinesBulkPostOptions = (options: Options<AddOrderLinesApiV1OrdersOrderLinesBulkPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await addOrderLinesApiV1OrdersOrderLinesBulkPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: addOrderLinesApiV1OrdersOrderLinesBulkPostQueryKey(options)
    });
};

/**
 * Add Order Lines
 * Add multiple order lines to an existing order.
 */
export const addOrderLinesApiV1OrdersOrderLinesBulkPostMutation = (options?: Partial<Options<AddOrderLinesApiV1OrdersOrderLinesBulkPostData>>): UseMutationOptions<AddOrderLinesApiV1OrdersOrderLinesBulkPostResponse, AxiosError<AddOrderLinesApiV1OrdersOrderLinesBulkPostError>, Options<AddOrderLinesApiV1OrdersOrderLinesBulkPostData>> => {
    const mutationOptions: UseMutationOptions<AddOrderLinesApiV1OrdersOrderLinesBulkPostResponse, AxiosError<AddOrderLinesApiV1OrdersOrderLinesBulkPostError>, Options<AddOrderLinesApiV1OrdersOrderLinesBulkPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await addOrderLinesApiV1OrdersOrderLinesBulkPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Production
 * Update production quantities for order lines.
 */
export const updateProductionApiV1OrdersProductionPutMutation = (options?: Partial<Options<UpdateProductionApiV1OrdersProductionPutData>>): UseMutationOptions<UpdateProductionApiV1OrdersProductionPutResponse, AxiosError<UpdateProductionApiV1OrdersProductionPutError>, Options<UpdateProductionApiV1OrdersProductionPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateProductionApiV1OrdersProductionPutResponse, AxiosError<UpdateProductionApiV1OrdersProductionPutError>, Options<UpdateProductionApiV1OrdersProductionPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateProductionApiV1OrdersProductionPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderStatisticsApiV1OrdersStatisticsSummaryGetQueryKey = (options?: Options<GetOrderStatisticsApiV1OrdersStatisticsSummaryGetData>) => createQueryKey('getOrderStatisticsApiV1OrdersStatisticsSummaryGet', options);

/**
 * Get Order Statistics
 * Get order statistics summary.
 */
export const getOrderStatisticsApiV1OrdersStatisticsSummaryGetOptions = (options?: Options<GetOrderStatisticsApiV1OrdersStatisticsSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderStatisticsApiV1OrdersStatisticsSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderStatisticsApiV1OrdersStatisticsSummaryGetQueryKey(options)
    });
};

export const getDashboardDataApiV1OrdersDashboardDataGetQueryKey = (options?: Options<GetDashboardDataApiV1OrdersDashboardDataGetData>) => createQueryKey('getDashboardDataApiV1OrdersDashboardDataGet', options);

/**
 * Get Dashboard Data
 * Get comprehensive dashboard data.
 */
export const getDashboardDataApiV1OrdersDashboardDataGetOptions = (options?: Options<GetDashboardDataApiV1OrdersDashboardDataGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDashboardDataApiV1OrdersDashboardDataGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDashboardDataApiV1OrdersDashboardDataGetQueryKey(options)
    });
};

export const searchOrderPartsApiV1OrderPartsGetQueryKey = (options?: Options<SearchOrderPartsApiV1OrderPartsGetData>) => createQueryKey('searchOrderPartsApiV1OrderPartsGet', options);

/**
 * Search Order Parts
 * Search order parts with optional filtering.
 */
export const searchOrderPartsApiV1OrderPartsGetOptions = (options?: Options<SearchOrderPartsApiV1OrderPartsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await searchOrderPartsApiV1OrderPartsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchOrderPartsApiV1OrderPartsGetQueryKey(options)
    });
};

export const createOrderPartApiV1OrderPartsPostQueryKey = (options: Options<CreateOrderPartApiV1OrderPartsPostData>) => createQueryKey('createOrderPartApiV1OrderPartsPost', options);

/**
 * Create Order Part
 * Create a new order part.
 */
export const createOrderPartApiV1OrderPartsPostOptions = (options: Options<CreateOrderPartApiV1OrderPartsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrderPartApiV1OrderPartsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrderPartApiV1OrderPartsPostQueryKey(options)
    });
};

/**
 * Create Order Part
 * Create a new order part.
 */
export const createOrderPartApiV1OrderPartsPostMutation = (options?: Partial<Options<CreateOrderPartApiV1OrderPartsPostData>>): UseMutationOptions<CreateOrderPartApiV1OrderPartsPostResponse, AxiosError<CreateOrderPartApiV1OrderPartsPostError>, Options<CreateOrderPartApiV1OrderPartsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrderPartApiV1OrderPartsPostResponse, AxiosError<CreateOrderPartApiV1OrderPartsPostError>, Options<CreateOrderPartApiV1OrderPartsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrderPartApiV1OrderPartsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetQueryKey = (options: Options<GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetData>) => createQueryKey('getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGet', options);

/**
 * Get Order Parts By Order
 * Get all order parts for a specific order.
 */
export const getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetOptions = (options: Options<GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetQueryKey(options)
    });
};

/**
 * Delete Order Part
 * Delete order part and all its bundles.
 */
export const deleteOrderPartApiV1OrderPartsOrderPartIdDeleteMutation = (options?: Partial<Options<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteError>, Options<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteError>, Options<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteOrderPartApiV1OrderPartsOrderPartIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderPartByIdApiV1OrderPartsOrderPartIdGetQueryKey = (options: Options<GetOrderPartByIdApiV1OrderPartsOrderPartIdGetData>) => createQueryKey('getOrderPartByIdApiV1OrderPartsOrderPartIdGet', options);

/**
 * Get Order Part By Id
 * Get order part by ID.
 */
export const getOrderPartByIdApiV1OrderPartsOrderPartIdGetOptions = (options: Options<GetOrderPartByIdApiV1OrderPartsOrderPartIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderPartByIdApiV1OrderPartsOrderPartIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderPartByIdApiV1OrderPartsOrderPartIdGetQueryKey(options)
    });
};

/**
 * Update Order Part
 * Update order part.
 */
export const updateOrderPartApiV1OrderPartsOrderPartIdPutMutation = (options?: Partial<Options<UpdateOrderPartApiV1OrderPartsOrderPartIdPutData>>): UseMutationOptions<UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponse, AxiosError<UpdateOrderPartApiV1OrderPartsOrderPartIdPutError>, Options<UpdateOrderPartApiV1OrderPartsOrderPartIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponse, AxiosError<UpdateOrderPartApiV1OrderPartsOrderPartIdPutError>, Options<UpdateOrderPartApiV1OrderPartsOrderPartIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderPartApiV1OrderPartsOrderPartIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetQueryKey = (options: Options<GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetData>) => createQueryKey('getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGet', options);

/**
 * Get Order Part With Bundles
 * Get order part by ID with order bundles.
 */
export const getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetOptions = (options: Options<GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetQueryKey(options)
    });
};

/**
 * Update Order Part Status
 * Update order part status.
 */
export const updateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutMutation = (options?: Partial<Options<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData>>): UseMutationOptions<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponse, AxiosError<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutError>, Options<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponse, AxiosError<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutError>, Options<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const bulkCreateOrderPartsApiV1OrderPartsBulkPostQueryKey = (options: Options<BulkCreateOrderPartsApiV1OrderPartsBulkPostData>) => createQueryKey('bulkCreateOrderPartsApiV1OrderPartsBulkPost', options);

/**
 * Bulk Create Order Parts
 * Create multiple order parts at once.
 */
export const bulkCreateOrderPartsApiV1OrderPartsBulkPostOptions = (options: Options<BulkCreateOrderPartsApiV1OrderPartsBulkPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await bulkCreateOrderPartsApiV1OrderPartsBulkPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: bulkCreateOrderPartsApiV1OrderPartsBulkPostQueryKey(options)
    });
};

/**
 * Bulk Create Order Parts
 * Create multiple order parts at once.
 */
export const bulkCreateOrderPartsApiV1OrderPartsBulkPostMutation = (options?: Partial<Options<BulkCreateOrderPartsApiV1OrderPartsBulkPostData>>): UseMutationOptions<BulkCreateOrderPartsApiV1OrderPartsBulkPostResponse, AxiosError<BulkCreateOrderPartsApiV1OrderPartsBulkPostError>, Options<BulkCreateOrderPartsApiV1OrderPartsBulkPostData>> => {
    const mutationOptions: UseMutationOptions<BulkCreateOrderPartsApiV1OrderPartsBulkPostResponse, AxiosError<BulkCreateOrderPartsApiV1OrderPartsBulkPostError>, Options<BulkCreateOrderPartsApiV1OrderPartsBulkPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await bulkCreateOrderPartsApiV1OrderPartsBulkPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetQueryKey = (options?: Options<GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetData>) => createQueryKey('getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGet', options);

/**
 * Get Order Part Statistics
 * Get order part statistics summary.
 */
export const getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetOptions = (options?: Options<GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetQueryKey(options)
    });
};

export const searchOrderBundlesApiV1OrderBundlesGetQueryKey = (options?: Options<SearchOrderBundlesApiV1OrderBundlesGetData>) => createQueryKey('searchOrderBundlesApiV1OrderBundlesGet', options);

/**
 * Search Order Bundles
 * Search order bundles with optional filtering.
 */
export const searchOrderBundlesApiV1OrderBundlesGetOptions = (options?: Options<SearchOrderBundlesApiV1OrderBundlesGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await searchOrderBundlesApiV1OrderBundlesGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchOrderBundlesApiV1OrderBundlesGetQueryKey(options)
    });
};

export const createOrderBundleApiV1OrderBundlesPostQueryKey = (options: Options<CreateOrderBundleApiV1OrderBundlesPostData>) => createQueryKey('createOrderBundleApiV1OrderBundlesPost', options);

/**
 * Create Order Bundle
 * Create a new order bundle.
 */
export const createOrderBundleApiV1OrderBundlesPostOptions = (options: Options<CreateOrderBundleApiV1OrderBundlesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrderBundleApiV1OrderBundlesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrderBundleApiV1OrderBundlesPostQueryKey(options)
    });
};

/**
 * Create Order Bundle
 * Create a new order bundle.
 */
export const createOrderBundleApiV1OrderBundlesPostMutation = (options?: Partial<Options<CreateOrderBundleApiV1OrderBundlesPostData>>): UseMutationOptions<CreateOrderBundleApiV1OrderBundlesPostResponse, AxiosError<CreateOrderBundleApiV1OrderBundlesPostError>, Options<CreateOrderBundleApiV1OrderBundlesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrderBundleApiV1OrderBundlesPostResponse, AxiosError<CreateOrderBundleApiV1OrderBundlesPostError>, Options<CreateOrderBundleApiV1OrderBundlesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrderBundleApiV1OrderBundlesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetQueryKey = (options: Options<GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetData>) => createQueryKey('getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGet', options);

/**
 * Get Order Bundles By Order Part
 * Get all order bundles for a specific order part.
 */
export const getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetOptions = (options: Options<GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetQueryKey(options)
    });
};

export const getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetQueryKey = (options: Options<GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetData>) => createQueryKey('getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGet', options);

/**
 * Get Order Bundles By Order
 * Get all order bundles for a specific order.
 */
export const getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetOptions = (options: Options<GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetQueryKey(options)
    });
};

/**
 * Delete Order Bundle
 * Delete order bundle.
 */
export const deleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteMutation = (options?: Partial<Options<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteError>, Options<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteError>, Options<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteOrderBundleApiV1OrderBundlesOrderBundleIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetQueryKey = (options: Options<GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetData>) => createQueryKey('getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGet', options);

/**
 * Get Order Bundle By Id
 * Get order bundle by ID.
 */
export const getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetOptions = (options: Options<GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetQueryKey(options)
    });
};

/**
 * Update Order Bundle
 * Update order bundle.
 */
export const updateOrderBundleApiV1OrderBundlesOrderBundleIdPutMutation = (options?: Partial<Options<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData>>): UseMutationOptions<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponse, AxiosError<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutError>, Options<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponse, AxiosError<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutError>, Options<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderBundleApiV1OrderBundlesOrderBundleIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Order Bundle Status
 * Update order bundle status.
 */
export const updateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutMutation = (options?: Partial<Options<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData>>): UseMutationOptions<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponse, AxiosError<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutError>, Options<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponse, AxiosError<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutError>, Options<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Order Bundle Production
 * Update order bundle production progress.
 */
export const updateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutMutation = (options?: Partial<Options<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData>>): UseMutationOptions<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponse, AxiosError<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutError>, Options<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponse, AxiosError<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutError>, Options<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const bulkCreateOrderBundlesApiV1OrderBundlesBulkPostQueryKey = (options: Options<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData>) => createQueryKey('bulkCreateOrderBundlesApiV1OrderBundlesBulkPost', options);

/**
 * Bulk Create Order Bundles
 * Create multiple order bundles at once.
 */
export const bulkCreateOrderBundlesApiV1OrderBundlesBulkPostOptions = (options: Options<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await bulkCreateOrderBundlesApiV1OrderBundlesBulkPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: bulkCreateOrderBundlesApiV1OrderBundlesBulkPostQueryKey(options)
    });
};

/**
 * Bulk Create Order Bundles
 * Create multiple order bundles at once.
 */
export const bulkCreateOrderBundlesApiV1OrderBundlesBulkPostMutation = (options?: Partial<Options<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData>>): UseMutationOptions<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponse, AxiosError<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostError>, Options<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData>> => {
    const mutationOptions: UseMutationOptions<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponse, AxiosError<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostError>, Options<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await bulkCreateOrderBundlesApiV1OrderBundlesBulkPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetQueryKey = (options?: Options<GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetData>) => createQueryKey('getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGet', options);

/**
 * Get Order Bundle Statistics
 * Get order bundle statistics summary.
 */
export const getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetOptions = (options?: Options<GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetQueryKey(options)
    });
};

export const getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetQueryKey = (options: Options<GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetData>) => createQueryKey('getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet', options);

/**
 * Get Order Crafts By Order
 * Get all order crafts for an order.
 */
export const getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetOptions = (options: Options<GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetQueryKey(options)
    });
};

export const createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostQueryKey = (options: Options<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData>) => createQueryKey('createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPost', options);

/**
 * Create Order Crafts For Order
 * Create order crafts configuration for an order.
 */
export const createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostOptions = (options: Options<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostQueryKey(options)
    });
};

/**
 * Create Order Crafts For Order
 * Create order crafts configuration for an order.
 */
export const createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostMutation = (options?: Partial<Options<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData>>): UseMutationOptions<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponse, AxiosError<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostError>, Options<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponse, AxiosError<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostError>, Options<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrderCraftByIdApiV1CraftsOrderCraftIdGetQueryKey = (options: Options<GetOrderCraftByIdApiV1CraftsOrderCraftIdGetData>) => createQueryKey('getOrderCraftByIdApiV1CraftsOrderCraftIdGet', options);

/**
 * Get Order Craft By Id
 * Get order craft by ID.
 */
export const getOrderCraftByIdApiV1CraftsOrderCraftIdGetOptions = (options: Options<GetOrderCraftByIdApiV1CraftsOrderCraftIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderCraftByIdApiV1CraftsOrderCraftIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderCraftByIdApiV1CraftsOrderCraftIdGetQueryKey(options)
    });
};

/**
 * Update Order Craft Status
 * Update order craft status.
 */
export const updateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutMutation = (options?: Partial<Options<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData>>): UseMutationOptions<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponse, AxiosError<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutError>, Options<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponse, AxiosError<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutError>, Options<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Order Craft Route Status
 * Update order craft route status.
 */
export const updateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutMutation = (options?: Partial<Options<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData>>): UseMutationOptions<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponse, AxiosError<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutError>, Options<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponse, AxiosError<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutError>, Options<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetQueryKey = (options: Options<GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetData>) => createQueryKey('getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGet', options);

/**
 * Get Next Craft For Order
 * Get the next pending craft for an order.
 */
export const getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetOptions = (options: Options<GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetQueryKey(options)
    });
};

export const getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetQueryKey = (options: Options<GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetData>) => createQueryKey('getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGet', options);

/**
 * Get Current Craft For Order
 * Get the currently in-progress craft for an order.
 */
export const getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetOptions = (options: Options<GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetQueryKey(options)
    });
};

export const getOrderCraftStatisticsApiV1StatisticsGetQueryKey = (options?: Options<GetOrderCraftStatisticsApiV1StatisticsGetData>) => createQueryKey('getOrderCraftStatisticsApiV1StatisticsGet', options);

/**
 * Get Order Craft Statistics
 * Get order craft statistics.
 */
export const getOrderCraftStatisticsApiV1StatisticsGetOptions = (options?: Options<GetOrderCraftStatisticsApiV1StatisticsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrderCraftStatisticsApiV1StatisticsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrderCraftStatisticsApiV1StatisticsGetQueryKey(options)
    });
};

export const createCraftInstanceApiV1CraftInstancesPostQueryKey = (options: Options<CreateCraftInstanceApiV1CraftInstancesPostData>) => createQueryKey('createCraftInstanceApiV1CraftInstancesPost', options);

/**
 * Create Craft Instance
 * Create a new craft instance - worker completion record.
 */
export const createCraftInstanceApiV1CraftInstancesPostOptions = (options: Options<CreateCraftInstanceApiV1CraftInstancesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createCraftInstanceApiV1CraftInstancesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createCraftInstanceApiV1CraftInstancesPostQueryKey(options)
    });
};

/**
 * Create Craft Instance
 * Create a new craft instance - worker completion record.
 */
export const createCraftInstanceApiV1CraftInstancesPostMutation = (options?: Partial<Options<CreateCraftInstanceApiV1CraftInstancesPostData>>): UseMutationOptions<CreateCraftInstanceApiV1CraftInstancesPostResponse, AxiosError<CreateCraftInstanceApiV1CraftInstancesPostError>, Options<CreateCraftInstanceApiV1CraftInstancesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateCraftInstanceApiV1CraftInstancesPostResponse, AxiosError<CreateCraftInstanceApiV1CraftInstancesPostError>, Options<CreateCraftInstanceApiV1CraftInstancesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createCraftInstanceApiV1CraftInstancesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const qrScanRegisterApiV1CraftInstancesQrScanPostQueryKey = (options: Options<QrScanRegisterApiV1CraftInstancesQrScanPostData>) => createQueryKey('qrScanRegisterApiV1CraftInstancesQrScanPost', options);

/**
 * Qr Scan Register
 * Register completion via QR code scanning.
 */
export const qrScanRegisterApiV1CraftInstancesQrScanPostOptions = (options: Options<QrScanRegisterApiV1CraftInstancesQrScanPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await qrScanRegisterApiV1CraftInstancesQrScanPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: qrScanRegisterApiV1CraftInstancesQrScanPostQueryKey(options)
    });
};

/**
 * Qr Scan Register
 * Register completion via QR code scanning.
 */
export const qrScanRegisterApiV1CraftInstancesQrScanPostMutation = (options?: Partial<Options<QrScanRegisterApiV1CraftInstancesQrScanPostData>>): UseMutationOptions<QrScanRegisterApiV1CraftInstancesQrScanPostResponse, AxiosError<QrScanRegisterApiV1CraftInstancesQrScanPostError>, Options<QrScanRegisterApiV1CraftInstancesQrScanPostData>> => {
    const mutationOptions: UseMutationOptions<QrScanRegisterApiV1CraftInstancesQrScanPostResponse, AxiosError<QrScanRegisterApiV1CraftInstancesQrScanPostError>, Options<QrScanRegisterApiV1CraftInstancesQrScanPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await qrScanRegisterApiV1CraftInstancesQrScanPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const searchCraftInstancesApiV1CraftInstancesSearchGetQueryKey = (options?: Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>) => createQueryKey('searchCraftInstancesApiV1CraftInstancesSearchGet', options);

/**
 * Search Craft Instances
 * Search craft instances with filters.
 */
export const searchCraftInstancesApiV1CraftInstancesSearchGetOptions = (options?: Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await searchCraftInstancesApiV1CraftInstancesSearchGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchCraftInstancesApiV1CraftInstancesSearchGetQueryKey(options)
    });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>>(queryKey: QueryKey<Options>, page: K) => {
    const params = {
        ...queryKey[0]
    };
    if (page.body) {
        params.body = {
            ...queryKey[0].body as any,
            ...page.body as any
        };
    }
    if (page.headers) {
        params.headers = {
            ...queryKey[0].headers,
            ...page.headers
        };
    }
    if (page.path) {
        params.path = {
            ...queryKey[0].path as any,
            ...page.path as any
        };
    }
    if (page.query) {
        params.query = {
            ...queryKey[0].query as any,
            ...page.query as any
        };
    }
    return params as unknown as typeof page;
};

export const searchCraftInstancesApiV1CraftInstancesSearchGetInfiniteQueryKey = (options?: Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>): QueryKey<Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>> => createQueryKey('searchCraftInstancesApiV1CraftInstancesSearchGet', options, true);

/**
 * Search Craft Instances
 * Search craft instances with filters.
 */
export const searchCraftInstancesApiV1CraftInstancesSearchGetInfiniteOptions = (options?: Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>) => {
    return infiniteQueryOptions<SearchCraftInstancesApiV1CraftInstancesSearchGetResponse, AxiosError<SearchCraftInstancesApiV1CraftInstancesSearchGetError>, InfiniteData<SearchCraftInstancesApiV1CraftInstancesSearchGetResponse>, QueryKey<Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>>, number | Pick<QueryKey<Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await searchCraftInstancesApiV1CraftInstancesSearchGet({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchCraftInstancesApiV1CraftInstancesSearchGetInfiniteQueryKey(options)
    });
};

export const getCraftInstanceApiV1CraftInstancesInstanceIdGetQueryKey = (options: Options<GetCraftInstanceApiV1CraftInstancesInstanceIdGetData>) => createQueryKey('getCraftInstanceApiV1CraftInstancesInstanceIdGet', options);

/**
 * Get Craft Instance
 * Get craft instance by ID.
 */
export const getCraftInstanceApiV1CraftInstancesInstanceIdGetOptions = (options: Options<GetCraftInstanceApiV1CraftInstancesInstanceIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCraftInstanceApiV1CraftInstancesInstanceIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCraftInstanceApiV1CraftInstancesInstanceIdGetQueryKey(options)
    });
};

export const verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostQueryKey = (options: Options<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData>) => createQueryKey('verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPost', options);

/**
 * Verify Craft Instance
 * Verify a craft instance.
 */
export const verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostOptions = (options: Options<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostQueryKey(options)
    });
};

/**
 * Verify Craft Instance
 * Verify a craft instance.
 */
export const verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostMutation = (options?: Partial<Options<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData>>): UseMutationOptions<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponse, AxiosError<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostError>, Options<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData>> => {
    const mutationOptions: UseMutationOptions<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponse, AxiosError<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostError>, Options<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostQueryKey = (options: Options<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData>) => createQueryKey('rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPost', options);

/**
 * Reject Craft Instance
 * Reject a craft instance.
 */
export const rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostOptions = (options: Options<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostQueryKey(options)
    });
};

/**
 * Reject Craft Instance
 * Reject a craft instance.
 */
export const rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostMutation = (options?: Partial<Options<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData>>): UseMutationOptions<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponse, AxiosError<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostError>, Options<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData>> => {
    const mutationOptions: UseMutationOptions<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponse, AxiosError<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostError>, Options<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetQueryKey = (options?: Options<GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetData>) => createQueryKey('getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGet', options);

/**
 * Get Craft Instance Statistics
 * Get craft instance statistics.
 */
export const getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetOptions = (options?: Options<GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetQueryKey(options)
    });
};

export const getAvailableRegistrationDataQueryKey = (options: Options<GetAvailableRegistrationDataData>) => createQueryKey('getAvailableRegistrationData', options);

/**
 * Get Available Registration Data
 * Get available registration data for an order - returns parts/bundles that can still be registered.
 */
export const getAvailableRegistrationDataOptions = (options: Options<GetAvailableRegistrationDataData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAvailableRegistrationData({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAvailableRegistrationDataQueryKey(options)
    });
};

export const getRegistrationSummaryQueryKey = (options: Options<GetRegistrationSummaryData>) => createQueryKey('getRegistrationSummary', options);

/**
 * Get Registration Summary
 * Get registration summary for an order - overview of completion status.
 */
export const getRegistrationSummaryOptions = (options: Options<GetRegistrationSummaryData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getRegistrationSummary({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getRegistrationSummaryQueryKey(options)
    });
};

export const getBillsApiV1BillsGetQueryKey = (options?: Options<GetBillsApiV1BillsGetData>) => createQueryKey('getBillsApiV1BillsGet', options);

/**
 * Get Bills
 * Get bills with filtering and pagination.
 */
export const getBillsApiV1BillsGetOptions = (options?: Options<GetBillsApiV1BillsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getBillsApiV1BillsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getBillsApiV1BillsGetQueryKey(options)
    });
};

export const createBillApiV1BillsPostQueryKey = (options: Options<CreateBillApiV1BillsPostData>) => createQueryKey('createBillApiV1BillsPost', options);

/**
 * Create Bill
 * Create a new bill.
 */
export const createBillApiV1BillsPostOptions = (options: Options<CreateBillApiV1BillsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createBillApiV1BillsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createBillApiV1BillsPostQueryKey(options)
    });
};

/**
 * Create Bill
 * Create a new bill.
 */
export const createBillApiV1BillsPostMutation = (options?: Partial<Options<CreateBillApiV1BillsPostData>>): UseMutationOptions<CreateBillApiV1BillsPostResponse, AxiosError<CreateBillApiV1BillsPostError>, Options<CreateBillApiV1BillsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateBillApiV1BillsPostResponse, AxiosError<CreateBillApiV1BillsPostError>, Options<CreateBillApiV1BillsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createBillApiV1BillsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getBillApiV1BillsBillIdGetQueryKey = (options: Options<GetBillApiV1BillsBillIdGetData>) => createQueryKey('getBillApiV1BillsBillIdGet', options);

/**
 * Get Bill
 * Get a bill by ID.
 */
export const getBillApiV1BillsBillIdGetOptions = (options: Options<GetBillApiV1BillsBillIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getBillApiV1BillsBillIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getBillApiV1BillsBillIdGetQueryKey(options)
    });
};

/**
 * Update Bill
 * Update a bill.
 */
export const updateBillApiV1BillsBillIdPutMutation = (options?: Partial<Options<UpdateBillApiV1BillsBillIdPutData>>): UseMutationOptions<UpdateBillApiV1BillsBillIdPutResponse, AxiosError<UpdateBillApiV1BillsBillIdPutError>, Options<UpdateBillApiV1BillsBillIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateBillApiV1BillsBillIdPutResponse, AxiosError<UpdateBillApiV1BillsBillIdPutError>, Options<UpdateBillApiV1BillsBillIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateBillApiV1BillsBillIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const submitBillForReviewApiV1BillsBillIdSubmitPostQueryKey = (options: Options<SubmitBillForReviewApiV1BillsBillIdSubmitPostData>) => createQueryKey('submitBillForReviewApiV1BillsBillIdSubmitPost', options);

/**
 * Submit Bill For Review
 * Submit a bill for review.
 */
export const submitBillForReviewApiV1BillsBillIdSubmitPostOptions = (options: Options<SubmitBillForReviewApiV1BillsBillIdSubmitPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await submitBillForReviewApiV1BillsBillIdSubmitPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: submitBillForReviewApiV1BillsBillIdSubmitPostQueryKey(options)
    });
};

/**
 * Submit Bill For Review
 * Submit a bill for review.
 */
export const submitBillForReviewApiV1BillsBillIdSubmitPostMutation = (options?: Partial<Options<SubmitBillForReviewApiV1BillsBillIdSubmitPostData>>): UseMutationOptions<SubmitBillForReviewApiV1BillsBillIdSubmitPostResponse, AxiosError<SubmitBillForReviewApiV1BillsBillIdSubmitPostError>, Options<SubmitBillForReviewApiV1BillsBillIdSubmitPostData>> => {
    const mutationOptions: UseMutationOptions<SubmitBillForReviewApiV1BillsBillIdSubmitPostResponse, AxiosError<SubmitBillForReviewApiV1BillsBillIdSubmitPostError>, Options<SubmitBillForReviewApiV1BillsBillIdSubmitPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await submitBillForReviewApiV1BillsBillIdSubmitPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const approveBillApiV1BillsBillIdApprovePostQueryKey = (options: Options<ApproveBillApiV1BillsBillIdApprovePostData>) => createQueryKey('approveBillApiV1BillsBillIdApprovePost', options);

/**
 * Approve Bill
 * Approve a bill.
 */
export const approveBillApiV1BillsBillIdApprovePostOptions = (options: Options<ApproveBillApiV1BillsBillIdApprovePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await approveBillApiV1BillsBillIdApprovePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: approveBillApiV1BillsBillIdApprovePostQueryKey(options)
    });
};

/**
 * Approve Bill
 * Approve a bill.
 */
export const approveBillApiV1BillsBillIdApprovePostMutation = (options?: Partial<Options<ApproveBillApiV1BillsBillIdApprovePostData>>): UseMutationOptions<ApproveBillApiV1BillsBillIdApprovePostResponse, AxiosError<ApproveBillApiV1BillsBillIdApprovePostError>, Options<ApproveBillApiV1BillsBillIdApprovePostData>> => {
    const mutationOptions: UseMutationOptions<ApproveBillApiV1BillsBillIdApprovePostResponse, AxiosError<ApproveBillApiV1BillsBillIdApprovePostError>, Options<ApproveBillApiV1BillsBillIdApprovePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await approveBillApiV1BillsBillIdApprovePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const rejectBillApiV1BillsBillIdRejectPostQueryKey = (options: Options<RejectBillApiV1BillsBillIdRejectPostData>) => createQueryKey('rejectBillApiV1BillsBillIdRejectPost', options);

/**
 * Reject Bill
 * Reject a bill.
 */
export const rejectBillApiV1BillsBillIdRejectPostOptions = (options: Options<RejectBillApiV1BillsBillIdRejectPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await rejectBillApiV1BillsBillIdRejectPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: rejectBillApiV1BillsBillIdRejectPostQueryKey(options)
    });
};

/**
 * Reject Bill
 * Reject a bill.
 */
export const rejectBillApiV1BillsBillIdRejectPostMutation = (options?: Partial<Options<RejectBillApiV1BillsBillIdRejectPostData>>): UseMutationOptions<RejectBillApiV1BillsBillIdRejectPostResponse, AxiosError<RejectBillApiV1BillsBillIdRejectPostError>, Options<RejectBillApiV1BillsBillIdRejectPostData>> => {
    const mutationOptions: UseMutationOptions<RejectBillApiV1BillsBillIdRejectPostResponse, AxiosError<RejectBillApiV1BillsBillIdRejectPostError>, Options<RejectBillApiV1BillsBillIdRejectPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await rejectBillApiV1BillsBillIdRejectPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const markBillAsPaidApiV1BillsBillIdPayPostQueryKey = (options: Options<MarkBillAsPaidApiV1BillsBillIdPayPostData>) => createQueryKey('markBillAsPaidApiV1BillsBillIdPayPost', options);

/**
 * Mark Bill As Paid
 * Mark a bill as paid.
 */
export const markBillAsPaidApiV1BillsBillIdPayPostOptions = (options: Options<MarkBillAsPaidApiV1BillsBillIdPayPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await markBillAsPaidApiV1BillsBillIdPayPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: markBillAsPaidApiV1BillsBillIdPayPostQueryKey(options)
    });
};

/**
 * Mark Bill As Paid
 * Mark a bill as paid.
 */
export const markBillAsPaidApiV1BillsBillIdPayPostMutation = (options?: Partial<Options<MarkBillAsPaidApiV1BillsBillIdPayPostData>>): UseMutationOptions<MarkBillAsPaidApiV1BillsBillIdPayPostResponse, AxiosError<MarkBillAsPaidApiV1BillsBillIdPayPostError>, Options<MarkBillAsPaidApiV1BillsBillIdPayPostData>> => {
    const mutationOptions: UseMutationOptions<MarkBillAsPaidApiV1BillsBillIdPayPostResponse, AxiosError<MarkBillAsPaidApiV1BillsBillIdPayPostError>, Options<MarkBillAsPaidApiV1BillsBillIdPayPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await markBillAsPaidApiV1BillsBillIdPayPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const cancelBillApiV1BillsBillIdCancelPostQueryKey = (options: Options<CancelBillApiV1BillsBillIdCancelPostData>) => createQueryKey('cancelBillApiV1BillsBillIdCancelPost', options);

/**
 * Cancel Bill
 * Cancel a bill.
 */
export const cancelBillApiV1BillsBillIdCancelPostOptions = (options: Options<CancelBillApiV1BillsBillIdCancelPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await cancelBillApiV1BillsBillIdCancelPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: cancelBillApiV1BillsBillIdCancelPostQueryKey(options)
    });
};

/**
 * Cancel Bill
 * Cancel a bill.
 */
export const cancelBillApiV1BillsBillIdCancelPostMutation = (options?: Partial<Options<CancelBillApiV1BillsBillIdCancelPostData>>): UseMutationOptions<CancelBillApiV1BillsBillIdCancelPostResponse, AxiosError<CancelBillApiV1BillsBillIdCancelPostError>, Options<CancelBillApiV1BillsBillIdCancelPostData>> => {
    const mutationOptions: UseMutationOptions<CancelBillApiV1BillsBillIdCancelPostResponse, AxiosError<CancelBillApiV1BillsBillIdCancelPostError>, Options<CancelBillApiV1BillsBillIdCancelPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await cancelBillApiV1BillsBillIdCancelPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getSettlementSummaryApiV1BillsSettlementSummaryGetQueryKey = (options?: Options<GetSettlementSummaryApiV1BillsSettlementSummaryGetData>) => createQueryKey('getSettlementSummaryApiV1BillsSettlementSummaryGet', options);

/**
 * Get Settlement Summary
 * Get settlement summary for the factory.
 */
export const getSettlementSummaryApiV1BillsSettlementSummaryGetOptions = (options?: Options<GetSettlementSummaryApiV1BillsSettlementSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getSettlementSummaryApiV1BillsSettlementSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getSettlementSummaryApiV1BillsSettlementSummaryGetQueryKey(options)
    });
};

export const disputeInstanceApiV1BillsInstancesInstanceIdDisputePostQueryKey = (options: Options<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData>) => createQueryKey('disputeInstanceApiV1BillsInstancesInstanceIdDisputePost', options);

/**
 * Dispute Instance
 * Dispute a bill instance.
 */
export const disputeInstanceApiV1BillsInstancesInstanceIdDisputePostOptions = (options: Options<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await disputeInstanceApiV1BillsInstancesInstanceIdDisputePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: disputeInstanceApiV1BillsInstancesInstanceIdDisputePostQueryKey(options)
    });
};

/**
 * Dispute Instance
 * Dispute a bill instance.
 */
export const disputeInstanceApiV1BillsInstancesInstanceIdDisputePostMutation = (options?: Partial<Options<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData>>): UseMutationOptions<unknown, AxiosError<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostError>, Options<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostError>, Options<DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await disputeInstanceApiV1BillsInstancesInstanceIdDisputePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostQueryKey = (options: Options<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData>) => createQueryKey('resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePost', options);

/**
 * Resolve Instance Dispute
 * Resolve an instance dispute.
 */
export const resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostOptions = (options: Options<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostQueryKey(options)
    });
};

/**
 * Resolve Instance Dispute
 * Resolve an instance dispute.
 */
export const resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostMutation = (options?: Partial<Options<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData>>): UseMutationOptions<unknown, AxiosError<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostError>, Options<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostError>, Options<ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await resolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getPendingBillsApiV1BillsPendingGetQueryKey = (options?: Options<GetPendingBillsApiV1BillsPendingGetData>) => createQueryKey('getPendingBillsApiV1BillsPendingGet', options);

/**
 * Get Pending Bills
 * Get all pending bills for the factory.
 */
export const getPendingBillsApiV1BillsPendingGetOptions = (options?: Options<GetPendingBillsApiV1BillsPendingGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getPendingBillsApiV1BillsPendingGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getPendingBillsApiV1BillsPendingGetQueryKey(options)
    });
};

export const getDashboardStatisticsApiV1StatisticsDashboardGetQueryKey = (options?: Options<GetDashboardStatisticsApiV1StatisticsDashboardGetData>) => createQueryKey('getDashboardStatisticsApiV1StatisticsDashboardGet', options);

/**
 * Get Dashboard Statistics
 * Get comprehensive dashboard statistics for the current factory.
 */
export const getDashboardStatisticsApiV1StatisticsDashboardGetOptions = (options?: Options<GetDashboardStatisticsApiV1StatisticsDashboardGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDashboardStatisticsApiV1StatisticsDashboardGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDashboardStatisticsApiV1StatisticsDashboardGetQueryKey(options)
    });
};

export const getScanSummaryApiV1StatisticsScanSummaryGetQueryKey = (options?: Options<GetScanSummaryApiV1StatisticsScanSummaryGetData>) => createQueryKey('getScanSummaryApiV1StatisticsScanSummaryGet', options);

/**
 * Get Scan Summary
 * Get scan count summary for the specified date.
 */
export const getScanSummaryApiV1StatisticsScanSummaryGetOptions = (options?: Options<GetScanSummaryApiV1StatisticsScanSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getScanSummaryApiV1StatisticsScanSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getScanSummaryApiV1StatisticsScanSummaryGetQueryKey(options)
    });
};

export const getOnlineEmployeesApiV1StatisticsOnlineEmployeesGetQueryKey = (options?: Options<GetOnlineEmployeesApiV1StatisticsOnlineEmployeesGetData>) => createQueryKey('getOnlineEmployeesApiV1StatisticsOnlineEmployeesGet', options);

/**
 * Get Online Employees
 * Get online employees information.
 */
export const getOnlineEmployeesApiV1StatisticsOnlineEmployeesGetOptions = (options?: Options<GetOnlineEmployeesApiV1StatisticsOnlineEmployeesGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOnlineEmployeesApiV1StatisticsOnlineEmployeesGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOnlineEmployeesApiV1StatisticsOnlineEmployeesGetQueryKey(options)
    });
};

export const createTemplateApiV1CraftTemplatesPostQueryKey = (options: Options<CreateTemplateApiV1CraftTemplatesPostData>) => createQueryKey('createTemplateApiV1CraftTemplatesPost', options);

/**
 * Create Template
 * Create a new craft template.
 */
export const createTemplateApiV1CraftTemplatesPostOptions = (options: Options<CreateTemplateApiV1CraftTemplatesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createTemplateApiV1CraftTemplatesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createTemplateApiV1CraftTemplatesPostQueryKey(options)
    });
};

/**
 * Create Template
 * Create a new craft template.
 */
export const createTemplateApiV1CraftTemplatesPostMutation = (options?: Partial<Options<CreateTemplateApiV1CraftTemplatesPostData>>): UseMutationOptions<CreateTemplateApiV1CraftTemplatesPostResponse, AxiosError<CreateTemplateApiV1CraftTemplatesPostError>, Options<CreateTemplateApiV1CraftTemplatesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateTemplateApiV1CraftTemplatesPostResponse, AxiosError<CreateTemplateApiV1CraftTemplatesPostError>, Options<CreateTemplateApiV1CraftTemplatesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createTemplateApiV1CraftTemplatesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Template
 * Delete a craft template.
 */
export const deleteTemplateApiV1CraftTemplatesTemplateIdDeleteMutation = (options?: Partial<Options<DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteError>, Options<DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteError>, Options<DeleteTemplateApiV1CraftTemplatesTemplateIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteTemplateApiV1CraftTemplatesTemplateIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getTemplateApiV1CraftTemplatesTemplateIdGetQueryKey = (options: Options<GetTemplateApiV1CraftTemplatesTemplateIdGetData>) => createQueryKey('getTemplateApiV1CraftTemplatesTemplateIdGet', options);

/**
 * Get Template
 * Get a craft template by ID.
 */
export const getTemplateApiV1CraftTemplatesTemplateIdGetOptions = (options: Options<GetTemplateApiV1CraftTemplatesTemplateIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTemplateApiV1CraftTemplatesTemplateIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTemplateApiV1CraftTemplatesTemplateIdGetQueryKey(options)
    });
};

/**
 * Update Template
 * Update a craft template.
 */
export const updateTemplateApiV1CraftTemplatesTemplateIdPutMutation = (options?: Partial<Options<UpdateTemplateApiV1CraftTemplatesTemplateIdPutData>>): UseMutationOptions<UpdateTemplateApiV1CraftTemplatesTemplateIdPutResponse, AxiosError<UpdateTemplateApiV1CraftTemplatesTemplateIdPutError>, Options<UpdateTemplateApiV1CraftTemplatesTemplateIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateTemplateApiV1CraftTemplatesTemplateIdPutResponse, AxiosError<UpdateTemplateApiV1CraftTemplatesTemplateIdPutError>, Options<UpdateTemplateApiV1CraftTemplatesTemplateIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateTemplateApiV1CraftTemplatesTemplateIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const searchTemplatesApiV1CraftTemplatesSearchPostQueryKey = (options: Options<SearchTemplatesApiV1CraftTemplatesSearchPostData>) => createQueryKey('searchTemplatesApiV1CraftTemplatesSearchPost', options);

/**
 * Search Templates
 * Search craft templates with flexible parameters.
 */
export const searchTemplatesApiV1CraftTemplatesSearchPostOptions = (options: Options<SearchTemplatesApiV1CraftTemplatesSearchPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await searchTemplatesApiV1CraftTemplatesSearchPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchTemplatesApiV1CraftTemplatesSearchPostQueryKey(options)
    });
};

/**
 * Search Templates
 * Search craft templates with flexible parameters.
 */
export const searchTemplatesApiV1CraftTemplatesSearchPostMutation = (options?: Partial<Options<SearchTemplatesApiV1CraftTemplatesSearchPostData>>): UseMutationOptions<SearchTemplatesApiV1CraftTemplatesSearchPostResponse, AxiosError<SearchTemplatesApiV1CraftTemplatesSearchPostError>, Options<SearchTemplatesApiV1CraftTemplatesSearchPostData>> => {
    const mutationOptions: UseMutationOptions<SearchTemplatesApiV1CraftTemplatesSearchPostResponse, AxiosError<SearchTemplatesApiV1CraftTemplatesSearchPostError>, Options<SearchTemplatesApiV1CraftTemplatesSearchPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await searchTemplatesApiV1CraftTemplatesSearchPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostQueryKey = (options: Options<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostData>) => createQueryKey('duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePost', options);

/**
 * Duplicate Template
 * Duplicate a template.
 */
export const duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostOptions = (options: Options<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostQueryKey(options)
    });
};

/**
 * Duplicate Template
 * Duplicate a template.
 */
export const duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostMutation = (options?: Partial<Options<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostData>>): UseMutationOptions<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostResponse, AxiosError<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostError>, Options<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostData>> => {
    const mutationOptions: UseMutationOptions<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostResponse, AxiosError<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostError>, Options<DuplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await duplicateTemplateApiV1CraftTemplatesTemplateIdDuplicatePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGetQueryKey = (options: Options<GetTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGetData>) => createQueryKey('getTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGet', options);

/**
 * Get Template Statistics
 * Get template statistics for a factory.
 */
export const getTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGetOptions = (options: Options<GetTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTemplateStatisticsApiV1CraftTemplatesStatisticsFactoryFactoryIdGetQueryKey(options)
    });
};

export const getTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGetQueryKey = (options: Options<GetTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGetData>) => createQueryKey('getTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGet', options);

/**
 * Get Template Crafts
 * Get all crafts in a template.
 */
export const getTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGetOptions = (options: Options<GetTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTemplateCraftsApiV1CraftTemplatesTemplateIdCraftsGetQueryKey(options)
    });
};

export const addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostQueryKey = (options: Options<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostData>) => createQueryKey('addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPost', options);

/**
 * Add Craft To Template
 * Add a craft to template.
 */
export const addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostOptions = (options: Options<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostQueryKey(options)
    });
};

/**
 * Add Craft To Template
 * Add a craft to template.
 */
export const addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostMutation = (options?: Partial<Options<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostData>>): UseMutationOptions<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostResponse, AxiosError<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostError>, Options<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostData>> => {
    const mutationOptions: UseMutationOptions<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostResponse, AxiosError<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostError>, Options<AddCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await addCraftToTemplateApiV1CraftTemplatesTemplateIdCraftsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Remove Craft From Template
 * Remove a craft from template.
 */
export const removeCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteMutation = (options?: Partial<Options<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteData>>): UseMutationOptions<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteResponse, AxiosError<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteError>, Options<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteData>> => {
    const mutationOptions: UseMutationOptions<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteResponse, AxiosError<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteError>, Options<RemoveCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await removeCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGetQueryKey = (options: Options<GetCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGetData>) => createQueryKey('getCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGet', options);

/**
 * Get Craft From Template
 * Get a specific craft from template.
 */
export const getCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGetOptions = (options: Options<GetCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCraftFromTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodeGetQueryKey(options)
    });
};

/**
 * Update Craft In Template
 * Update a craft in template.
 */
export const updateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutMutation = (options?: Partial<Options<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutData>>): UseMutationOptions<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutResponse, AxiosError<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutError>, Options<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutData>> => {
    const mutationOptions: UseMutationOptions<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutResponse, AxiosError<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutError>, Options<UpdateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCraftInTemplateApiV1CraftTemplatesTemplateIdCraftsCraftCodePut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createAuditRecordApiV1PriceAuditPostQueryKey = (options: Options<CreateAuditRecordApiV1PriceAuditPostData>) => createQueryKey('createAuditRecordApiV1PriceAuditPost', options);

/**
 * Create Audit Record
 * Create a new price audit record.
 */
export const createAuditRecordApiV1PriceAuditPostOptions = (options: Options<CreateAuditRecordApiV1PriceAuditPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createAuditRecordApiV1PriceAuditPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createAuditRecordApiV1PriceAuditPostQueryKey(options)
    });
};

/**
 * Create Audit Record
 * Create a new price audit record.
 */
export const createAuditRecordApiV1PriceAuditPostMutation = (options?: Partial<Options<CreateAuditRecordApiV1PriceAuditPostData>>): UseMutationOptions<CreateAuditRecordApiV1PriceAuditPostResponse, AxiosError<CreateAuditRecordApiV1PriceAuditPostError>, Options<CreateAuditRecordApiV1PriceAuditPostData>> => {
    const mutationOptions: UseMutationOptions<CreateAuditRecordApiV1PriceAuditPostResponse, AxiosError<CreateAuditRecordApiV1PriceAuditPostError>, Options<CreateAuditRecordApiV1PriceAuditPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createAuditRecordApiV1PriceAuditPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Audit Record
 * Delete a price audit record.
 */
export const deleteAuditRecordApiV1PriceAuditRecordIdDeleteMutation = (options?: Partial<Options<DeleteAuditRecordApiV1PriceAuditRecordIdDeleteData>>): UseMutationOptions<unknown, AxiosError<DeleteAuditRecordApiV1PriceAuditRecordIdDeleteError>, Options<DeleteAuditRecordApiV1PriceAuditRecordIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DeleteAuditRecordApiV1PriceAuditRecordIdDeleteError>, Options<DeleteAuditRecordApiV1PriceAuditRecordIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteAuditRecordApiV1PriceAuditRecordIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAuditRecordApiV1PriceAuditRecordIdGetQueryKey = (options: Options<GetAuditRecordApiV1PriceAuditRecordIdGetData>) => createQueryKey('getAuditRecordApiV1PriceAuditRecordIdGet', options);

/**
 * Get Audit Record
 * Get a price audit record by ID.
 */
export const getAuditRecordApiV1PriceAuditRecordIdGetOptions = (options: Options<GetAuditRecordApiV1PriceAuditRecordIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAuditRecordApiV1PriceAuditRecordIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAuditRecordApiV1PriceAuditRecordIdGetQueryKey(options)
    });
};

/**
 * Update Audit Record
 * Update a price audit record.
 */
export const updateAuditRecordApiV1PriceAuditRecordIdPutMutation = (options?: Partial<Options<UpdateAuditRecordApiV1PriceAuditRecordIdPutData>>): UseMutationOptions<UpdateAuditRecordApiV1PriceAuditRecordIdPutResponse, AxiosError<UpdateAuditRecordApiV1PriceAuditRecordIdPutError>, Options<UpdateAuditRecordApiV1PriceAuditRecordIdPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateAuditRecordApiV1PriceAuditRecordIdPutResponse, AxiosError<UpdateAuditRecordApiV1PriceAuditRecordIdPutError>, Options<UpdateAuditRecordApiV1PriceAuditRecordIdPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateAuditRecordApiV1PriceAuditRecordIdPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const searchAuditRecordsApiV1PriceAuditSearchPostQueryKey = (options: Options<SearchAuditRecordsApiV1PriceAuditSearchPostData>) => createQueryKey('searchAuditRecordsApiV1PriceAuditSearchPost', options);

/**
 * Search Audit Records
 * Search price audit records with flexible parameters.
 */
export const searchAuditRecordsApiV1PriceAuditSearchPostOptions = (options: Options<SearchAuditRecordsApiV1PriceAuditSearchPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await searchAuditRecordsApiV1PriceAuditSearchPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: searchAuditRecordsApiV1PriceAuditSearchPostQueryKey(options)
    });
};

/**
 * Search Audit Records
 * Search price audit records with flexible parameters.
 */
export const searchAuditRecordsApiV1PriceAuditSearchPostMutation = (options?: Partial<Options<SearchAuditRecordsApiV1PriceAuditSearchPostData>>): UseMutationOptions<SearchAuditRecordsApiV1PriceAuditSearchPostResponse, AxiosError<SearchAuditRecordsApiV1PriceAuditSearchPostError>, Options<SearchAuditRecordsApiV1PriceAuditSearchPostData>> => {
    const mutationOptions: UseMutationOptions<SearchAuditRecordsApiV1PriceAuditSearchPostResponse, AxiosError<SearchAuditRecordsApiV1PriceAuditSearchPostError>, Options<SearchAuditRecordsApiV1PriceAuditSearchPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await searchAuditRecordsApiV1PriceAuditSearchPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGetQueryKey = (options: Options<GetAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGetData>) => createQueryKey('getAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGet', options);

/**
 * Get Audit Records By Route Instance
 * Get all audit records for a specific route instance.
 */
export const getAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGetOptions = (options: Options<GetAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAuditRecordsByRouteInstanceApiV1PriceAuditRouteInstanceRouteInstanceIdGetQueryKey(options)
    });
};

export const getPendingReviewsApiV1PriceAuditPendingReviewsGetQueryKey = (options: Options<GetPendingReviewsApiV1PriceAuditPendingReviewsGetData>) => createQueryKey('getPendingReviewsApiV1PriceAuditPendingReviewsGet', options);

/**
 * Get Pending Reviews
 * Get all pending review records.
 */
export const getPendingReviewsApiV1PriceAuditPendingReviewsGetOptions = (options: Options<GetPendingReviewsApiV1PriceAuditPendingReviewsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getPendingReviewsApiV1PriceAuditPendingReviewsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getPendingReviewsApiV1PriceAuditPendingReviewsGetQueryKey(options)
    });
};

export const getUnderReviewApiV1PriceAuditUnderReviewGetQueryKey = (options: Options<GetUnderReviewApiV1PriceAuditUnderReviewGetData>) => createQueryKey('getUnderReviewApiV1PriceAuditUnderReviewGet', options);

/**
 * Get Under Review
 * Get all records currently under review.
 */
export const getUnderReviewApiV1PriceAuditUnderReviewGetOptions = (options: Options<GetUnderReviewApiV1PriceAuditUnderReviewGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUnderReviewApiV1PriceAuditUnderReviewGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUnderReviewApiV1PriceAuditUnderReviewGetQueryKey(options)
    });
};

export const getOverdueRecordsApiV1PriceAuditOverdueGetQueryKey = (options: Options<GetOverdueRecordsApiV1PriceAuditOverdueGetData>) => createQueryKey('getOverdueRecordsApiV1PriceAuditOverdueGet', options);

/**
 * Get Overdue Records
 * Get all overdue audit records.
 */
export const getOverdueRecordsApiV1PriceAuditOverdueGetOptions = (options: Options<GetOverdueRecordsApiV1PriceAuditOverdueGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOverdueRecordsApiV1PriceAuditOverdueGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOverdueRecordsApiV1PriceAuditOverdueGetQueryKey(options)
    });
};

export const getHighPriorityRecordsApiV1PriceAuditHighPriorityGetQueryKey = (options: Options<GetHighPriorityRecordsApiV1PriceAuditHighPriorityGetData>) => createQueryKey('getHighPriorityRecordsApiV1PriceAuditHighPriorityGet', options);

/**
 * Get High Priority Records
 * Get high priority audit records.
 */
export const getHighPriorityRecordsApiV1PriceAuditHighPriorityGetOptions = (options: Options<GetHighPriorityRecordsApiV1PriceAuditHighPriorityGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getHighPriorityRecordsApiV1PriceAuditHighPriorityGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getHighPriorityRecordsApiV1PriceAuditHighPriorityGetQueryKey(options)
    });
};

export const getEscalatedRecordsApiV1PriceAuditEscalatedGetQueryKey = (options: Options<GetEscalatedRecordsApiV1PriceAuditEscalatedGetData>) => createQueryKey('getEscalatedRecordsApiV1PriceAuditEscalatedGet', options);

/**
 * Get Escalated Records
 * Get escalated audit records.
 */
export const getEscalatedRecordsApiV1PriceAuditEscalatedGetOptions = (options: Options<GetEscalatedRecordsApiV1PriceAuditEscalatedGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEscalatedRecordsApiV1PriceAuditEscalatedGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEscalatedRecordsApiV1PriceAuditEscalatedGetQueryKey(options)
    });
};

export const performAuditActionApiV1PriceAuditActionPostQueryKey = (options: Options<PerformAuditActionApiV1PriceAuditActionPostData>) => createQueryKey('performAuditActionApiV1PriceAuditActionPost', options);

/**
 * Perform Audit Action
 * Perform an audit action on a record.
 */
export const performAuditActionApiV1PriceAuditActionPostOptions = (options: Options<PerformAuditActionApiV1PriceAuditActionPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await performAuditActionApiV1PriceAuditActionPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: performAuditActionApiV1PriceAuditActionPostQueryKey(options)
    });
};

/**
 * Perform Audit Action
 * Perform an audit action on a record.
 */
export const performAuditActionApiV1PriceAuditActionPostMutation = (options?: Partial<Options<PerformAuditActionApiV1PriceAuditActionPostData>>): UseMutationOptions<PerformAuditActionApiV1PriceAuditActionPostResponse, AxiosError<PerformAuditActionApiV1PriceAuditActionPostError>, Options<PerformAuditActionApiV1PriceAuditActionPostData>> => {
    const mutationOptions: UseMutationOptions<PerformAuditActionApiV1PriceAuditActionPostResponse, AxiosError<PerformAuditActionApiV1PriceAuditActionPostError>, Options<PerformAuditActionApiV1PriceAuditActionPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await performAuditActionApiV1PriceAuditActionPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const bulkAuditActionApiV1PriceAuditBulkActionPostQueryKey = (options: Options<BulkAuditActionApiV1PriceAuditBulkActionPostData>) => createQueryKey('bulkAuditActionApiV1PriceAuditBulkActionPost', options);

/**
 * Bulk Audit Action
 * Perform bulk audit action on multiple records.
 */
export const bulkAuditActionApiV1PriceAuditBulkActionPostOptions = (options: Options<BulkAuditActionApiV1PriceAuditBulkActionPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await bulkAuditActionApiV1PriceAuditBulkActionPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: bulkAuditActionApiV1PriceAuditBulkActionPostQueryKey(options)
    });
};

/**
 * Bulk Audit Action
 * Perform bulk audit action on multiple records.
 */
export const bulkAuditActionApiV1PriceAuditBulkActionPostMutation = (options?: Partial<Options<BulkAuditActionApiV1PriceAuditBulkActionPostData>>): UseMutationOptions<BulkAuditActionApiV1PriceAuditBulkActionPostResponse, AxiosError<BulkAuditActionApiV1PriceAuditBulkActionPostError>, Options<BulkAuditActionApiV1PriceAuditBulkActionPostData>> => {
    const mutationOptions: UseMutationOptions<BulkAuditActionApiV1PriceAuditBulkActionPostResponse, AxiosError<BulkAuditActionApiV1PriceAuditBulkActionPostError>, Options<BulkAuditActionApiV1PriceAuditBulkActionPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await bulkAuditActionApiV1PriceAuditBulkActionPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const assignReviewerApiV1PriceAuditAssignReviewerPostQueryKey = (options: Options<AssignReviewerApiV1PriceAuditAssignReviewerPostData>) => createQueryKey('assignReviewerApiV1PriceAuditAssignReviewerPost', options);

/**
 * Assign Reviewer
 * Assign reviewer to audit records.
 */
export const assignReviewerApiV1PriceAuditAssignReviewerPostOptions = (options: Options<AssignReviewerApiV1PriceAuditAssignReviewerPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await assignReviewerApiV1PriceAuditAssignReviewerPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: assignReviewerApiV1PriceAuditAssignReviewerPostQueryKey(options)
    });
};

/**
 * Assign Reviewer
 * Assign reviewer to audit records.
 */
export const assignReviewerApiV1PriceAuditAssignReviewerPostMutation = (options?: Partial<Options<AssignReviewerApiV1PriceAuditAssignReviewerPostData>>): UseMutationOptions<AssignReviewerApiV1PriceAuditAssignReviewerPostResponse, AxiosError<AssignReviewerApiV1PriceAuditAssignReviewerPostError>, Options<AssignReviewerApiV1PriceAuditAssignReviewerPostData>> => {
    const mutationOptions: UseMutationOptions<AssignReviewerApiV1PriceAuditAssignReviewerPostResponse, AxiosError<AssignReviewerApiV1PriceAuditAssignReviewerPostError>, Options<AssignReviewerApiV1PriceAuditAssignReviewerPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await assignReviewerApiV1PriceAuditAssignReviewerPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const setPriorityApiV1PriceAuditSetPriorityPostQueryKey = (options: Options<SetPriorityApiV1PriceAuditSetPriorityPostData>) => createQueryKey('setPriorityApiV1PriceAuditSetPriorityPost', options);

/**
 * Set Priority
 * Set priority for audit records.
 */
export const setPriorityApiV1PriceAuditSetPriorityPostOptions = (options: Options<SetPriorityApiV1PriceAuditSetPriorityPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await setPriorityApiV1PriceAuditSetPriorityPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: setPriorityApiV1PriceAuditSetPriorityPostQueryKey(options)
    });
};

/**
 * Set Priority
 * Set priority for audit records.
 */
export const setPriorityApiV1PriceAuditSetPriorityPostMutation = (options?: Partial<Options<SetPriorityApiV1PriceAuditSetPriorityPostData>>): UseMutationOptions<SetPriorityApiV1PriceAuditSetPriorityPostResponse, AxiosError<SetPriorityApiV1PriceAuditSetPriorityPostError>, Options<SetPriorityApiV1PriceAuditSetPriorityPostData>> => {
    const mutationOptions: UseMutationOptions<SetPriorityApiV1PriceAuditSetPriorityPostResponse, AxiosError<SetPriorityApiV1PriceAuditSetPriorityPostError>, Options<SetPriorityApiV1PriceAuditSetPriorityPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await setPriorityApiV1PriceAuditSetPriorityPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const addEvidenceApiV1PriceAuditAddEvidencePostQueryKey = (options: Options<AddEvidenceApiV1PriceAuditAddEvidencePostData>) => createQueryKey('addEvidenceApiV1PriceAuditAddEvidencePost', options);

/**
 * Add Evidence
 * Add evidence to an audit record.
 */
export const addEvidenceApiV1PriceAuditAddEvidencePostOptions = (options: Options<AddEvidenceApiV1PriceAuditAddEvidencePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await addEvidenceApiV1PriceAuditAddEvidencePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: addEvidenceApiV1PriceAuditAddEvidencePostQueryKey(options)
    });
};

/**
 * Add Evidence
 * Add evidence to an audit record.
 */
export const addEvidenceApiV1PriceAuditAddEvidencePostMutation = (options?: Partial<Options<AddEvidenceApiV1PriceAuditAddEvidencePostData>>): UseMutationOptions<AddEvidenceApiV1PriceAuditAddEvidencePostResponse, AxiosError<AddEvidenceApiV1PriceAuditAddEvidencePostError>, Options<AddEvidenceApiV1PriceAuditAddEvidencePostData>> => {
    const mutationOptions: UseMutationOptions<AddEvidenceApiV1PriceAuditAddEvidencePostResponse, AxiosError<AddEvidenceApiV1PriceAuditAddEvidencePostError>, Options<AddEvidenceApiV1PriceAuditAddEvidencePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await addEvidenceApiV1PriceAuditAddEvidencePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const escalateAuditApiV1PriceAuditEscalatePostQueryKey = (options: Options<EscalateAuditApiV1PriceAuditEscalatePostData>) => createQueryKey('escalateAuditApiV1PriceAuditEscalatePost', options);

/**
 * Escalate Audit
 * Escalate an audit record.
 */
export const escalateAuditApiV1PriceAuditEscalatePostOptions = (options: Options<EscalateAuditApiV1PriceAuditEscalatePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await escalateAuditApiV1PriceAuditEscalatePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: escalateAuditApiV1PriceAuditEscalatePostQueryKey(options)
    });
};

/**
 * Escalate Audit
 * Escalate an audit record.
 */
export const escalateAuditApiV1PriceAuditEscalatePostMutation = (options?: Partial<Options<EscalateAuditApiV1PriceAuditEscalatePostData>>): UseMutationOptions<EscalateAuditApiV1PriceAuditEscalatePostResponse, AxiosError<EscalateAuditApiV1PriceAuditEscalatePostError>, Options<EscalateAuditApiV1PriceAuditEscalatePostData>> => {
    const mutationOptions: UseMutationOptions<EscalateAuditApiV1PriceAuditEscalatePostResponse, AxiosError<EscalateAuditApiV1PriceAuditEscalatePostError>, Options<EscalateAuditApiV1PriceAuditEscalatePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await escalateAuditApiV1PriceAuditEscalatePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const setDeadlineApiV1PriceAuditSetDeadlinePostQueryKey = (options: Options<SetDeadlineApiV1PriceAuditSetDeadlinePostData>) => createQueryKey('setDeadlineApiV1PriceAuditSetDeadlinePost', options);

/**
 * Set Deadline
 * Set deadline for an audit record.
 */
export const setDeadlineApiV1PriceAuditSetDeadlinePostOptions = (options: Options<SetDeadlineApiV1PriceAuditSetDeadlinePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await setDeadlineApiV1PriceAuditSetDeadlinePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: setDeadlineApiV1PriceAuditSetDeadlinePostQueryKey(options)
    });
};

/**
 * Set Deadline
 * Set deadline for an audit record.
 */
export const setDeadlineApiV1PriceAuditSetDeadlinePostMutation = (options?: Partial<Options<SetDeadlineApiV1PriceAuditSetDeadlinePostData>>): UseMutationOptions<SetDeadlineApiV1PriceAuditSetDeadlinePostResponse, AxiosError<SetDeadlineApiV1PriceAuditSetDeadlinePostError>, Options<SetDeadlineApiV1PriceAuditSetDeadlinePostData>> => {
    const mutationOptions: UseMutationOptions<SetDeadlineApiV1PriceAuditSetDeadlinePostResponse, AxiosError<SetDeadlineApiV1PriceAuditSetDeadlinePostError>, Options<SetDeadlineApiV1PriceAuditSetDeadlinePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await setDeadlineApiV1PriceAuditSetDeadlinePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGetQueryKey = (options: Options<GetAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGetData>) => createQueryKey('getAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGet', options);

/**
 * Get Audit Statistics
 * Get audit statistics for a factory.
 */
export const getAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGetOptions = (options: Options<GetAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAuditStatisticsApiV1PriceAuditStatisticsFactoryFactoryIdGetQueryKey(options)
    });
};

export const getDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGetQueryKey = (options: Options<GetDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGetData>) => createQueryKey('getDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGet', options);

/**
 * Get Date Range Statistics
 * Get audit statistics for a date range.
 */
export const getDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGetOptions = (options: Options<GetDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDateRangeStatisticsApiV1PriceAuditStatisticsDateRangeGetQueryKey(options)
    });
};

export const getUserStatisticsApiV1PriceAuditStatisticsUserUserIdGetQueryKey = (options: Options<GetUserStatisticsApiV1PriceAuditStatisticsUserUserIdGetData>) => createQueryKey('getUserStatisticsApiV1PriceAuditStatisticsUserUserIdGet', options);

/**
 * Get User Statistics
 * Get audit statistics for a specific user.
 */
export const getUserStatisticsApiV1PriceAuditStatisticsUserUserIdGetOptions = (options: Options<GetUserStatisticsApiV1PriceAuditStatisticsUserUserIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUserStatisticsApiV1PriceAuditStatisticsUserUserIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUserStatisticsApiV1PriceAuditStatisticsUserUserIdGetQueryKey(options)
    });
};

export const getFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGetQueryKey = (options: Options<GetFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGetData>) => createQueryKey('getFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGet', options);

/**
 * Get Financial Impact Summary
 * Get financial impact summary.
 */
export const getFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGetOptions = (options: Options<GetFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getFinancialImpactSummaryApiV1PriceAuditStatisticsFinancialImpactGetQueryKey(options)
    });
};

export const getTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGetQueryKey = (options: Options<GetTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGetData>) => createQueryKey('getTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGet', options);

/**
 * Get Top Error Types
 * Get top error types by frequency.
 */
export const getTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGetOptions = (options: Options<GetTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTopErrorTypesApiV1PriceAuditStatisticsErrorTypesGetQueryKey(options)
    });
};

export const getTopReportersApiV1PriceAuditStatisticsReportersGetQueryKey = (options: Options<GetTopReportersApiV1PriceAuditStatisticsReportersGetData>) => createQueryKey('getTopReportersApiV1PriceAuditStatisticsReportersGet', options);

/**
 * Get Top Reporters
 * Get top users by number of reports.
 */
export const getTopReportersApiV1PriceAuditStatisticsReportersGetOptions = (options: Options<GetTopReportersApiV1PriceAuditStatisticsReportersGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTopReportersApiV1PriceAuditStatisticsReportersGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTopReportersApiV1PriceAuditStatisticsReportersGetQueryKey(options)
    });
};

export const getReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGetQueryKey = (options: Options<GetReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGetData>) => createQueryKey('getReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGet', options);

/**
 * Get Reviewer Performance
 * Get reviewer performance statistics.
 */
export const getReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGetOptions = (options: Options<GetReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getReviewerPerformanceApiV1PriceAuditStatisticsReviewerPerformanceGetQueryKey(options)
    });
};

export const getEscalationStatisticsApiV1PriceAuditStatisticsEscalationGetQueryKey = (options: Options<GetEscalationStatisticsApiV1PriceAuditStatisticsEscalationGetData>) => createQueryKey('getEscalationStatisticsApiV1PriceAuditStatisticsEscalationGet', options);

/**
 * Get Escalation Statistics
 * Get escalation statistics.
 */
export const getEscalationStatisticsApiV1PriceAuditStatisticsEscalationGetOptions = (options: Options<GetEscalationStatisticsApiV1PriceAuditStatisticsEscalationGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEscalationStatisticsApiV1PriceAuditStatisticsEscalationGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEscalationStatisticsApiV1PriceAuditStatisticsEscalationGetQueryKey(options)
    });
};

export const getDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGetQueryKey = (options: Options<GetDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGetData>) => createQueryKey('getDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGet', options);

/**
 * Get Deadline Compliance
 * Get deadline compliance statistics.
 */
export const getDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGetOptions = (options: Options<GetDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDeadlineComplianceApiV1PriceAuditStatisticsDeadlineComplianceGetQueryKey(options)
    });
};

export const getAuditDashboardApiV1PriceAuditDashboardGetQueryKey = (options: Options<GetAuditDashboardApiV1PriceAuditDashboardGetData>) => createQueryKey('getAuditDashboardApiV1PriceAuditDashboardGet', options);

/**
 * Get Audit Dashboard
 * Get comprehensive audit dashboard data.
 */
export const getAuditDashboardApiV1PriceAuditDashboardGetOptions = (options: Options<GetAuditDashboardApiV1PriceAuditDashboardGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAuditDashboardApiV1PriceAuditDashboardGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAuditDashboardApiV1PriceAuditDashboardGetQueryKey(options)
    });
};

export const getAuditReportApiV1PriceAuditReportGetQueryKey = (options: Options<GetAuditReportApiV1PriceAuditReportGetData>) => createQueryKey('getAuditReportApiV1PriceAuditReportGet', options);

/**
 * Get Audit Report
 * Get comprehensive audit report for a date range.
 */
export const getAuditReportApiV1PriceAuditReportGetOptions = (options: Options<GetAuditReportApiV1PriceAuditReportGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAuditReportApiV1PriceAuditReportGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAuditReportApiV1PriceAuditReportGetQueryKey(options)
    });
};

export const getAuditSummaryApiV1PriceAuditRecordIdSummaryGetQueryKey = (options: Options<GetAuditSummaryApiV1PriceAuditRecordIdSummaryGetData>) => createQueryKey('getAuditSummaryApiV1PriceAuditRecordIdSummaryGet', options);

/**
 * Get Audit Summary
 * Get audit record summary.
 */
export const getAuditSummaryApiV1PriceAuditRecordIdSummaryGetOptions = (options: Options<GetAuditSummaryApiV1PriceAuditRecordIdSummaryGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAuditSummaryApiV1PriceAuditRecordIdSummaryGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAuditSummaryApiV1PriceAuditRecordIdSummaryGetQueryKey(options)
    });
};

export const rootGetQueryKey = (options?: Options<RootGetData>) => createQueryKey('rootGet', options);

/**
 * Root
 * Root endpoint.
 */
export const rootGetOptions = (options?: Options<RootGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await rootGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: rootGetQueryKey(options)
    });
};

export const healthCheckHealthGetQueryKey = (options?: Options<HealthCheckHealthGetData>) => createQueryKey('healthCheckHealthGet', options);

/**
 * Health Check
 * Health check endpoint.
 */
export const healthCheckHealthGetOptions = (options?: Options<HealthCheckHealthGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await healthCheckHealthGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: healthCheckHealthGetQueryKey(options)
    });
};