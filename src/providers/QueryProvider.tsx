import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import type { ReactNode } from 'react';
import { queryClient } from '../lib/queryClient';
import { useGlobalErrorHandler } from '../lib/useGlobalErrorHandler';

interface QueryProviderProps {
  children: ReactNode;
}

function QueryProviderContent({ children }: QueryProviderProps) {
  // 启用全局错误处理
  useGlobalErrorHandler();
  
  return (
    <>
      {children}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </>
  );
}

export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <QueryProviderContent>{children}</QueryProviderContent>
    </QueryClientProvider>
  );
}
