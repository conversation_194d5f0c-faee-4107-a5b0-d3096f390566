import { createContext } from 'react'
import type { UserResponseDto, CurrentUserDto } from '@/services/types.gen'

// Extended user interface with permissions
export interface AuthUser extends UserResponseDto {
  permissions?: string[]
  role?: string
}

// Auth context interface
export interface AuthContextType {
  // User state
  user: CurrentUserDto | null
  isAuthenticated: boolean
  isLoading: boolean
  error: Error | null
  
  // Auth method        s
  login: (token: string) => void
  logout: () => void
  refreshUser: () => void
  
  // Permission methods
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
//   hasRole: (role: string) => boolean
//   hasAnyRole: (roles: string[]) => boolean
  isSuperuser: () => boolean
}

// Create auth context
export const AuthContext = createContext<AuthContextType | undefined>(undefined)
