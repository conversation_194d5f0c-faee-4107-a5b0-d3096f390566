import { useContext, useState, useEffect, useCallback, type ReactNode, useMemo } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { readUsersMeApiV1AuthMeGetOptions } from '@/services/@tanstack/react-query.gen'
import type { CurrentUserDto } from '@/services/types.gen'
import { AuthContext, type AuthContextType } from './auth'

// Auth provider props
interface AuthProviderProps {
  children: ReactNode
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false)
  const queryClient = useQueryClient()

  // Check initial auth state
  useEffect(() => {
    const token = localStorage.getItem('access_token')
    const authState = localStorage.getItem('isAuthenticated')
    setIsAuthenticated(!!(token && authState === 'true'))
  }, [])

  // Logout method with useCallback to fix dependency warning
  const logout = useCallback(() => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('isAuthenticated')
    setIsAuthenticated(false)
    queryClient.clear() // Clear all cached data
  }, [queryClient])

  // Fetch current user information
  const { 
    data: userInfo, 
    isLoading, 
    error,
    refetch: refreshUser
  } = useQuery({
    ...readUsersMeApiV1AuthMeGetOptions(),
    enabled: isAuthenticated,
    retry: false,
  })


  // Effect to handle auth errors
  useEffect(() => {
    if (error && isAuthenticated) {
      logout()
    }
  }, [error, isAuthenticated, logout])

  // Create auth user object with permissions
  const user: CurrentUserDto | null = useMemo(() => {
    if (!userInfo) return null

    return userInfo
  }, [userInfo])

  // Login method
  const login = (token: string) => {
    localStorage.setItem('access_token', token)
    localStorage.setItem('isAuthenticated', 'true')
    setIsAuthenticated(true)
  }

  // Permission checking methods
  const hasPermission = (permission: string): boolean => {
    if (!user) return false
    if (user.is_superuser) return true // Superuser has all permissions
    return user.permissions?.includes(permission) || false
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return permissions.some(permission => user.permissions?.includes(permission))
  }

  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return permissions.every(permission => user.permissions?.includes(permission))
  }

  const isSuperuser = (): boolean => {
    return user?.is_superuser || false
  }

  const contextValue: AuthContextType = {
    // User state
    user,
    isAuthenticated,
    isLoading,
    error: error as Error | null,
    
    // Auth methods
    login,
    logout,
    refreshUser,
    
    // Permission methods
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isSuperuser,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Permission guard component
interface PermissionGuardProps {
  children: ReactNode
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  fallback?: ReactNode
}

export function PermissionGuard({
  children,
  permission,
  permissions,
  requireAll = false,
  fallback = null
}: PermissionGuardProps) {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('PermissionGuard must be used within an AuthProvider')
  }

  const auth = context

  // Check single permission
  if (permission && !auth.hasPermission(permission)) {
    return <>{fallback}</>
  }

  // Check multiple permissions
  if (permissions) {
    const hasAccess = requireAll 
      ? auth.hasAllPermissions(permissions)
      : auth.hasAnyPermission(permissions)
    
    if (!hasAccess) {
      return <>{fallback}</>
    }
  }

  return <>{children}</>
}
