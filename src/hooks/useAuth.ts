import { useContext } from 'react'
import { AuthContext, type AuthContextType } from '@/contexts/auth'

// Custom hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for permission checking
export function usePermissions() {
  const auth = useAuth()
  
  return {
    hasPermission: auth.hasPermission,
    hasAnyPermission: auth.hasAnyPermission,
    hasAllPermissions: auth.hasAllPermissions,
    isSuperuser: auth.isSuperuser,
  }
}
