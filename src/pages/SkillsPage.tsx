import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Plus, Search, Filter, Edit2, Trash2, Users, MoreHorizontal } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from 'sonner'
import AppLayout from '@/components/AppLayout'
import { SkillDialog, AssignSkillDialog, SkillUsersDialog } from '@/components/skills'
import { 
  getAllSkillsApiV1SkillsGetOptions,
  createSkillApiV1SkillsPostMutation,
  updateSkillApiV1SkillsSkillIdPutMutation,
  deleteSkillApiV1SkillsSkillIdDeleteMutation
} from '@/services/@tanstack/react-query.gen'
import type { SkillResponseDto, SkillCreateDto, SkillUpdateDto } from '@/services/types.gen'

// 技能分类选项
const SKILL_CATEGORIES = [
  { value: 'production', label: '生产技能' },
  { value: 'quality', label: '质量控制' },
  { value: 'maintenance', label: '设备维护' },
  { value: 'safety', label: '安全操作' },
  { value: 'management', label: '管理技能' },
  { value: 'technical', label: '技术技能' },
  { value: 'other', label: '其他' }
]

export default function SkillsPage() {
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isSkillDialogOpen, setIsSkillDialogOpen] = useState(false)
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [selectedSkill, setSelectedSkill] = useState<SkillResponseDto | null>(null)
  const [selectedUserSkills, setSelectedUserSkills] = useState<number[]>([])
  const [skillUsersDialogOpen, setSkillUsersDialogOpen] = useState(false)
  const [selectedSkillForUsers, setSelectedSkillForUsers] = useState<SkillResponseDto | null>(null)

  // 获取所有技能
  const { data: skillsData } = useQuery(
    getAllSkillsApiV1SkillsGetOptions({
      query: {
        search_term: searchTerm && searchTerm.trim() !== '' ? searchTerm : undefined,
        category: categoryFilter !== 'all' ? categoryFilter : undefined,
        is_active: statusFilter === 'active' ? true : (statusFilter === 'inactive' ? false : undefined),
        includes: 'users'
      }
    })
  )

  // 简化数据访问
  const skills = skillsData?.skills || []

  // 创建技能mutation
  const createSkillMutation = useMutation(createSkillApiV1SkillsPostMutation())
  
  // 更新技能mutation
  const updateSkillMutation = useMutation(updateSkillApiV1SkillsSkillIdPutMutation())
  
  // 删除技能mutation
  const deleteSkillMutation = useMutation(deleteSkillApiV1SkillsSkillIdDeleteMutation())

  // 过滤技能
  const filteredSkills = skills.filter((skill) => {
    const matchesSearch = skill.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         skill.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !categoryFilter || categoryFilter === 'all' || skill.category === categoryFilter
    const matchesStatus = !statusFilter || statusFilter === 'all' ||
                         (statusFilter === 'active' && skill.is_active) || 
                         (statusFilter === 'inactive' && !skill.is_active)
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  // 获取分类标签
  const getCategoryLabel = (category: string) => {
    return SKILL_CATEGORIES.find(c => c.value === category)?.label || category
  }

  // 处理技能提交（创建或更新）
  const handleSkillSubmit = async (data: SkillCreateDto | { id: number; data: SkillUpdateDto }) => {
    try {
      if ('id' in data) {
        // 编辑模式
        await updateSkillMutation.mutateAsync({
          path: { skill_id: data.id },
          body: data.data
        })
        toast.success('技能更新成功')
      } else {
        // 创建模式
        await createSkillMutation.mutateAsync({
          body: data
        })
        toast.success('技能创建成功')
      }
      setIsSkillDialogOpen(false)
      setSelectedSkill(null)
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
    } catch {
      toast.error(selectedSkill ? '技能更新失败' : '技能创建失败')
    }
  }

  // 处理删除技能
  const handleDeleteSkill = async (skillId: number) => {
    try {
      await deleteSkillMutation.mutateAsync({
        path: { skill_id: skillId }
      })
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
      toast.success('技能删除成功')
    } catch {
      toast.error('技能删除失败')
    }
  }

  // 打开用户技能对话框
  const handleOpenSkillUsersDialog = (skill: SkillResponseDto) => {
    setSelectedSkillForUsers(skill)
    setSkillUsersDialogOpen(true)
  }

  // 打开编辑对话框
  const openEditDialog = (skill: SkillResponseDto) => {
    setSelectedSkill(skill)
    setIsSkillDialogOpen(true)
  }

  // 打开创建对话框
  const openCreateDialog = () => {
    setSelectedSkill(null)
    setIsSkillDialogOpen(true)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 页面标题和操作按钮 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">技能管理</h1>
            <p className="text-muted-foreground">管理员工技能和技能认证</p>
          </div>
          <div className="flex gap-2">
            <AssignSkillDialog 
              open={isAssignDialogOpen} 
              onOpenChange={setIsAssignDialogOpen}
            />
            
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              新建技能
            </Button>
          </div>
        </div>

        {/* 搜索和过滤栏 */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索技能或员工..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="技能分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有分类</SelectItem>
                {SKILL_CATEGORIES.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="active">启用</SelectItem>
                <SelectItem value="inactive">禁用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 主要内容 */}
        <Card>
          <CardHeader>
            <CardTitle>技能列表</CardTitle>
          </CardHeader>
          <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <Checkbox
                          checked={selectedUserSkills.length === filteredSkills.length && filteredSkills.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedUserSkills(filteredSkills.map(skill => skill.id))
                            } else {
                              setSelectedUserSkills([])
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>技能名称</TableHead>
                      <TableHead>分类</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>掌握人数</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSkills.map((skill) => (
                      <TableRow key={skill.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedUserSkills.includes(skill.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedUserSkills([...selectedUserSkills, skill.id])
                              } else {
                                setSelectedUserSkills(selectedUserSkills.filter(id => id !== skill.id))
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{skill.name}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {getCategoryLabel(skill.category || '')}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate" title={skill.description || undefined}>
                          {skill.description || '暂无描述'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={skill.is_active ? "default" : "destructive"}>
                            {skill.is_active ? "启用" : "禁用"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="flex items-center hover:bg-blue-50"
                            onClick={() => handleOpenSkillUsersDialog(skill)}
                          >
                            <Users className="h-4 w-4 mr-1" />
                            {skill.user_skills?.length || 0}
                          </Button>
                        </TableCell>
                        <TableCell>{new Date().toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>操作</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => openEditDialog(skill)}>
                                <Edit2 className="h-4 w-4 mr-2" />
                                编辑
                              </DropdownMenuItem>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem onSelect={(e: Event) => e.preventDefault()}>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    删除
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>确认删除</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      您确定要删除技能"{skill.name}"吗？此操作无法撤销。
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDeleteSkill(skill.id)}>
                                      删除
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

        {/* 技能对话框 */}
        <SkillDialog
          open={isSkillDialogOpen}
          onOpenChange={setIsSkillDialogOpen}
          editingSkill={selectedSkill}
          onSubmit={handleSkillSubmit}
          isLoading={createSkillMutation.isPending || updateSkillMutation.isPending}
        />

        {/* 技能用户对话框 */}
        <SkillUsersDialog
          open={skillUsersDialogOpen}
          onOpenChange={setSkillUsersDialogOpen}
          skill={selectedSkillForUsers}
        />
      </div>
    </AppLayout>
  )
}
