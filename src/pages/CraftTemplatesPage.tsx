import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, FileText, Search, Edit2, Trash2 } from 'lucide-react'
import AppLayout from '@/components/AppLayout'
import CraftTemplateDetail from '@/components/CraftTemplateDetail'

interface CraftRoute {
  id: string
  code: string
  name: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  price: number
}

interface Craft {
  id: string
  craft_code: string
  craft_name: string
  order: number
  is_required: boolean
  estimated_duration_hours?: number
  craft_routes: CraftRoute[]
}

interface CraftTemplate {
  id: string
  code: string
  name: string
  part_type: string
  part_name: string
  description?: string
  crafts: Craft[]
  created_at: string
  updated_at: string
}

// 预定义的部件类型
const PART_TYPES = [
  { value: 'front', label: '前片' },
  { value: 'back', label: '后片' },
  { value: 'sleeve', label: '袖子' },
  { value: 'collar', label: '领子' },
  { value: 'pocket', label: '口袋' },
  { value: 'cuff', label: '袖口' },
  { value: 'hem', label: '下摆' },
  { value: 'other', label: '其他' }
]

// 模拟数据
const mockTemplates: CraftTemplate[] = [
  {
    id: '1',
    code: 'FRONT_BASIC',
    name: '基础前片工艺',
    part_type: 'front',
    part_name: '前片',
    description: '适用于基础款式的前片工艺模板',
    crafts: [
      {
        id: '1-1',
        craft_code: 'CUT_001',
        craft_name: '裁剪',
        order: 1,
        is_required: true,
        estimated_duration_hours: 2,
        craft_routes: [
          {
            id: '1-1-1',
            code: 'CUT_MAIN',
            name: '主体裁剪',
            skill_code: 'CUTTING',
            skill_name: '裁剪技能',
            order: 1,
            measurement_types: ['piece', 'hour'],
            registration_types: ['scan', 'manual'],
            price: 10.5
          }
        ]
      },
      {
        id: '1-2',
        craft_code: 'SEW_001',
        craft_name: '缝制',
        order: 2,
        is_required: true,
        estimated_duration_hours: 4,
        craft_routes: [
          {
            id: '1-2-1',
            code: 'SEW_EDGE',
            name: '边缘缝制',
            skill_code: 'SEWING',
            skill_name: '缝制技能',
            order: 1,
            measurement_types: ['piece'],
            registration_types: ['scan'],
            price: 15.0
          },
          {
            id: '1-2-2',
            code: 'SEW_DETAIL',
            name: '细节缝制',
            skill_code: 'SEWING',
            skill_name: '缝制技能',
            order: 2,
            measurement_types: ['piece', 'minute'],
            registration_types: ['scan', 'manual'],
            price: 20.0
          }
        ]
      }
    ],
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:20:00Z'
  },
  {
    id: '2',
    code: 'SLEEVE_STANDARD',
    name: '标准袖子工艺',
    part_type: 'sleeve',
    part_name: '袖子',
    description: '标准袖子制作工艺模板',
    crafts: [
      {
        id: '2-1',
        craft_code: 'CUT_002',
        craft_name: '袖子裁剪',
        order: 1,
        is_required: true,
        estimated_duration_hours: 1.5,
        craft_routes: [
          {
            id: '2-1-1',
            code: 'CUT_SLEEVE',
            name: '袖子裁剪',
            skill_code: 'CUTTING',
            skill_name: '裁剪技能',
            order: 1,
            measurement_types: ['piece'],
            registration_types: ['scan'],
            price: 8.0
          }
        ]
      }
    ],
    created_at: '2024-01-10T09:15:00Z',
    updated_at: '2024-01-18T16:45:00Z'
  }
]

const CraftTemplatesPage: React.FC = () => {
  const [templates] = useState<CraftTemplate[]>(mockTemplates)
  const [selectedTemplate, setSelectedTemplate] = useState<CraftTemplate | null>(
    templates.length > 0 ? templates[0] : null
  )
  const [searchTerm, setSearchTerm] = useState('')
  const [filterPartType, setFilterPartType] = useState<string>('')

  // 过滤模板
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.code.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPartType = !filterPartType || filterPartType === 'all' || template.part_type === filterPartType
    return matchesSearch && matchesPartType
  })

  const handleCreateTemplate = () => {
    // TODO: 实现创建新模板的逻辑
    console.log('Create new template')
  }

  const handleEditTemplate = (template: CraftTemplate) => {
    // TODO: 实现编辑模板的逻辑
    console.log('Edit template:', template)
  }

  const handleDeleteTemplate = (templateId: string) => {
    // TODO: 实现删除模板的逻辑
    console.log('Delete template:', templateId)
  }

  return (
    <AppLayout>
      <div className="flex h-[calc(100vh-4rem)] bg-gray-50">
        {/* Left Sidebar - Template List */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <FileText className="w-5 h-5" />
              工艺模板
            </h2>
            <Button size="sm" onClick={handleCreateTemplate}>
              <Plus className="w-4 h-4 mr-2" />
              新建
            </Button>
          </div>
          
          {/* Search */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索模板..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* Filter */}
          <Select value={filterPartType} onValueChange={setFilterPartType}>
            <SelectTrigger>
              <SelectValue placeholder="筛选部件类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              {PART_TYPES.map((partType) => (
                <SelectItem key={partType.value} value={partType.value}>
                  {partType.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Template List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-2">
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无模板数据
              </div>
            ) : (
              <div className="space-y-2">
                {filteredTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedTemplate?.id === template.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">{template.name}</h4>
                          <p className="text-xs text-muted-foreground mb-2">{template.code}</p>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="text-xs">
                              {template.part_name}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {template.crafts.length} 工艺
                            </Badge>
                          </div>
                          {template.description && (
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {template.description}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1 ml-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEditTemplate(template)
                            }}
                            className="h-6 w-6 p-0"
                          >
                            <Edit2 className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteTemplate(template.id)
                            }}
                            className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Right Content - Template Detail */}
      <div className="flex-1 flex flex-col">
        {selectedTemplate ? (
          <CraftTemplateDetail template={selectedTemplate} />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">选择工艺模板</h3>
              <p className="text-muted-foreground">
                从左侧列表中选择一个模板来查看详细信息
              </p>
            </div>
          </div>
        )}
      </div>
      </div>
    </AppLayout>
  )
}

export default CraftTemplatesPage
