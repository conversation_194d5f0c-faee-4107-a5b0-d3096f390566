import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AppLayout from "@/components/AppLayout"
import { Plus, Edit2, Trash2, Loader2 } from "lucide-react"
import { DepartmentDialog } from "@/components/departments"
import {
  getDepartmentsApiV1DepartmentsGetOptions,
  createDepartmentApiV1DepartmentsPostMutation,
  updateDepartmentApiV1DepartmentsDepartmentIdPutMutation,
  deleteDepartmentApiV1DepartmentsDepartmentIdDeleteMutation,
  readUsersMeApiV1AuthMeGetOptions
} from '@/services/@tanstack/react-query.gen'
import type { DepartmentCreateDto, DepartmentUpdateDto, DepartmentResponseDto } from '@/services/types.gen'

export default function DepartmentsPage() {
  const queryClient = useQueryClient()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<DepartmentResponseDto | null>(null)

  // Get current user for operator_id
  const { data: currentUser } = useQuery({
    ...readUsersMeApiV1AuthMeGetOptions(),
    staleTime: 300000, // 5 minutes
  })

  // Fetch departments
  const { data: departmentsData, isLoading, error } = useQuery({
    ...getDepartmentsApiV1DepartmentsGetOptions(),
    staleTime: 30000, // 30 seconds
  })

  // Create department mutation
  const createDepartmentMutation = useMutation({
    ...createDepartmentApiV1DepartmentsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getDepartmentsApiV1DepartmentsGet'] })
      setIsDialogOpen(false)
    },
    onError: (error) => {
      console.error('Failed to create department:', error)
    }
  })

  // Update department mutation
  const updateDepartmentMutation = useMutation({
    ...updateDepartmentApiV1DepartmentsDepartmentIdPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getDepartmentsApiV1DepartmentsGet'] })
      setIsDialogOpen(false)
      setEditingDepartment(null)
    },
    onError: (error) => {
      console.error('Failed to update department:', error)
    }
  })

  // Delete department mutation
  const deleteDepartmentMutation = useMutation({
    ...deleteDepartmentApiV1DepartmentsDepartmentIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getDepartmentsApiV1DepartmentsGet'] })
    },
    onError: (error) => {
      console.error('Failed to delete department:', error)
    }
  })

  const handleDepartmentSubmit = (data: DepartmentCreateDto | { id: number; data: DepartmentUpdateDto }) => {
    if ('id' in data) {
      // Update existing department
      updateDepartmentMutation.mutate({
        path: { department_id: data.id },
        body: data.data
      })
    } else {
      // Create new department
      createDepartmentMutation.mutate({ body: data })
    }
  }

  const handleEditDepartment = (department: DepartmentResponseDto) => {
    setEditingDepartment(department)
    setIsDialogOpen(true)
  }

  const handleCreateDepartment = () => {
    setEditingDepartment(null)
    setIsDialogOpen(true)
  }

  const handleDeleteDepartment = (id: number) => {
    if (currentUser?.id && confirm('确定要删除这个部门吗？')) {
      deleteDepartmentMutation.mutate({
        path: { department_id: id }
      })
    }
  }

  const departments = departmentsData?.departments || []
  const isSubmitting = createDepartmentMutation.isPending || updateDepartmentMutation.isPending

  return (
    <AppLayout>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">部门管理</h1>
          <p className="text-muted-foreground">管理服装生产各部门信息</p>
        </div>
        <Button onClick={handleCreateDepartment}>
          <Plus className="w-4 h-4 mr-2" />
          新增部门
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      ) : error ? (
        <div className="text-center py-8 text-red-500">
          加载部门数据失败，请重试
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {departments.map((department) => (
            <Card key={department.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{department.name}</CardTitle>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditDepartment(department)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteDepartment(department.id)}
                      disabled={deleteDepartmentMutation.isPending}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <CardDescription>{department.description || '暂无描述'}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">部门代码：</span>
                    <span className="text-sm font-medium">{department.code}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">部门主管：</span>
                    <span className="text-sm font-medium">{department.manager_name || '未设置'}</span>
                  </div>
                  {department.location && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">位置：</span>
                      <span className="text-sm font-medium">{department.location}</span>
                    </div>
                  )}
                  {department.phone && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">电话：</span>
                      <span className="text-sm font-medium">{department.phone}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">状态：</span>
                    <span className={`text-sm font-medium ${department.is_active ? 'text-green-600' : 'text-red-600'}`}>
                      {department.is_active ? '活跃' : '停用'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Department Dialog */}
      <DepartmentDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        editingDepartment={editingDepartment}
        onSubmit={handleDepartmentSubmit}
        isLoading={isSubmitting}
        currentUserId={currentUser?.id}
      />
    </AppLayout>
  )
}
