import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import AppLayout from "@/components/AppLayout"
import BillDetailDialog from "@/components/BillDetailDialog"
import { 
  Search, 
  Eye, 
  Edit, 
  Check, 
  X, 
  DollarSign, 
  Ban, 
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Loader2
} from "lucide-react"
import { toast } from 'sonner'
import {
  getBillsApiV1BillsGetOptions,
  submitBillForReviewApiV1BillsBillIdSubmitPostMutation,
  approveBillApiV1BillsBillIdApprovePostMutation,
  rejectBillApiV1BillsBillIdRejectPostMutation,
  markBillAsPaidApiV1BillsBillIdPayPostMutation,
  cancelBillApiV1BillsBillIdCancelPostMutation
} from '@/services/@tanstack/react-query.gen'
import type { BillResponseDto, BillStatus, PaymentMethod } from '@/services/types.gen'

// Status badge component
const BillStatusBadge = ({ status }: { status: BillStatus }) => {
  const statusConfig = {
    draft: { label: '草稿', variant: 'secondary' as const, icon: Edit },
    pending: { label: '待审核', variant: 'default' as const, icon: Clock },
    approved: { label: '已审核', variant: 'default' as const, icon: CheckCircle },
    paid: { label: '已支付', variant: 'default' as const, icon: DollarSign },
    rejected: { label: '已拒绝', variant: 'destructive' as const, icon: XCircle },
    cancelled: { label: '已取消', variant: 'secondary' as const, icon: Ban }
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="w-3 h-3" />
      {config.label}
    </Badge>
  )
}

const BillsPage: React.FC = () => {
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<BillStatus | 'all'>('all')
  const [workerFilter, setWorkerFilter] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [selectedBillId, setSelectedBillId] = useState<number | null>(null)
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)

  // Get bills list
  const { data: billsData, isLoading, error } = useQuery({
    ...getBillsApiV1BillsGetOptions({
      query: {
        status: statusFilter === 'all' ? undefined : statusFilter,
        start_date: startDate || undefined,
        end_date: endDate || undefined,
        limit: 100
      }
    })
  })

  const bills = billsData?.bills || []

  // Mutations for bill operations
  const submitBillMutation = useMutation({
    ...submitBillForReviewApiV1BillsBillIdSubmitPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getBillsApiV1BillsGet'] })
      toast.success('账单已提交审核')
    },
    onError: () => {
      toast.error('提交失败')
    }
  })

  const approveBillMutation = useMutation({
    ...approveBillApiV1BillsBillIdApprovePostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getBillsApiV1BillsGet'] })
      toast.success('账单已审核通过')
    },
    onError: () => {
      toast.error('审核失败')
    }
  })

  const rejectBillMutation = useMutation({
    ...rejectBillApiV1BillsBillIdRejectPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getBillsApiV1BillsGet'] })
      toast.success('账单已拒绝')
    },
    onError: () => {
      toast.error('拒绝失败')
    }
  })

  const payBillMutation = useMutation({
    ...markBillAsPaidApiV1BillsBillIdPayPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getBillsApiV1BillsGet'] })
      toast.success('账单已标记为已支付')
    },
    onError: () => {
      toast.error('支付标记失败')
    }
  })

  const cancelBillMutation = useMutation({
    ...cancelBillApiV1BillsBillIdCancelPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getBillsApiV1BillsGet'] })
      toast.success('账单已取消')
    },
    onError: () => {
      toast.error('取消失败')
    }
  })

  // Filter bills based on search and filters
  const filteredBills = bills.filter(bill => {
    const matchesSearch = searchTerm === '' ||
      bill.bill_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bill.worker_user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bill.worker_user?.phone?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesWorker = workerFilter === '' ||
      bill.worker_user?.name?.toLowerCase().includes(workerFilter.toLowerCase())

    // Date filtering is handled by the API query, but we can add client-side filtering as backup
    const billDate = new Date(bill.bill_date)
    const matchesStartDate = !startDate || billDate >= new Date(startDate)
    const matchesEndDate = !endDate || billDate <= new Date(endDate)

    return matchesSearch && matchesWorker && matchesStartDate && matchesEndDate
  })

  // Action handlers
  const handleSubmitBill = (billId: number) => {
    submitBillMutation.mutate({ path: { bill_id: billId }, body: {} })
  }

  const handleApproveBill = (billId: number) => {
    approveBillMutation.mutate({
      path: { bill_id: billId },
      body: { review_notes: '审核通过' }
    })
  }

  const handleRejectBill = (billId: number) => {
    rejectBillMutation.mutate({
      path: { bill_id: billId },
      body: { rejection_reason: '审核不通过' }
    })
  }

  const handlePayBill = (billId: number) => {
    payBillMutation.mutate({
      path: { bill_id: billId },
      body: { payment_method: 'bank_transfer' }
    })
  }

  const handleCancelBill = (billId: number) => {
    cancelBillMutation.mutate({
      path: { bill_id: billId },
      body: { cancellation_reason: '取消账单' }
    })
  }

  const handleViewBill = (billId: number) => {
    setSelectedBillId(billId)
    setDetailDialogOpen(true)
  }

  // Format currency
  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return `¥${num.toFixed(2)}`
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">账单管理</h1>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>账单列表</CardTitle>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索账单号、员工姓名..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as BillStatus | 'all')}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="draft">草稿</SelectItem>
                    <SelectItem value="pending">待审核</SelectItem>
                    <SelectItem value="approved">已审核</SelectItem>
                    <SelectItem value="paid">已支付</SelectItem>
                    <SelectItem value="rejected">已拒绝</SelectItem>
                    <SelectItem value="cancelled">已取消</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  placeholder="员工筛选..."
                  value={workerFilter}
                  onChange={(e) => setWorkerFilter(e.target.value)}
                  className="w-32"
                />
                <Input
                  type="date"
                  placeholder="开始日期"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-36"
                />
                <Input
                  type="date"
                  placeholder="结束日期"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-36"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                <p className="text-muted-foreground">加载中...</p>
              </div>
            ) : filteredBills.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {searchTerm || statusFilter !== 'all' || workerFilter || startDate || endDate ? '未找到匹配的账单' : '暂无账单数据'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>账单号</TableHead>
                      <TableHead>员工</TableHead>
                      <TableHead>账单日期</TableHead>
                      <TableHead>完成数量</TableHead>
                      <TableHead>工作时长</TableHead>
                      <TableHead>基础金额</TableHead>
                      <TableHead>奖金</TableHead>
                      <TableHead>扣款</TableHead>
                      <TableHead>总金额</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredBills.map((bill) => (
                      <TableRow key={bill.id}>
                        <TableCell className="font-medium">{bill.bill_no}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{bill.worker_user?.name || '-'}</div>
                            <div className="text-sm text-muted-foreground">{bill.worker_user?.phone || '-'}</div>
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(bill.bill_date)}</TableCell>
                        <TableCell>{bill.total_completed_quantity}</TableCell>
                        <TableCell>{Math.round(bill.total_work_duration_minutes / 60)}小时</TableCell>
                        <TableCell>{formatCurrency(bill.base_amount)}</TableCell>
                        <TableCell>{formatCurrency(bill.bonus_amount)}</TableCell>
                        <TableCell>{formatCurrency(bill.deduction_amount)}</TableCell>
                        <TableCell className="font-medium">{formatCurrency(bill.total_amount)}</TableCell>
                        <TableCell>
                          <BillStatusBadge status={bill.status} />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleViewBill(bill.id)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            
                            {bill.status === 'draft' && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleSubmitBill(bill.id)}
                                disabled={submitBillMutation.isPending}
                              >
                                <Check className="w-4 h-4" />
                              </Button>
                            )}
                            
                            {bill.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleApproveBill(bill.id)}
                                  disabled={approveBillMutation.isPending}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleRejectBill(bill.id)}
                                  disabled={rejectBillMutation.isPending}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                            
                            {bill.status === 'approved' && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handlePayBill(bill.id)}
                                disabled={payBillMutation.isPending}
                                className="text-blue-600 hover:text-blue-700"
                              >
                                <DollarSign className="w-4 h-4" />
                              </Button>
                            )}
                            
                            {(bill.status === 'draft' || bill.status === 'pending') && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleCancelBill(bill.id)}
                                disabled={cancelBillMutation.isPending}
                                className="text-gray-600 hover:text-gray-700"
                              >
                                <Ban className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Bill Detail Dialog */}
      <BillDetailDialog
        billId={selectedBillId}
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
      />
    </AppLayout>
  )
}

export default BillsPage
