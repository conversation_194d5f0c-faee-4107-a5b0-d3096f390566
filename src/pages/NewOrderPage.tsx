import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  createOrderApiV1OrdersPostMutation,
} from '@/services/@tanstack/react-query.gen'
import AppLayout from "@/components/AppLayout"
import { 
  OrderBaseInfo, 
  OrderLinesConfig, 
  OrderPartTypesConfig
} from '@/components/order'
import { Button } from '@/components/ui/button'
import { Save, ArrowLeft } from 'lucide-react'
import { toast } from 'sonner'

// 数据类型定义
interface OrderLineData {
  size: string
  amount: number
  notes?: string
}

interface OrderCraftRouteData {
  code: string
  name?: string
  skill_code: string
  skill_name?: string
  order: number
  measurement_types: string[]
  registration_types: string[]
  assigned_user_id?: number
  price: number
}

interface OrderCraftData {
  craft_code: string
  craft_name: string
  order: number
  is_required: boolean
  estimated_duration_hours?: number
  order_craft_routes: OrderCraftRouteData[]
}

interface PartTypeData {
  part_type: string
  part_name: string
  order_crafts: OrderCraftData[]
}

// 表单验证模式
const orderFormSchema = z.object({
  skc_no: z.string().min(1, 'SKC号是必填项'),
  external_skc_no: z.string().optional(),
  order_no: z.string().min(1, '订单号是必填项'),
  external_order_no: z.string().optional(),
  external_order_no2: z.string().optional(),
  price: z.union([z.number(), z.string()]).optional(),
  expect_finished_at: z.string().optional(),
  owner_user_id: z.number().optional(),
  notes: z.string().optional(),
  order_lines: z.array(z.object({
    size: z.string().min(1, '尺码是必填项'),
    amount: z.number().min(1, '数量必须大于0'),
    notes: z.string().optional(),
  })).min(1, '至少需要一个订单行'),
  part_types: z.array(z.object({
    part_type: z.string().min(1, '部件类型不能为空'),
    part_name: z.string().min(1, '部件名称不能为空'),
    order_crafts: z.array(z.object({
      craft_code: z.string().min(1, '工序编码不能为空'),
      craft_name: z.string().min(1, '工序不能为空'),
      order: z.number().min(1, '顺序必须大于0'),
      is_required: z.boolean(),
      estimated_duration_hours: z.number().optional(),
      order_craft_routes: z.array(z.object({
        code: z.string().min(1, '工艺路线代码不能为空'),
        name: z.string().optional(),
        skill_code: z.string().min(1, '技能代码不能为空'),
        skill_name: z.string().optional(),
        order: z.number().min(1, '顺序必须大于0'),
        measurement_types: z.array(z.string()),
        registration_types: z.array(z.string()),
        assigned_user_id: z.number().optional(),
        price: z.number().min(0, '价格不能小于0')
      }))
    }))
  })).optional()
})

type OrderFormData = z.infer<typeof orderFormSchema>

const NewOrderPage: React.FC = () => {
  const navigate = useNavigate()
  
  // 初始化表单
  const form = useForm<OrderFormData>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      skc_no: '',
      order_no: '',
      external_skc_no: '',
      external_order_no: '',
      external_order_no2: '',
      price: '',
      expect_finished_at: '',
      owner_user_id: undefined,
      notes: '',
      order_lines: [],
      part_types: []
    }
  })

  // 创建订单
  const createOrderMutation = useMutation({
    ...createOrderApiV1OrdersPostMutation(),
    onSuccess: () => {
      toast.success('订单创建成功!')
      navigate('/orders')
    },
    onError: (error) => {
      console.error('创建订单失败:', error)
      toast.error('创建订单失败，请检查输入信息')
    }
  })

  const handleSaveOrder = form.handleSubmit((formData) => {
    // 表单验证已通过，直接构造请求数据
    const orderData = {
      body: {
        skc_no: formData.skc_no,
        external_skc_no: formData.external_skc_no || null,
        order_no: formData.order_no,
        external_order_no: formData.external_order_no || null,
        external_order_no2: formData.external_order_no2 || null,
        cost: null, // 成本移除，设为null
        price: formData.price ? (typeof formData.price === 'string' ? parseFloat(formData.price) : formData.price) : null,
        expect_finished_at: formData.expect_finished_at || null,
        owner_user_id: formData.owner_user_id || null,
        description: null, // 描述和备注合并，使用notes字段
        notes: formData.notes || null,
        order_lines: formData.order_lines.map((line: OrderLineData) => ({
          size: line.size,
          amount: line.amount,
          notes: line.notes || null
        })),
        // 将part_types转换为order_crafts结构
        order_crafts: formData.part_types && formData.part_types.length > 0 
          ? formData.part_types.flatMap((partType: PartTypeData) => 
              partType.order_crafts?.map((craft: OrderCraftData) => ({
                craft_code: craft.craft_code,
                craft_name: craft.craft_name,
                order: craft.order,
                is_required: craft.is_required,
                estimated_duration_hours: craft.estimated_duration_hours,
                order_craft_routes: craft.order_craft_routes,
                part_type: partType.part_type // 添加部件类型标识
              })) || []
            ) 
          : null
      }
    }

    createOrderMutation.mutate(orderData)
  })

  const handleCancel = () => {
    navigate('/orders')
  }

  return (
    <AppLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6 flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">新建订单</h1>
            <p className="text-muted-foreground mt-1">
              创建新的服装生产订单，配置基础信息、订单行和工艺流程
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              取消
            </Button>
            <Button onClick={handleSaveOrder} disabled={createOrderMutation.isPending}>
              <Save className="w-4 h-4 mr-2" />
              {createOrderMutation.isPending ? '保存中...' : '保存订单'}
            </Button>
          </div>
        </div>

        <FormProvider {...form}>
          <div className="space-y-8">
            {/* 基础信息 */}
            <OrderBaseInfo />

            {/* 订单行配置 */}
            <OrderLinesConfig />

            {/* 部件工艺配置 */}
            <OrderPartTypesConfig />
          </div>
        </FormProvider>
      </div>
    </AppLayout>
  )
}

export default NewOrderPage