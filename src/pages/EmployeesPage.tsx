import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import AppLayout from "@/components/AppLayout"
import CreateUserDialog from "@/components/CreateUserDialog"
import { Plus, Trash2, User, Settings, Search, UserMinus } from "lucide-react"
import { 
  getAvailableUsersApiV1UserManagementAvailableUsersGetOptions,
  createUserWithFactoryApiV1UserManagementCreateUserPostMutation,
  bindUserRoleApiV1UserManagementBindRolesPostMutation,
  suspendUserInFactoryApiV1UserManagementSuspendPostMutation,
  removeUserFromFactoryApiV1UserManagementRemoveUserDeleteMutation,
  getAllRolesApiV1RolesGetOptions,
  getDepartmentsApiV1DepartmentsGetOptions
} from '@/services/@tanstack/react-query.gen'
import type { UserSummaryDto, CreateUserWithFactoryDto, AvailableUsersDto } from '@/services/types.gen'
import {toast} from 'sonner'

export default function EmployeesPage() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserSummaryDto | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRoleFilter, setSelectedRoleFilter] = useState<string>('all')
  const [selectedDepartmentFilter, setSelectedDepartmentFilter] = useState<string>('all')
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null)

  const queryClient = useQueryClient()

  // Fetch users list
  const { data: usersData, isLoading: isUsersLoading } = useQuery({
    ...getAvailableUsersApiV1UserManagementAvailableUsersGetOptions({
      query: {
        search_term: searchTerm || undefined,
        role_id: selectedRoleFilter !== 'all' ? parseInt(selectedRoleFilter) : undefined,
        department_id: selectedDepartmentFilter !== 'all' ? parseInt(selectedDepartmentFilter) : undefined,
        is_active: true
      }
    }),
    staleTime: 30000,
  }) as { data: AvailableUsersDto | undefined, isLoading: boolean }

  // Fetch roles for filtering and assignment
  const { data: rolesData } = useQuery({
    ...getAllRolesApiV1RolesGetOptions(),
    staleTime: 300000, // 5 minutes
  })

  const { data: departmentData } = useQuery({
    ...getDepartmentsApiV1DepartmentsGetOptions(),
    staleTime: 300000, // 5 minutes
  })

  // Mutations
  const createUserMutation = useMutation({
    ...createUserWithFactoryApiV1UserManagementCreateUserPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAvailableUsersApiV1UserManagementAvailableUsersGet'] })
      toast.success('用户创建成功')
      setIsAddModalOpen(false)
    },
    onError: (error) => {
      toast.error(`创建用户失败: ${error.message}`)
    }
  })

  const bindRoleMutation = useMutation({
    ...bindUserRoleApiV1UserManagementBindRolesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAvailableUsersApiV1UserManagementAvailableUsersGet'] })
      toast.success('角色绑定成功')
      setIsRoleModalOpen(false)
      setSelectedUser(null)
      setSelectedRoleId(null)
    },
    onError: (error) => {
      toast.error(`角色绑定失败: ${error.message}`)
    }
  })

  const suspendUserMutation = useMutation({
    ...suspendUserInFactoryApiV1UserManagementSuspendPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAvailableUsersApiV1UserManagementAvailableUsersGet'] })
      toast.success('用户已暂停')
    },
    onError: (error) => {
      toast.error(`暂停用户失败: ${error.message}`)
    }
  })

  const removeUserMutation = useMutation({
    ...removeUserFromFactoryApiV1UserManagementRemoveUserDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAvailableUsersApiV1UserManagementAvailableUsersGet'] })
      toast.success('用户已移除')
    },
    onError: (error) => {
      toast.error(`移除用户失败: ${error.message}`)
    }
  })

  const handleCreateUser = (data: CreateUserWithFactoryDto) => {
    createUserMutation.mutate({
      body: data
    })
  }

  const handleBindRole = () => {
    if (!selectedUser || !selectedRoleId) {
      alert('请选择用户和角色')
      return
    }

    bindRoleMutation.mutate({
      body: {
        user_id: selectedUser.id,
        role_id: selectedRoleId
      }
    })
  }

  const handleSuspendUser = (userId: number) => {
    if (confirm('确定要暂停此用户吗？')) {
      suspendUserMutation.mutate({
        body: { user_id: userId }
      })
    }
  }

  const handleRemoveUser = (userId: number) => {
    if (confirm('确定要从工厂中移除此用户吗？')) {
      removeUserMutation.mutate({
        body: { user_id: userId }
      })
    }
  }

  const openRoleModal = (user: UserSummaryDto) => {
    setSelectedUser(user)
    setIsRoleModalOpen(true)
  }

  const getStatusColor = (status?: string | null) => {
    switch (status?.toUpperCase()) {
      case 'APPROVED': return 'bg-green-100 text-green-800'
      case 'SUSPENDED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status?: string | null) => {
    switch (status?.toUpperCase()) {
      case 'APPROVED': return '已批准'
      case 'SUSPENDED': return '已暂停'
      case 'PENDING': return '待审批'
      default: return status
    }
  }

  return (
    <AppLayout>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">用户管理</h1>
          <p className="text-muted-foreground">管理工厂内的用户信息和权限</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            添加用户
          </Button>
        </div>
      </div>

      {/* Filter Section */}
      <div className="mb-6 flex items-center space-x-4">
        <Select value={selectedRoleFilter} onValueChange={setSelectedRoleFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="筛选角色" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有角色</SelectItem>
            {rolesData?.roles?.map((role) => (
              <SelectItem key={role.id} value={role.id.toString()}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedDepartmentFilter} onValueChange={setSelectedDepartmentFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="筛选部门" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有部门</SelectItem>
            {departmentData?.departments?.map((department) => (
              <SelectItem key={department.id} value={department.id + ''}>
                {department.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索用户..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* Users List */}
      {isUsersLoading ? (
        <div className="text-center py-8">加载中...</div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {usersData?.users?.map((factoryUser) => (
            <Card key={factoryUser.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full">
                      <User className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{factoryUser.full_name || factoryUser.username}</CardTitle>
                      <CardDescription>{factoryUser.username}</CardDescription>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => openRoleModal(factoryUser)}
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleSuspendUser(factoryUser.id)}
                    >
                      <UserMinus className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleRemoveUser(factoryUser.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">邮箱：</span>
                    <span className="text-sm font-medium">{factoryUser.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">手机号:</span>
                    <span className="text-sm font-medium">{factoryUser.phone}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">状态：</span>
                    <Badge className={getStatusColor(factoryUser.status)}>
                      {getStatusText(factoryUser.status)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">角色：</span>
                    {factoryUser.role ?<Badge>
                      {factoryUser.role?.name}
                    </Badge>: <div />}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">账户状态：</span>
                    <Badge variant={factoryUser.is_active ? "default" : "secondary"}>
                      {factoryUser.is_active ? '激活' : '禁用'}
                    </Badge>
                  </div>
                  {factoryUser.skill_count !== undefined && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">技能数量：</span>
                      <span className="text-sm font-medium">
                        {factoryUser.skill_count}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create User Dialog */}
      <CreateUserDialog
        open={isAddModalOpen}
        onOpenChange={setIsAddModalOpen}
        onSubmit={handleCreateUser}
        isLoading={createUserMutation.isPending}
      />

      {/* Role Binding Modal */}
      <Dialog open={isRoleModalOpen} onOpenChange={setIsRoleModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>分配角色</DialogTitle>
            <DialogDescription>
              为用户 {selectedUser?.full_name || selectedUser?.username} 分配角色
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="role-select">选择角色</Label>
              <Select value={selectedRoleId?.toString()} onValueChange={(value: string) => setSelectedRoleId(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择角色" />
                </SelectTrigger>
                <SelectContent>
                  {rolesData?.roles?.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleBindRole} disabled={!selectedRoleId}>
              分配角色
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}
