import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { getAllOrdersApiV1OrdersGetOptions } from '@/services/@tanstack/react-query.gen'
import AppLayout from '@/components/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Plus, Search, Eye, Edit, Trash2 } from 'lucide-react'
import { toast } from 'sonner'

const OrderListPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')

  // 获取订单列表
  const { data: {orders} = {orders: []}, isLoading, error } = useQuery({
    ...getAllOrdersApiV1OrdersGetOptions()
  })

  const handleCreateOrder = () => {
    navigate('/orders/new')
  }

  const handleViewOrder = (orderNo: string) => {
    navigate(`/orders/${orderNo}`)
  }

  const handleEditOrder = (orderNo: string) => {
    navigate(`/orders/${orderNo}/edit`)
  }

  const handleDeleteOrder = (orderId: number) => {
    // TODO: Implement delete functionality
    toast.success(`删除订单 ${orderId}`)
  }

  // 过滤订单
  const filteredOrders = orders?.filter(order => 
    order.order_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.skc_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (order.external_order_no && order.external_order_no.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || []

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending': { label: '待处理', variant: 'secondary' as const },
      'in_progress': { label: '进行中', variant: 'default' as const },
      'completed': { label: '已完成', variant: 'default' as const },
      'cancelled': { label: '已取消', variant: 'destructive' as const }
    }
    
    return statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'outline' as const }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const formatPrice = (price: string | null | undefined) => {
    return price ? `¥${parseFloat(price).toFixed(2)}` : '-'
  }

  if (error) {
    return (
      <AppLayout>
        <div className="container mx-auto py-6">
          <div className="text-center">
            <p className="text-red-500">加载订单列表失败</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6 flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
            <p className="text-muted-foreground mt-1">
              管理所有生产订单，查看订单状态和进度
            </p>
          </div>
          <Button onClick={handleCreateOrder}>
            <Plus className="w-4 h-4 mr-2" />
            新建订单
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>订单列表</CardTitle>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索订单号、SKC号..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">加载中...</p>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {searchTerm ? '未找到匹配的订单' : '暂无订单数据'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>订单号</TableHead>
                      <TableHead>SKC号</TableHead>
                      <TableHead>外部订单号</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>价格</TableHead>
                      <TableHead>负责人</TableHead>
                      <TableHead>预计完成时间</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="w-[150px]">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredOrders.map((order) => {
                      const statusConfig = getStatusBadge(order.status)
                      return (
                        <TableRow key={order.id}>
                          <TableCell className="font-mono">
                            {order.order_no}
                          </TableCell>
                          <TableCell className="font-mono">
                            {order.skc_no}
                          </TableCell>
                          <TableCell className="font-mono">
                            {order.external_order_no || '-'}
                          </TableCell>
                          <TableCell>
                            <Badge variant={statusConfig.variant}>
                              {statusConfig.label}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatPrice(order.price)}
                          </TableCell>
                          <TableCell>
                            {order.owner_user_id || '-'}
                          </TableCell>
                          <TableCell>
                            {order.expect_finished_at ? formatDate(order.expect_finished_at) : '-'}
                          </TableCell>
                          <TableCell>
                            {formatDate(order.created_at)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleViewOrder(order.order_no)}
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleEditOrder(order.order_no)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleDeleteOrder(order.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}

export default OrderListPage