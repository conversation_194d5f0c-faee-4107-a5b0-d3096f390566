import { useState, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Plus, Search, Edit2, Trash2, MoreHorizontal } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { toast } from 'sonner'
import AppLayout from '@/components/AppLayout'
import CraftDialog from '@/components/CraftDialog'
import CraftRouteDialog from '@/components/CraftRouteDialog'
import EditableCraftRouteTable from '@/components/EditableCraftRouteTable'
import { 
  getAllCraftsApiV1CraftsGetOptions,
  createCraftApiV1CraftsPostMutation,
  updateCraftApiV1CraftsCraftIdPutMutation,
  deleteCraftApiV1CraftsCraftIdDeleteMutation,
  getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetOptions,
  createCraftRouteApiV1CraftsRoutesPostMutation,
  updateCraftRouteApiV1CraftsRoutesRouteIdPutMutation,
  deleteCraftRouteApiV1CraftsRoutesRouteIdDeleteMutation,
  reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostMutation,
  getAllSkillsApiV1SkillsGetOptions
} from '@/services/@tanstack/react-query.gen'
import type { 
  CraftResponseDto, 
  CraftCreateDto, 
  CraftUpdateDto, 
  CraftRouteDetailDto, 
  CraftRouteCreateDto, 
  CraftRouteUpdateDto
} from '@/services/types.gen'

export default function CraftsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [showCraftDialog, setShowCraftDialog] = useState(false)
  const [showRouteDialog, setShowRouteDialog] = useState(false)
  const [editingCraft, setEditingCraft] = useState<CraftResponseDto | null>(null)
  const [editingRoute, setEditingRoute] = useState<CraftRouteDetailDto | null>(null)
  const [activeTab, setActiveTab] = useState<string>('')

  const queryClient = useQueryClient()

  // 获取所有工艺
  const { data: craftsData, isLoading: craftsLoading } = useQuery(
    getAllCraftsApiV1CraftsGetOptions({
      query: { search_term: searchTerm }
    })
  )

  // 获取所有技能
  const { data: skillsData } = useQuery(
    getAllSkillsApiV1SkillsGetOptions()
  )

  // 简化数据访问
  const crafts = useMemo(() => craftsData?.crafts || [], [craftsData?.crafts])
  const skills = skillsData?.skills || []

  // 创建工艺mutation
  const createCraftMutation = useMutation({
    mutationFn: createCraftApiV1CraftsPostMutation().mutationFn,
    onSuccess: (data) => {
      toast.success('工艺创建成功')
      queryClient.invalidateQueries({ queryKey: ['getAllCraftsApiV1CraftsGet'] })
      setShowCraftDialog(false)
      setActiveTab(data.code)
    },
    onError: () => {
      toast.error('创建失败')
    }
  })

  // 更新工艺mutation
  const updateCraftMutation = useMutation({
    mutationFn: updateCraftApiV1CraftsCraftIdPutMutation().mutationFn,
    onSuccess: () => {
      toast.success('工艺更新成功')
      queryClient.invalidateQueries({ queryKey: ['getAllCraftsApiV1CraftsGet'] })
      setShowCraftDialog(false)
      setEditingCraft(null)
    },
    onError: () => {
      toast.error('更新失败')
    }
  })

  // 创建工艺路线mutation
  const createRouteMutation = useMutation({
    mutationFn: createCraftRouteApiV1CraftsRoutesPostMutation().mutationFn,
    onSuccess: () => {
      toast.success('工艺路线创建成功')
      queryClient.invalidateQueries({ 
        queryKey: ['getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet'] 
      })
      setShowRouteDialog(false)
    },
    onError: () => {
      toast.error('创建失败')
    }
  })

  // 更新工艺路线mutation
  const updateRouteMutation = useMutation({
    mutationFn: updateCraftRouteApiV1CraftsRoutesRouteIdPutMutation().mutationFn,
    onSuccess: () => {
      toast.success('工艺路线更新成功')
      queryClient.invalidateQueries({ 
        queryKey: ['getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet'] 
      })
      setShowRouteDialog(false)
      setEditingRoute(null)
    },
    onError: () => {
      toast.error('更新失败')
    }
  })

  // 获取优先级标签
  const getPriorityBadge = (priority: number | undefined) => {
    const p = priority || 1
    if (p >= 8) return <Badge variant="destructive">高</Badge>
    if (p >= 5) return <Badge variant="default">中</Badge>
    return <Badge variant="secondary">低</Badge>
  }

  // 处理工艺提交（创建或更新）
  const handleCraftSubmit = async (data: CraftCreateDto | { id: number; data: CraftUpdateDto }) => {
    try {
      if ('id' in data) {
        // 编辑模式
        await updateCraftMutation.mutateAsync({
          path: { craft_id: data.id },
          body: data.data
        })
      } else {
        // 创建模式
        await createCraftMutation.mutateAsync({
          body: data
        })
      }
    } catch {
      // Error handling is done in mutation onError
    }
  }

  // 处理工艺路线提交（创建或更新）
  const handleRouteSubmit = async (data: CraftRouteCreateDto | { id: number; data: CraftRouteUpdateDto }) => {
    try {
      if ('id' in data) {
        // 编辑模式
        await updateRouteMutation.mutateAsync({
          path: { route_id: data.id },
          body: data.data
        })
      } else {
        // 创建模式
        await createRouteMutation.mutateAsync({
          body: data
        })
      }
    } catch {
      // Error handling is done in mutation onError
    }
  }

  // CraftCard 组件 - 显示单个工艺及其路线
  function CraftCard({ craft }: { craft: CraftResponseDto }) {
    // 获取当前工艺的路线数据
    const { data: routesData, isLoading: routesLoading } = useQuery({
      ...getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetOptions({
        path: { craft_code: craft.code }
      })
    })

    const routes = routesData?.routes || []

    // 处理编辑工艺
    const handleEditCraft = () => {
      setEditingCraft(craft)
      setShowCraftDialog(true)
    }

    // 删除工艺路线mutation
    const deleteRouteMutation = useMutation({
      mutationFn: deleteCraftRouteApiV1CraftsRoutesRouteIdDeleteMutation().mutationFn,
      onSuccess: () => {
        toast.success('工艺路线删除成功')
        queryClient.invalidateQueries({ 
          queryKey: ['getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet', { path: { craft_code: craft.code } }] 
        })
      },
      onError: () => {
        toast.error('删除失败')
      }
    })

    // 删除工艺mutation
    const deleteCraftMutation = useMutation({
      mutationFn: deleteCraftApiV1CraftsCraftIdDeleteMutation().mutationFn,
      onSuccess: () => {
        toast.success('工艺删除成功')
        queryClient.invalidateQueries({ queryKey: ['getAllCraftsApiV1CraftsGet'] })
      },
      onError: () => {
        toast.error('删除失败')
      }
    })

    // 重新排序工艺路线
    const reorderRoutesMutation = useMutation({
      mutationFn: reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostMutation().mutationFn,
      onSuccess: () => {
        toast.success('工艺路线排序成功')
        queryClient.invalidateQueries({ 
          queryKey: ['getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet', { path: { craft_code: craft.code } }] 
        })
      },
      onError: () => {
        toast.error('排序失败')
      }
    })

    return (
      <Card className="overflow-hidden">
        {/* 工艺基本信息 */}
        <CardHeader className="bg-gray-50 border-b">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <CardTitle className="text-xl">{craft.name}</CardTitle>
                <Badge variant="outline" className="text-sm">{craft.code}</Badge>
                {getPriorityBadge(craft.priority)}
                {!craft.enabled && <Badge variant="destructive">停用</Badge>}
              </div>
              {craft.description && (
                <p className="text-gray-600 max-w-2xl">{craft.description}</p>
              )}
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>工艺操作</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleEditCraft}>
                  <Edit2 className="mr-2 h-4 w-4" />
                  编辑工艺
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem 
                      className="text-red-600"
                      onSelect={(e) => e.preventDefault()}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除工艺
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认删除</AlertDialogTitle>
                      <AlertDialogDescription>
                        确定要删除工艺 "{craft.name}" 吗？此操作无法撤销，同时会删除所有相关的工艺路线。
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={() => {
                          deleteCraftMutation.mutate({
                            path: { craft_id: craft.id }
                          })
                        }}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        {/* 工艺路线 */}
        <CardContent className="p-6 pt-0">
          <EditableCraftRouteTable
            routes={routes}
            skills={skills}
            craftCode={craft.code}
            onRouteUpdate={async (routeId: number, data: CraftRouteUpdateDto) => {
              await updateRouteMutation.mutateAsync({
                path: { route_id: routeId },
                body: data
              })
            }}
            onRouteCreate={async (data: CraftRouteCreateDto) => {
              await createRouteMutation.mutateAsync({
                body: data
              })
            }}
            onRouteDelete={async (routeId: number) => {
              await deleteRouteMutation.mutateAsync({
                path: { route_id: routeId }
              })
            }}
            onRouteReorder={async (routeId: number, direction: 'up' | 'down') => {
              const currentIndex = routes.findIndex(r => r.id === routeId)
              if (currentIndex === -1) return

              const newOrder = direction === 'up' ? currentIndex : currentIndex + 2
              await reorderRoutesMutation.mutateAsync({
                path: { craft_code: craft.code },
                body: [
                  {
                    route_id: routeId,
                    new_order: newOrder
                  }
                ]
              })
            }}
            isLoading={routesLoading}
          />
        </CardContent>
      </Card>
    )
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">工艺路线管理</h1>
            <p className="text-muted-foreground">管理工艺和工艺路线配置</p>
          </div>
          <Button 
            onClick={() => {
              setEditingCraft(null)
              setShowCraftDialog(true)
            }}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            新建工艺
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索工艺名称或编码..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* 工艺管理 */}
        {craftsLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        ) : crafts.length === 0 ? (
          <Card>
            <CardContent className="text-center py-16">
              <div className="text-gray-400 mb-6">
                <Plus className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无工艺</h3>
              <p className="text-gray-600 mb-6">创建第一个工艺开始管理工艺路线</p>
              <Button 
                onClick={() => {
                  setEditingCraft(null)
                  setShowCraftDialog(true)
                }}
                className="bg-blue-600 hover:bg-blue-700"
                size="lg"
              >
                <Plus className="h-5 w-5 mr-2" />
                新建工艺
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-8">
            {/* 工艺列表 - 每个工艺显示为大卡片，包含所有路线 */}
            {crafts.map((craft) => (
              <CraftCard key={craft.code} craft={craft} />
            ))}
          </div>
        )}

        {/* 工艺对话框 */}
        <CraftDialog
          open={showCraftDialog}
          onOpenChange={setShowCraftDialog}
          editingCraft={editingCraft}
          onSubmit={handleCraftSubmit}
          isLoading={createCraftMutation.isPending || updateCraftMutation.isPending}
        />

        {/* 工艺路线对话框 */}
        <CraftRouteDialog
          open={showRouteDialog}
          onOpenChange={setShowRouteDialog}
          editingRoute={editingRoute}
          craftCode={activeTab}
          skills={skills}
          maxOrder={0}
          onSubmit={handleRouteSubmit}
          isLoading={createRouteMutation.isPending || updateRouteMutation.isPending}
        />
      </div>
    </AppLayout>
  )
}
