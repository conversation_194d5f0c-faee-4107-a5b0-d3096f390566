import { useState, useMemo } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import AppLayout from "@/components/AppLayout"
import AuditRecordDetailDialog from "@/components/AuditRecordDetailDialog"
import { 
  Search, 
  Eye, 
  Check, 
  X, 
  AlertTriangle,
  TrendingUp,
  Filter,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Loader2,
  FileText,
  DollarSign
} from "lucide-react"
import { toast } from 'sonner'
import { formatDate } from '@/lib/dateUtils'
import {
  searchAuditRecordsApiV1PriceAuditSearchPostOptions,
  performAuditActionApiV1PriceAuditActionPostMutation,
  bulkAuditActionApiV1PriceAuditBulkActionPostMutation
} from '@/services/@tanstack/react-query.gen'
import type { 
  CraftRoutePriceAuditListDto,
  CraftRoutePriceAuditSearchDto,
  PriceAuditAction,
  PriceErrorType
} from '@/services/types.gen'

// Status badge component
const AuditStatusBadge = ({ action, isResolved }: { action: PriceAuditAction, isResolved: boolean }) => {
  const statusConfig = {
    submitted: { label: '已提交', variant: 'secondary' as const, icon: FileText },
    under_review: { label: '审核中', variant: 'default' as const, icon: Eye },
    approved: { label: '已批准', variant: 'default' as const, icon: Check },
    rejected: { label: '已拒绝', variant: 'destructive' as const, icon: X },
    price_corrected: { label: '价格已更正', variant: 'default' as const, icon: DollarSign },
    withdrawn: { label: '已撤销', variant: 'secondary' as const, icon: X }
  }

  const config = statusConfig[action]
  const Icon = config.icon

  return (
    <Badge variant={isResolved ? 'default' : config.variant} className="flex items-center gap-1">
      <Icon size={12} />
      {config.label}
      {isResolved && ' (已解决)'}
    </Badge>
  )
}

// Error type badge component
const ErrorTypeBadge = ({ errorType }: { errorType: PriceErrorType | null | undefined }) => {
  if (!errorType) return <span className="text-gray-400">-</span>

  const errorConfig = {
    price_too_high: { label: '价格过高', variant: 'destructive' as const },
    price_too_low: { label: '价格过低', variant: 'destructive' as const },
    wrong_price: { label: '价格错误', variant: 'destructive' as const },
    missing_price: { label: '缺少价格', variant: 'outline' as const },
    calculation_error: { label: '计算错误', variant: 'secondary' as const },
    other: { label: '其他', variant: 'outline' as const }
  }

  const config = errorConfig[errorType]
  return <Badge variant={config.variant}>{config.label}</Badge>
}

// Priority badge component
const PriorityBadge = ({ priority }: { priority: string }) => {
  const priorityConfig = {
    low: { label: '低', variant: 'secondary' as const },
    medium: { label: '中', variant: 'default' as const },
    high: { label: '高', variant: 'destructive' as const },
    critical: { label: '紧急', variant: 'destructive' as const }
  }

  const config = priorityConfig[priority as keyof typeof priorityConfig] || 
                  { label: priority, variant: 'outline' as const }
  
  return <Badge variant={config.variant}>{config.label}</Badge>
}

export default function PriceAuditPage() {
  const [searchParams, setSearchParams] = useState<CraftRoutePriceAuditSearchDto>({
    factory_id: 1, // This should come from context/auth
    skip: 0,
    limit: 20
  })
  const [selectedRecords, setSelectedRecords] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<CraftRoutePriceAuditListDto | null>(null)
  const [showDetailDialog, setShowDetailDialog] = useState(false)

  // Query for audit records
  const { data: auditRecords = [], isLoading, error, refetch } = useQuery(
    searchAuditRecordsApiV1PriceAuditSearchPostOptions({
      body: searchParams
    })
  )

  // Mutations
  const performActionMutation = useMutation(performAuditActionApiV1PriceAuditActionPostMutation())
  const bulkActionMutation = useMutation(bulkAuditActionApiV1PriceAuditBulkActionPostMutation())

  // Handlers
  const handleSearch = () => {
    setSearchParams(prev => ({
      ...prev,
      search_term: searchTerm || undefined,
      skip: 0
    }))
  }

  const handleFilter = (filters: Partial<CraftRoutePriceAuditSearchDto>) => {
    setSearchParams(prev => ({
      ...prev,
      ...filters,
      skip: 0
    }))
  }

  const handlePageChange = (newSkip: number) => {
    setSearchParams(prev => ({
      ...prev,
      skip: newSkip
    }))
  }

  const handleSelectRecord = (recordId: number, selected: boolean) => {
    if (selected) {
      setSelectedRecords(prev => [...prev, recordId])
    } else {
      setSelectedRecords(prev => prev.filter(id => id !== recordId))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedRecords(auditRecords.map(record => record.id))
    } else {
      setSelectedRecords([])
    }
  }

  const handlePerformAction = async (recordId: number, action: PriceAuditAction, reason?: string) => {
    try {
      await performActionMutation.mutateAsync({
        body: {
          record_id: recordId,
          action,
          user_id: 1, // This should come from context/auth
          reason
        }
      })
      toast.success('操作成功')
      refetch()
    } catch {
      toast.error('操作失败')
    }
  }

  const handleBulkAction = async (action: PriceAuditAction, reason?: string) => {
    if (selectedRecords.length === 0) {
      toast.error('请选择要操作的记录')
      return
    }

    try {
      await bulkActionMutation.mutateAsync({
        body: {
          record_ids: selectedRecords,
          action,
          user_id: 1, // This should come from context/auth
          reason
        }
      })
      toast.success(`批量操作成功，处理了 ${selectedRecords.length} 条记录`)
      setSelectedRecords([])
      refetch()
    } catch {
      toast.error('批量操作失败')
    }
  }

  const handleViewRecord = (record: CraftRoutePriceAuditListDto) => {
    setSelectedRecord(record)
    setShowDetailDialog(true)
  }

  // Statistics
  const statistics = useMemo(() => {
    const total = auditRecords.length
    const resolved = auditRecords.filter(record => record.is_resolved).length
    const highPriority = auditRecords.filter(record => record.priority_level === 'high' || record.priority_level === 'critical').length
    const escalated = auditRecords.filter(record => record.escalation_level > 0).length

    return { total, resolved, highPriority, escalated }
  }, [auditRecords])

  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-red-500">
                <AlertTriangle className="mx-auto mb-2" size={48} />
                <p>加载审核记录失败</p>
                <Button onClick={() => refetch()} className="mt-2">
                  重试
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">价格审核</h1>
            <p className="text-muted-foreground">
              工艺路线价格错误审核管理
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总记录数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已解决</CardTitle>
              <Check className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statistics.resolved}</div>
              <p className="text-xs text-muted-foreground">
                {statistics.total > 0 ? `${Math.round((statistics.resolved / statistics.total) * 100)}%` : '0%'}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">高优先级</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statistics.highPriority}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已升级</CardTitle>
              <TrendingUp className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{statistics.escalated}</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>搜索和筛选</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="搜索审核记录..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                搜索
              </Button>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">审核状态</label>
                  <Select 
                    value={searchParams.audit_action || ''} 
                    onValueChange={(value) => handleFilter({ audit_action: value as PriceAuditAction || undefined })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部</SelectItem>
                      <SelectItem value="submitted">已提交</SelectItem>
                      <SelectItem value="under_review">审核中</SelectItem>
                      <SelectItem value="approved">已批准</SelectItem>
                      <SelectItem value="rejected">已拒绝</SelectItem>
                      <SelectItem value="price_corrected">价格已更正</SelectItem>
                      <SelectItem value="withdrawn">已撤销</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">错误类型</label>
                  <Select 
                    value={searchParams.error_type || ''} 
                    onValueChange={(value) => handleFilter({ error_type: value as PriceErrorType || undefined })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择错误类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部</SelectItem>
                      <SelectItem value="price_too_high">价格过高</SelectItem>
                      <SelectItem value="price_too_low">价格过低</SelectItem>
                      <SelectItem value="wrong_price">价格错误</SelectItem>
                      <SelectItem value="missing_price">缺少价格</SelectItem>
                      <SelectItem value="calculation_error">计算错误</SelectItem>
                      <SelectItem value="other">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">优先级</label>
                  <Select 
                    value={searchParams.priority_level || ''} 
                    onValueChange={(value) => handleFilter({ priority_level: value || undefined })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部</SelectItem>
                      <SelectItem value="low">低</SelectItem>
                      <SelectItem value="medium">中</SelectItem>
                      <SelectItem value="high">高</SelectItem>
                      <SelectItem value="critical">紧急</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">解决状态</label>
                  <Select 
                    value={searchParams.is_resolved === undefined || searchParams.is_resolved === null ? '' : searchParams.is_resolved.toString()} 
                    onValueChange={(value) => handleFilter({ is_resolved: value === '' ? undefined : value === 'true' })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择解决状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部</SelectItem>
                      <SelectItem value="false">未解决</SelectItem>
                      <SelectItem value="true">已解决</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedRecords.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  已选择 {selectedRecords.length} 条记录
                </span>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleBulkAction('approved', '批量批准')}
                  >
                    <Check className="mr-1 h-4 w-4" />
                    批量批准
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleBulkAction('rejected', '批量拒绝')}
                  >
                    <X className="mr-1 h-4 w-4" />
                    批量拒绝
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedRecords([])}
                  >
                    取消选择
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Audit Records Table */}
        <Card>
          <CardHeader>
            <CardTitle>审核记录</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : auditRecords.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无审核记录
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedRecords.length === auditRecords.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>审核ID</TableHead>
                      <TableHead>工艺路线实例</TableHead>
                      <TableHead>错误类型</TableHead>
                      <TableHead>原价格</TableHead>
                      <TableHead>报告价格</TableHead>
                      <TableHead>更正价格</TableHead>
                      <TableHead>财务影响</TableHead>
                      <TableHead>优先级</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>升级级别</TableHead>
                      <TableHead>报告时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {auditRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedRecords.includes(record.id)}
                            onCheckedChange={(checked) => handleSelectRecord(record.id, !!checked)}
                          />
                        </TableCell>
                        <TableCell className="font-mono">{record.id}</TableCell>
                        <TableCell className="font-mono">{record.order_craft_route_instance_id}</TableCell>
                        <TableCell>
                          <ErrorTypeBadge errorType={record.error_type} />
                        </TableCell>
                        <TableCell>{record.original_price || '-'}</TableCell>
                        <TableCell>{record.reported_price || '-'}</TableCell>
                        <TableCell>{record.corrected_price || '-'}</TableCell>
                        <TableCell className={record.financial_impact ? 'text-red-600 font-medium' : ''}>
                          {record.financial_impact || '-'}
                        </TableCell>
                        <TableCell>
                          <PriorityBadge priority={record.priority_level} />
                        </TableCell>
                        <TableCell>
                          <AuditStatusBadge action={record.audit_action} isResolved={record.is_resolved} />
                        </TableCell>
                        <TableCell>
                          {record.escalation_level > 0 ? (
                            <Badge variant="outline">L{record.escalation_level}</Badge>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell>
                          {formatDate(record.reported_at)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handlePerformAction(record.id, 'approved', '手动批准')}
                              disabled={record.is_resolved || performActionMutation.isPending}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handlePerformAction(record.id, 'rejected', '手动拒绝')}
                              disabled={record.is_resolved || performActionMutation.isPending}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleViewRecord(record)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    显示 {searchParams.skip! + 1} - {Math.min(searchParams.skip! + searchParams.limit!, searchParams.skip! + auditRecords.length)} 条记录
                  </div>
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handlePageChange(Math.max(0, searchParams.skip! - searchParams.limit!))}
                      disabled={searchParams.skip === 0}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      上一页
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handlePageChange(searchParams.skip! + searchParams.limit!)}
                      disabled={auditRecords.length < searchParams.limit!}
                    >
                      下一页
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Audit Record Detail Dialog */}
        <AuditRecordDetailDialog
          record={selectedRecord}
          open={showDetailDialog}
          onOpenChange={setShowDetailDialog}
          onRecordUpdated={() => refetch()}
        />
      </div>
    </AppLayout>
  )
}
