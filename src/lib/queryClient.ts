import { QueryClient } from '@tanstack/react-query';
import { isAuthError } from './authInterceptor';

// Helper function to check if error is axios error
const isAxiosError = (error: Error): error is Error & { response?: { status: number } } => {
  return 'response' in error;
};

// Create a client with default options
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: Error) => {
        // Don't retry on 401 (unauthorized) or 403 (forbidden)
        if (isAxiosError(error) && error.response) {
          const status = error.response.status;
          if (status === 401 || status === 403) {
            return false;
          }
        }
        // Also check using our auth error detector
        if (isAuthError(error)) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: (failureCount, error: Error) => {
        // Don't retry mutations on client errors (4xx)
        if (isAxiosError(error) && error.response) {
          const status = error.response.status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        // Also check using our auth error detector
        if (isAuthError(error)) {
          return false;
        }
        return failureCount < 2;
      },
    },
  },
});
