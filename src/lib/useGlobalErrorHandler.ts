import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { handleGlobalError } from './authInterceptor';

/**
 * 全局错误处理Hook
 * 监听React Query的错误事件并处理401认证错误
 */
export const useGlobalErrorHandler = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    // 监听查询错误
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'observerResultsUpdated') {
        const { query } = event;
        if (query.state.error) {
          handleGlobalError(query.state.error);
        }
      }
    });

    // 监听变更错误
    const unsubscribeMutation = queryClient.getMutationCache().subscribe((event) => {
      if (event.type === 'updated') {
        const { mutation } = event;
        if (mutation.state.error) {
          handleGlobalError(mutation.state.error);
        }
      }
    });

    return () => {
      unsubscribe();
      unsubscribeMutation();
    };
  }, [queryClient]);
};
