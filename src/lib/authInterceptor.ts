/**
 * 处理401认证错误，重定向到登录页面并保存返回URL和工厂ID
 */
export const handleAuthError = () => {
  // 获取当前完整URL
  const currentUrl = window.location.href;
  const currentPath = window.location.pathname;
  
  // 如果当前URL已经是登录页面，避免递归重定向
  if (currentUrl.includes('/login') || currentPath === '/login') {
    return;
  }
  
  // 清除认证信息
  localStorage.removeItem('access_token');
  localStorage.removeItem('isAuthenticated');
  
  // 获取当前路径和查询参数作为返回地址
  const returnUrl = window.location.pathname + window.location.search;
  
  // 从localStorage或其他地方获取factory_id（这里假设存储在localStorage中）
  const factoryId = localStorage.getItem('factory_id') || '';
  
  // 构建登录页面URL参数
  const loginUrl = new URL('/login', window.location.origin);
  loginUrl.searchParams.set('returnUrl', returnUrl);
  
  if (factoryId) {
    loginUrl.searchParams.set('factory_id', factoryId);
  }
  
  // 重定向到登录页面
  window.location.href = loginUrl.toString();
};

/**
 * 检查错误是否为401认证错误
 */
export const isAuthError = (error: unknown): boolean => {
  if (!error || typeof error !== 'object') return false;
  
  // 检查不同可能的错误结构
  const err = error as Record<string, unknown>;
  return (
    err.status === 401 ||
    (err.response as Record<string, unknown>)?.status === 401 ||
    err.statusCode === 401
  );
};

/**
 * 全局错误处理函数，可在React Query错误回调中使用
 */
export const handleGlobalError = (error: unknown) => {
  if (isAuthError(error)) {
    console.log('检测到401认证错误，重定向到登录页面');
    handleAuthError();
  }
};
