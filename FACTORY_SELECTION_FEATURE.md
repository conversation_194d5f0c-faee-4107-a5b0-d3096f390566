# 登录页面工厂选择功能 (Factory Selection Feature)

## 功能概述

在用户成功登录后，系统现在会显示一个工厂选择界面，让用户选择要登录的工厂。这个功能支持以下场景：

### 📋 功能特性

1. **自动检测工厂数量**
   - 如果用户只有一个工厂权限：显示5秒倒计时自动进入
   - 如果用户有多个工厂权限：显示工厂列表供用户选择
   - 如果用户没有工厂权限：显示错误提示

2. **用户体验优化**
   - 单工厂用户可以立即点击按钮跳过倒计时
   - 多工厂用户可以看到清晰的工厂列表
   - 支持返回登录页面重新登录

3. **错误处理**
   - 工厂切换失败时显示错误提示
   - 网络异常时的重试机制

## 🔄 登录流程

```
用户输入凭据 
    ↓
登录验证成功，获取Token
    ↓
切换到工厂选择界面
    ↓
获取用户可用工厂列表
    ↓
根据工厂数量显示不同界面：
    ├── 0个工厂: 显示错误提示
    ├── 1个工厂: 显示倒计时自动进入
    └── 多个工厂: 显示选择列表
    ↓
用户选择工厂或自动选择
    ↓
调用工厂切换API
    ↓
跳转到目标页面
```

## 🎨 界面设计

### 单工厂界面
- 显示工厂图标和名称
- 5秒倒计时提示
- "立即进入"按钮
- "返回登录页面"选项

### 多工厂界面
- 工厂列表，每个工厂显示：
  - 工厂图标
  - 工厂名称
  - 工厂代码
- 点击任意工厂卡片即可选择
- "返回登录页面"选项

### 无工厂权限界面
- 错误提示信息
- 联系管理员的建议
- "返回登录页面"选项

## 🔧 技术实现

### 状态管理
```typescript
type LoginStep = 'login' | 'factory-selection'
const [loginStep, setLoginStep] = useState<LoginStep>('login')
const [factoryCountdown, setFactoryCountdown] = useState(0)
```

### API集成
- `getMyAvailableFactoriesApiV1SessionMyFactoriesGet` - 获取用户可用工厂
- `switchFactoryContextApiV1SessionSwitchFactoryPost` - 切换当前工厂

### 自动跳转逻辑
```typescript
useEffect(() => {
  if (loginStep === 'factory-selection' && factoriesData?.factories) {
    const factories = factoriesData.factories
    
    if (factories.length === 1) {
      // 启动5秒倒计时
      setFactoryCountdown(5)
      const timer = setInterval(() => {
        setFactoryCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            handleFactorySelection(factories[0])
            return 0
          }
          return prev - 1
        })
      }, 1000)
      
      return () => clearInterval(timer)
    }
  }
}, [loginStep, factoriesData, handleFactorySelection])
```

## 🛡️ 安全考虑

1. **权限验证**：只有登录成功后才能访问工厂选择界面
2. **Token管理**：在工厂切换前确保Token有效
3. **错误处理**：工厂切换失败时的安全回退

## 🧪 测试场景

### 单工厂用户测试
1. 登录成功后应该看到工厂选择界面
2. 显示倒计时（5秒）
3. 可以点击"立即进入"跳过倒计时
4. 倒计时结束后自动进入工厂

### 多工厂用户测试
1. 登录成功后看到工厂列表
2. 每个工厂显示名称和代码
3. 点击任意工厂可以成功切换
4. 切换失败时显示错误提示

### 无工厂权限测试
1. 显示无权限错误提示
2. 提供联系管理员的建议
3. 可以返回登录页面

### 返回登录功能测试
1. 点击"返回登录页面"清除认证状态
2. 清除表单数据
3. 刷新验证码
4. 返回到登录界面

## 📱 响应式设计

- 界面适配移动端和桌面端
- 工厂卡片在小屏幕上适当调整
- 按钮和文本大小适配不同设备

## 🔮 未来扩展

1. **工厂搜索**：当工厂数量很多时添加搜索功能
2. **工厂分组**：按地区或类型对工厂进行分组
3. **最近使用**：记住用户最后使用的工厂
4. **工厂信息**：显示更多工厂详细信息
5. **快捷切换**：在系统内部快速切换工厂

## 📝 使用说明

### 开发者
1. 确保后端API返回正确的工厂列表
2. 测试不同工厂数量的场景
3. 验证工厂切换API的响应

### 测试人员
1. 测试各种用户权限组合
2. 验证倒计时功能的准确性
3. 测试网络异常情况
4. 确认返回登录功能正常

### 最终用户
1. 登录后选择要使用的工厂
2. 单工厂用户可等待自动进入或手动跳过
3. 多工厂用户从列表中选择目标工厂
4. 如需更换账号可返回登录页面

这个功能极大地改善了多工厂用户的登录体验，同时保持了单工厂用户的便利性。
