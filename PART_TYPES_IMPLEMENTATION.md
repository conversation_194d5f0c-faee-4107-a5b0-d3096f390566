# 订单部件工艺配置功能实现

## 概述

根据您的需求，我已经将原本的单一工艺配置模式改为支持多部件类型的工艺配置模式。现在用户可以：

1. 选择不同的部件类型（前片、后片、袖子、领子等）
2. 为每个部件类型单独配置工艺流程
3. 每个部件可以有多个工艺编辑卡片

## 主要变更

### 1. 新建组件

#### `OrderPartTypesConfig.tsx`
- 主要的部件工艺配置组件
- 支持多个部件类型的选择和管理
- 使用Tab界面显示不同部件的工艺配置

#### `PartTypeCraftRouteTable.tsx`
- 专门处理部件工艺路线表格的组件
- 支持嵌套的表单字段路径

#### `PartTypeCraftRouteRow.tsx`
- 部件工艺路线行组件
- 支持编辑工艺路线的详细信息

### 2. 数据结构变更

#### 原结构：
```typescript
order_crafts: OrderCraft[]
```

#### 新结构：
```typescript
part_types: {
  part_type: string
  part_name: string  
  order_crafts: OrderCraft[]
}[]
```

### 3. 预定义部件类型

支持以下部件类型：
- 前片 (front)
- 后片 (back)
- 袖子 (sleeve)
- 领子 (collar)
- 口袋 (pocket)
- 袖口 (cuff)
- 下摆 (hem)
- 其他 (other)

## 功能特点

### 1. 部件选择
- 用户从下拉菜单选择部件类型
- 点击"添加部件"按钮添加到配置中
- 防止重复添加相同部件类型

### 2. Tab界面
- 每个部件类型显示为一个Tab
- Tab标题显示部件名称和工艺数量
- 可以轻松切换查看不同部件的配置

### 3. 工艺管理
- 每个部件可以独立添加工艺
- 支持工艺导入功能
- 可以删除单个工艺或整个部件

### 4. 数据转换
- 表单提交时自动将部件结构转换为后端所需的工艺结构
- 为每个工艺添加部件类型标识

## 使用方式

1. 在新建订单页面，用户首先填写基础信息和订单行
2. 在"部件工艺配置"区域：
   - 选择部件类型（如"前片"）
   - 点击"添加部件"
   - 切换到对应的Tab
   - 点击"添加工艺"导入或创建工艺
   - 配置工艺路线的详细信息
3. 重复上述步骤为其他部件添加工艺
4. 保存订单时，系统自动整合所有部件的工艺数据

## 技术实现

- 使用React Hook Form的`useFieldArray`管理动态表单数组
- 使用Tailwind CSS实现现代化UI界面
- 集成现有的工艺导入对话框
- 支持完整的CRUD操作（创建、读取、更新、删除）
- 类型安全的TypeScript实现

这样的实现让用户可以更清晰地组织不同部件的工艺流程，提供了更好的用户体验和数据管理能力。
