# 验证码功能实现说明

## 概述

已成功为登录页面添加了图片验证码功能，使用 `/api/v1/auth/generate-image-code` 接口生成验证码图片。

## 实现细节

### 1. 新增的 State 变量
```typescript
const [imageCode, setImageCode] = useState('')  // 用户输入的验证码
const [captchaData, setCaptchaData] = useState<ImageCodeResponseDto | null>(null)  // 验证码数据
```

### 2. 验证码生成
- 使用 `generateImageCodeApiV1AuthGenerateImageCodePostMutation` 生成验证码
- 页面加载时自动生成验证码
- 登录失败时自动刷新验证码

### 3. 验证码显示
- 验证码图片显示在输入框右侧
- 支持点击图片刷新验证码
- 提供专门的"刷新"按钮

### 4. 登录流程更新
- 登录 API 更新为 `loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostMutation`
- 登录时需要提供四个参数：
  - `phone`: 手机号
  - `password`: 密码
  - `image_code`: 用户输入的验证码
  - `session_id`: 验证码会话ID

## 用户界面改进

### 验证码输入区域
- 验证码输入框与验证码图片并排显示
- 验证码图片可点击刷新
- 刷新按钮提供额外的刷新方式

### 错误处理
- 登录失败时显示更详细的错误信息
- 验证码加载失败时显示相应提示
- 自动刷新验证码以提升用户体验

### 表单验证
- 只有在验证码数据加载成功后才能提交表单
- 所有必填字段都完整后才能登录

## 技术实现

### API 类型定义
```typescript
// 验证码响应数据
export type ImageCodeResponseDto = {
  session_id: string;
  image_base64: string;
  expires_in_seconds: number;
}

// 登录请求数据
export type PhonePasswordLoginDto = {
  phone: string;
  password: string;
  image_code: string;
  session_id: string;
}
```

### 关键功能函数
```typescript
// 刷新验证码
const refreshCaptcha = () => {
  generateCaptchaMutation.mutate({
    body: {} // API 不需要请求体参数
  })
}

// 处理登录
const handleSubmit = (e: React.FormEvent) => {
  e.preventDefault()
  
  if (phone && password && imageCode && captchaData) {
    loginMutation.mutate({
      body: {
        phone,
        password,
        image_code: imageCode,
        session_id: captchaData.session_id,
      },
    })
  }
}
```

## 用户体验优化

1. **自动加载验证码**: 页面打开时自动生成验证码
2. **错误处理**: 登录失败时自动刷新验证码
3. **视觉反馈**: 加载状态、错误状态的清晰提示
4. **便捷操作**: 支持点击图片或按钮刷新验证码
5. **响应式设计**: 验证码区域在不同屏幕尺寸下正常显示

## 注意事项

- 验证码有过期时间（由 `expires_in_seconds` 字段指定）
- 每次登录失败后都会自动刷新验证码
- 验证码图片使用 Base64 编码显示
- 确保在提交表单前验证码数据已经加载完成
