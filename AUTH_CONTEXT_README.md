# 认证与权限系统 (Authentication & Permission System)

本项目实现了一个完整的基于React Context的认证与权限管理系统。

## 架构概览

### 核心文件
- `src/contexts/auth.ts` - 认证上下文类型定义
- `src/contexts/AuthContext.tsx` - 认证上下文提供者和权限守卫组件
- `src/hooks/useAuth.ts` - 认证相关自定义Hook

## 主要功能

### 1. 用户认证管理
- 用户登录状态管理
- Token存储和验证
- 自动登出处理
- 用户信息获取和缓存

### 2. 权限控制系统
- 基于权限的访问控制
- 基于角色的访问控制
- 超级管理员特权处理
- 灵活的权限检查方法

### 3. React组件集成
- 权限守卫组件
- 自定义Hook
- TypeScript类型安全

## 使用方法

### 1. 应用配置

在应用根组件中包装AuthProvider：

```tsx
import { AuthProvider } from './contexts/AuthContext'

function App() {
  return (
    <QueryProvider>
      <AuthProvider>
        <Router>
          {/* 您的应用内容 */}
        </Router>
      </AuthProvider>
    </QueryProvider>
  )
}
```

### 2. 使用认证Hook

```tsx
import { useAuth } from '@/hooks/useAuth'

function UserProfile() {
  const { user, isAuthenticated, logout } = useAuth()
  
  if (!isAuthenticated) {
    return <div>请先登录</div>
  }
  
  return (
    <div>
      <h1>欢迎, {user?.full_name}</h1>
      <button onClick={logout}>登出</button>
    </div>
  )
}
```

### 3. 使用权限Hook

```tsx
import { usePermissions } from '@/hooks/useAuth'

function UserManagement() {
  const { hasPermission, hasRole, isSuperuser } = usePermissions()
  
  return (
    <div>
      {hasPermission('user:create') && (
        <button>创建用户</button>
      )}
      
      {hasRole('管理员') && (
        <button>管理员功能</button>
      )}
      
      {isSuperuser() && (
        <button>超级管理员功能</button>
      )}
    </div>
  )
}
```

### 4. 使用权限守卫组件

```tsx
import { PermissionGuard } from '@/contexts/AuthContext'

function App() {
  return (
    <div>
      {/* 单个权限检查 */}
      <PermissionGuard 
        permission="user:create"
        fallback={<div>无权限访问</div>}
      >
        <CreateUserButton />
      </PermissionGuard>
      
      {/* 多个权限检查 (任一权限) */}
      <PermissionGuard 
        permissions={['order:view', 'order:create']}
        requireAll={false}
        fallback={<div>需要订单相关权限</div>}
      >
        <OrderManagement />
      </PermissionGuard>
      
      {/* 多个权限检查 (全部权限) */}
      <PermissionGuard 
        permissions={['user:view', 'user:create', 'user:edit']}
        requireAll={true}
        fallback={<div>需要完整用户管理权限</div>}
      >
        <FullUserManagement />
      </PermissionGuard>
      
      {/* 角色检查 */}
      <PermissionGuard 
        role="管理员"
        fallback={<div>仅管理员可见</div>}
      >
        <AdminPanel />
      </PermissionGuard>
      
      {/* 多角色检查 */}
      <PermissionGuard 
        roles={['管理员', '超级管理员']}
        fallback={<div>需要管理员权限</div>}
      >
        <AdvancedFeatures />
      </PermissionGuard>
    </div>
  )
}
```

## API参考

### AuthContextType 接口

```typescript
interface AuthContextType {
  // 用户状态
  user: AuthUser | null
  isAuthenticated: boolean
  isLoading: boolean
  error: Error | null
  
  // 认证方法
  login: (token: string) => void
  logout: () => void
  refreshUser: () => void
  
  // 权限方法
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
  hasRole: (role: string) => boolean
  hasAnyRole: (roles: string[]) => boolean
  isSuperuser: () => boolean
}
```

### AuthUser 接口

```typescript
interface AuthUser extends UserResponseDto {
  permissions?: string[]  // 用户权限列表
  role?: string          // 用户角色
}
```

### PermissionGuard 组件Props

```typescript
interface PermissionGuardProps {
  children: ReactNode
  permission?: string        // 单个权限检查
  permissions?: string[]     // 多个权限检查
  requireAll?: boolean      // 是否需要所有权限 (默认: false)
  role?: string            // 单个角色检查
  roles?: string[]         // 多个角色检查
  fallback?: ReactNode     // 无权限时显示的内容
}
```

## 权限格式建议

建议使用 "资源:操作" 的格式定义权限：

```
user:create    - 创建用户
user:view      - 查看用户
user:edit      - 编辑用户
user:delete    - 删除用户

order:create   - 创建订单
order:view     - 查看订单
order:edit     - 编辑订单
order:delete   - 删除订单

department:*   - 部门所有权限
*:view         - 所有资源的查看权限
```

## 特殊权限处理

### 超级管理员
- `is_superuser` 为 true 的用户自动拥有所有权限
- 无需单独配置权限列表

### 权限继承
系统支持通过角色进行权限继承，可以在后端API中为每个角色配置默认权限集合。

## 后端集成

### UserResponseDto 扩展

需要在后端API的UserResponseDto中添加以下字段：

```typescript
interface UserResponseDto {
  // 现有字段...
  permissions?: string[]  // 用户权限列表
  role?: string          // 用户角色名称
}
```

### 权限数据示例

```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "full_name": "管理员",
  "is_superuser": false,
  "permissions": [
    "user:create",
    "user:view", 
    "user:edit",
    "order:view",
    "order:create",
    "department:view"
  ],
  "role": "管理员"
}
```

## 错误处理

- 认证失败自动重定向到登录页
- Token过期自动清除并登出
- 权限不足显示相应提示
- 网络错误重试机制

## 测试

可以使用 `PermissionExampleComponent` 组件测试权限系统的各种功能。

## 性能优化

- 用户信息缓存
- 权限检查结果缓存
- React Query集成减少重复请求
- 懒加载权限检查

## 安全注意事项

1. **前端权限仅用于UI控制**：实际的权限验证必须在后端进行
2. **Token安全**：使用HTTPS传输，考虑Token刷新机制
3. **权限同步**：确保前后端权限定义一致
4. **审计日志**：记录关键权限操作

## 扩展功能

该系统支持以下扩展：

- 动态权限加载
- 权限缓存策略
- 多租户权限隔离
- 权限变更实时通知
- 权限使用统计
