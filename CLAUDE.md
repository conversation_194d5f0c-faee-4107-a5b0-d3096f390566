# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server (Vite)
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

### API Development
- `npm run generate-api` - Generate API client from OpenAPI spec
- `npm run watch-api` - Watch OpenAPI spec and auto-regenerate client

## Architecture Overview

### Tech Stack
- **Frontend**: React 19 + TypeScript + Vite
- **UI**: shadcn/ui components + Tailwind CSS + Radix UI
- **Routing**: React Router 7.x
- **State Management**: React Query (TanStack Query) + React Hook Form
- **HTTP Client**: Auto-generated from OpenAPI spec using @hey-api/openapi-ts

### API Architecture
- **Code Generation**: Uses `@hey-api/openapi-ts` to generate complete TypeScript client from OpenAPI spec
- **Source Spec**: `/Users/<USER>/IdeaProjects/production-ticket/openapi.json`
- **Generated Files**: All API code in `src/services/` (client, types, React Query hooks)
- **Authentication**: Bearer token stored in localStorage, automatic injection via auth callback
- **Error Handling**: Global 401 handling with redirect to login page

### Project Structure
```
src/
├── components/
│   ├── ui/              # shadcn/ui components
│   ├── order/           # Order management components
│   └── skills/          # Skill management components
├── pages/               # Page components (routing)
├── services/            # Generated API client (DO NOT EDIT MANUALLY)
├── lib/                 # Utilities and configuration
├── providers/           # React providers (Query, Auth)
└── hooks/               # Custom React hooks
```

### Key Patterns

#### API Usage
- Use generated React Query hooks from `src/services/@tanstack/react-query.gen.ts`
- All API types are auto-generated in `src/services/types.gen.ts`
- Authentication is handled automatically via localStorage token

#### Component Development
- Use shadcn/ui components from `src/components/ui/`
- Follow existing patterns in `src/components/order/` for complex feature components
- Use React Hook Form with Zod validation for forms
- Implement responsive design with Tailwind CSS

#### State Management
- React Query for server state (auto-configured in QueryProvider)
- Local component state with useState/useReducer
- Form state with React Hook Form

### Multi-language Support
- Interface is primarily in Chinese (扫非系统 - ScanPay System)
- Comments and documentation should follow existing language patterns
- This is a garment manufacturing scanning/piece-rate system

### Authentication Flow
- Login stores access_token in localStorage
- Global auth interceptor handles token injection
- 401 responses automatically clear auth and redirect to login
- Factory context management for multi-tenant support

### Important Notes
- **Never manually edit files in `src/services/`** - they are auto-generated
- Run `npm run generate-api` after OpenAPI spec changes
- Use the existing component patterns and UI library consistently
- Follow the established Chinese/English mixed documentation style