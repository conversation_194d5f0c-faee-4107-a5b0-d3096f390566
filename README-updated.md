# 扫非系统 (ScanPay System) - Web端

基于 React + TypeScript + Vite + shadcn/ui 的服装扫码计件/价系统Web端项目。

## 📋 项目概述

扫非系统是一个专为服装制造行业设计的扫码计件/价管理系统，通过扫码技术实现对生产过程的精确计量和成本核算。本项目为系统的Web管理端，提供用户管理、部门管理、员工管理等核心功能。

## ✨ 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite 6.x
- **样式**: Tailwind CSS 3.x
- **UI组件**: shadcn/ui
- **路由**: React Router 7.x
- **工具库**: clsx, tailwind-merge, class-variance-authority
- **图标**: Lucide React

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   ├── ui/             # shadcn/ui 组件
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── input.tsx
│   │   └── label.tsx
│   └── ProtectedRoute.tsx
├── pages/              # 页面组件
│   ├── LoginPage.tsx   # 登录页
│   ├── DashboardPage.tsx # 仪表板
│   ├── DepartmentsPage.tsx # 部门管理
│   └── EmployeesPage.tsx # 员工管理
├── lib/                # 工具函数
│   └── utils.ts        # 通用工具
├── App.tsx             # 应用根组件
├── main.tsx           # 应用入口
└── index.css          # 全局样式
```

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 🔧 核心功能模块

### ✅ 已完成功能

1. **用户认证系统**
   - 登录页面（使用shadcn/ui组件）
   - 路由保护机制
   - 登录状态管理

2. **主界面布局**
   - 响应式导航栏
   - 现代化仪表板设计
   - 卡片式功能入口

3. **页面基础架构**
   - 部门管理页面框架
   - 员工管理页面框架
   - 统一的页面布局风格

### 🔄 开发中功能

1. **部门管理**
   - [ ] 部门列表展示
   - [ ] 添加/编辑/删除部门
   - [ ] 部门信息查询

2. **员工管理**
   - [ ] 员工列表展示
   - [ ] 添加/编辑/删除员工
   - [ ] 员工权限管理

### 📋 规划中功能

1. **扫码计件模块**
   - [ ] 二维码扫描界面
   - [ ] 工作量录入
   - [ ] 实时统计显示

2. **报表统计**
   - [ ] 日/周/月统计报表
   - [ ] 数据可视化图表
   - [ ] 数据导出功能

3. **系统设置**
   - [ ] 计件标准配置
   - [ ] 系统参数设置
   - [ ] 用户权限配置

## 🎨 UI设计特点

- **现代化设计**: 采用 shadcn/ui 设计系统，界面简洁美观
- **响应式布局**: 支持桌面端和移动端自适应
- **一致性**: 统一的组件样式和交互规范
- **可访问性**: 遵循 WCAG 可访问性标准

## 🔒 路由保护

系统实现了基于 localStorage 的简单认证机制：
- 未登录用户自动重定向到登录页
- 已登录用户可访问所有受保护页面
- 支持登出功能，清除认证状态

## 📝 开发规范

1. **代码风格**
   - 使用 TypeScript 进行类型安全开发
   - 遵循 React Hooks 最佳实践
   - 组件使用 PascalCase 命名
   - 文件使用 PascalCase.tsx 命名

2. **样式规范**
   - 优先使用 Tailwind CSS 工具类
   - 使用 shadcn/ui 组件库
   - 支持明暗主题切换

3. **项目配置**
   - 配置了路径别名 `@/` 指向 `src/`
   - 支持 TypeScript 严格模式
   - 使用 Vite 作为构建工具

## 🚀 部署说明

1. **构建项目**
   ```bash
   npm run build
   ```

2. **部署到静态服务器**
   - 将 `dist/` 目录上传到服务器
   - 配置服务器支持 SPA 路由

3. **环境变量配置**
   ```bash
   # 开发环境
   VITE_API_URL=http://localhost:3000/api
   
   # 生产环境
   VITE_API_URL=https://api.scanpay.com
   ```

## 📄 许可证

本项目仅供学习和开发参考使用。

## 👥 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

---

**项目状态**: 开发中 🚧
**最后更新**: 2025年6月19日
