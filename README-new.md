# 扫非系统 (ScanPay System)

服装扫码计件/价系统Web端管理平台

## 项目简介

扫非系统是一个专为服装制造业设计的扫码计件/计价管理系统，提供现代化的Web界面来管理部门、员工以及生产数据。

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **路由管理**: React Router
- **组件库**: shadcn/ui 架构
- **状态管理**: React Hooks
- **类型检查**: TypeScript

## 项目结构

```
scanpay-web/
├── src/
│   ├── pages/              # 页面组件
│   │   ├── LoginPage.tsx   # 登录页面
│   │   ├── DashboardPage.tsx # 仪表板
│   │   ├── DepartmentsPage.tsx # 部门管理
│   │   └── EmployeesPage.tsx # 员工管理
│   ├── components/         # 公共组件
│   │   └── ProtectedRoute.tsx # 路由保护
│   ├── lib/               # 工具函数
│   │   └── utils.ts       # 通用工具
│   └── assets/            # 静态资源
├── public/                # 公共资源
└── ...配置文件
```

## 功能模块

### 已完成功能

- [x] 用户登录认证
- [x] 路由保护机制
- [x] 响应式导航栏
- [x] 部门管理
  - [x] 部门列表展示
  - [x] 添加新部门
  - [x] 删除部门
- [x] 员工管理
  - [x] 员工列表展示
  - [x] 添加新员工
  - [x] 员工状态管理
  - [x] 删除员工

### 待开发功能

- [ ] 扫码功能
  - [ ] 二维码生成
  - [ ] 扫码录入
  - [ ] 计件统计
- [ ] 工作量统计
  - [ ] 日报表
  - [ ] 月报表
  - [ ] 绩效分析
- [ ] 系统设置
  - [ ] 用户权限管理
  - [ ] 系统参数配置
  - [ ] 数据备份恢复
- [ ] API集成
  - [ ] 后端接口对接
  - [ ] 数据持久化
  - [ ] 实时数据同步

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 开发规范

### 代码风格

- 使用TypeScript进行类型安全开发
- 采用函数组件和React Hooks
- 使用Tailwind CSS进行样式编写
- 保持中文界面和注释

### 组件规范

- 组件文件使用PascalCase命名
- 接口定义使用TypeScript interface
- 使用clsx和tailwind-merge处理CSS类名
- 保持组件的单一职责原则

### 提交规范

使用语义化提交信息：

- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建或辅助工具的变动

## 部署说明

### 开发环境

1. 克隆项目到本地
2. 安装依赖：`npm install`
3. 启动开发服务器：`npm run dev`
4. 在浏览器中访问 http://localhost:5173

### 生产环境

1. 构建项目：`npm run build`
2. 将`dist`目录部署到Web服务器
3. 配置服务器支持单页应用路由

## 登录说明

目前使用简单的本地存储认证机制：
- 用户名和密码可以是任意非空值
- 登录状态保存在localStorage中
- 可以通过"退出"按钮清除登录状态

## 贡献指南

1. Fork 本项目
2. 创建特性分支：`git checkout -b feature/新功能`
3. 提交更改：`git commit -m 'feat: 添加新功能'`
4. 推送到分支：`git push origin feature/新功能`
5. 提交Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过Issue或邮件联系项目维护者。
