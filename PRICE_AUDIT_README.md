# 价格审核系统 (Price Audit System)

## 概述

价格审核系统是一个用于管理工艺路线价格错误审核的Web应用程序。基于React + TypeScript + Vite + Tailwind CSS技术栈开发。

## 新增功能：价格审核页面

### 功能特点

1. **审核记录列表**
   - 分页显示价格审核记录
   - 支持多种状态过滤（已提交、审核中、已批准、已拒绝等）
   - 支持错误类型过滤（价格过高、价格过低、计算错误等）
   - 支持优先级过滤（低、中、高、紧急）
   - 支持按解决状态过滤

2. **搜索功能**
   - 关键词搜索
   - 高级筛选面板
   - 实时搜索结果更新

3. **统计概览**
   - 总记录数统计
   - 已解决记录数及百分比
   - 高优先级记录数
   - 已升级记录数

4. **批量操作**
   - 批量选择记录
   - 批量批准
   - 批量拒绝
   - 批量状态更新

5. **详细审核对话框**
   - 查看完整的审核记录信息
   - 价格对比（原价格、报告价格、更正价格）
   - 财务影响分析
   - 审核操作（批准、拒绝、价格更正）
   - 优先级设置
   - 审核升级功能

6. **状态管理**
   - 实时状态更新
   - 自动刷新功能
   - 操作后数据同步

### 页面结构

```
/price-audit
├── 统计卡片区域
│   ├── 总记录数
│   ├── 已解决数量
│   ├── 高优先级数量
│   └── 已升级数量
├── 搜索和筛选区域
│   ├── 搜索框
│   └── 筛选面板
│       ├── 审核状态筛选
│       ├── 错误类型筛选
│       ├── 优先级筛选
│       └── 解决状态筛选
├── 批量操作区域
│   ├── 已选择记录数量
│   ├── 批量批准按钮
│   ├── 批量拒绝按钮
│   └── 取消选择按钮
├── 审核记录表格
│   ├── 选择框列
│   ├── 审核ID列
│   ├── 工艺路线实例ID列
│   ├── 错误类型列
│   ├── 价格信息列（原价格、报告价格、更正价格）
│   ├── 财务影响列
│   ├── 优先级列
│   ├── 状态列
│   ├── 升级级别列
│   ├── 报告时间列
│   └── 操作列（批准、拒绝、查看）
├── 分页控件
└── 审核记录详情对话框
    ├── 状态概览卡片
    ├── 价格信息详情
    ├── 时间轴信息
    └── 审核操作区域
        ├── 操作原因输入
        ├── 更正价格输入
        ├── 解决方案输入
        ├── 操作按钮（批准、拒绝、价格更正、更新记录）
        ├── 优先级设置
        └── 升级功能
```

### API集成

系统集成了以下价格审核相关的API：

1. **搜索审核记录** - `searchAuditRecordsApiV1PriceAuditSearchPost`
2. **执行审核操作** - `performAuditActionApiV1PriceAuditActionPost`
3. **批量审核操作** - `bulkAuditActionApiV1PriceAuditBulkActionPost`
4. **更新审核记录** - `updateAuditRecordApiV1PriceAuditRecordIdPut`
5. **设置优先级** - `setPriorityApiV1PriceAuditSetPriorityPost`
6. **升级审核** - `escalateAuditApiV1PriceAuditEscalatePost`

### 数据类型

- `CraftRoutePriceAuditListDto` - 审核记录列表项
- `CraftRoutePriceAuditSearchDto` - 搜索参数
- `PriceAuditAction` - 审核动作枚举
- `PriceErrorType` - 价格错误类型枚举

### 导航

在应用的侧边栏"订单管理"组中添加了"价格审核"菜单项，路由为 `/price-audit`。

### 组件文件

- `src/pages/PriceAuditPage.tsx` - 主页面组件
- `src/components/AuditRecordDetailDialog.tsx` - 审核记录详情对话框
- `src/lib/dateUtils.ts` - 日期格式化工具函数

### 样式特点

- 现代化UI设计，使用Tailwind CSS
- 响应式布局，支持多种屏幕尺寸
- 直观的状态标记和优先级显示
- 统一的色彩方案和交互设计

### 用户体验

- 实时数据更新和状态同步
- 友好的错误提示和成功反馈
- 便捷的批量操作和快速操作
- 详细的审核记录查看和编辑功能
- 高效的搜索和筛选功能

## 开发环境

确保已安装以下依赖：
- React 18+
- TypeScript
- Vite
- Tailwind CSS
- TanStack Query
- Lucide React Icons

## 运行项目

```bash
npm run dev
```

访问 `http://localhost:5173/price-audit` 查看价格审核页面。
