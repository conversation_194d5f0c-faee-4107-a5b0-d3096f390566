# 扫非系统 API 集成

这个项目使用 `@hey-api/openapi-ts` 自动生成 TypeScript API 客户端和 TanStack Query hooks。

## 文件结构

```
src/
├── services/                    # 自动生成的API服务
│   ├── @tanstack/
│   │   └── react-query.gen.ts  # TanStack Query hooks
│   ├── client.gen.ts           # HTTP客户端配置
│   ├── sdk.gen.ts              # SDK函数
│   ├── types.gen.ts            # TypeScript类型定义
│   ├── index.ts                # 主入口文件
│   └── api.ts                  # 自定义API配置
├── providers/
│   └── QueryProvider.tsx      # React Query Provider
├── lib/
│   └── queryClient.ts         # Query Client配置
└── pages/
    ├── LoginPage.tsx          # 使用登录API的示例
    └── DashboardPage.tsx     # 使用查询API的示例
```

## 主要特性

### 1. 自动生成的API客户端
- **类型安全**: 从OpenAPI规范自动生成TypeScript类型
- **SDK函数**: 每个API端点都有对应的函数
- **TanStack Query集成**: 自动生成query options和mutation hooks

### 2. 生成的hooks示例

#### Query Hooks (GET请求)
```typescript
// 获取用户信息
const { data, isLoading, error } = useQuery({
  ...getApiV1UsersMeOptions(),
});

// 获取可用工厂
const { data: factories } = useQuery({
  ...getApiV1SessionAvailableFactoriesOptions(),
});
```

#### Mutation Hooks (POST/PUT/DELETE请求)
```typescript
// 登录
const loginMutation = useMutation({
  ...postApiV1AuthLoginMutation(),
  onSuccess: (data) => {
    console.log('登录成功:', data);
    // 处理成功逻辑
  },
  onError: (error) => {
    console.error('登录失败:', error);
    // 处理错误逻辑
  },
});

// 调用登录
loginMutation.mutate({
  body: {
    phone: '1234567890',
    password: 'password123',
  },
});
```

### 3. 可用的API端点

#### 认证相关
- `postApiV1AuthLogin` - 用户登录
- `postApiV1AuthRegister` - 用户注册
- `postApiV1AuthPhoneLoginPhonePassword` - 手机密码登录
- `postApiV1AuthPhoneLoginPhoneSms` - 手机短信登录

#### 工厂管理
- `postApiV1FactoryManagementJoinRequest` - 申请加入工厂
- `postApiV1FactoryManagementApproveRequest` - 审批加入申请
- `getApiV1FactoryManagementPendingRequests` - 获取待审批申请
- `getApiV1FactoryManagementMembers` - 获取工厂成员

#### 会话管理
- `postApiV1SessionSwitchFactory` - 切换工厂
- `getApiV1SessionAvailableFactories` - 获取可用工厂
- `getApiV1UsersMe` - 获取当前用户信息

## 使用方法

### 1. 生成API客户端
```bash
npm run generate-api
```

### 2. 在组件中使用
```typescript
import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  getApiV1UsersMeOptions,
  postApiV1AuthLoginMutation 
} from '@/services/@tanstack/react-query.gen';

function MyComponent() {
  // 查询示例
  const { data: user } = useQuery({
    ...getApiV1UsersMeOptions(),
  });

  // 变更示例
  const loginMutation = useMutation({
    ...postApiV1AuthLoginMutation(),
    onSuccess: (data) => {
      // 成功处理
    },
  });

  return (
    <div>
      {user && <p>用户: {JSON.stringify(user)}</p>}
      <button onClick={() => loginMutation.mutate({ body: { phone: '', password: '' } })}>
        登录
      </button>
    </div>
  );
}
```

### 3. 配置API基础URL
在 `.env` 文件中设置:
```
VITE_API_BASE_URL=http://localhost:8000
```

## 开发工具

### React Query DevTools
开发模式下会自动显示React Query开发工具，可以查看:
- 查询状态
- 缓存数据
- 网络请求
- 重试逻辑

### 自动重新生成
如果OpenAPI规范文件发生变化，可以使用以下命令自动重新生成:
```bash
npm run watch-api  # 需要安装nodemon
```

## 错误处理

项目配置了智能的错误重试逻辑:
- **401/403错误**: 不重试（需要重新认证）
- **4xx客户端错误**: 不重试
- **5xx服务器错误**: 重试最多3次
- **网络错误**: 重试最多3次

## 认证

API客户端会自动从localStorage读取`access_token`并添加到请求头中:
```typescript
auth: () => {
  const token = localStorage.getItem('access_token');
  return token ? `Bearer ${token}` : undefined;
}
```

登录成功后记得将token保存到localStorage中。
